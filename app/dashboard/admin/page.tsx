import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import AdminDashboard from '@/components/dashboards/AdminDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Admin Dashboard | KaziSync',
  description: 'Company Admin Dashboard for KaziSync',
};

export default function AdminDashboardPage() {
  return (
    <ProtectedRoute allowedRoles={['admin', 'super-admin']}>
      <DashboardLayout>
        <AdminDashboard />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
