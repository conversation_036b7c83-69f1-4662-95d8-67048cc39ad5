import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ManagerDashboard from '@/components/dashboards/ManagerDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Manager Dashboard | KaziSync',
  description: 'Manager Dashboard for KaziSync',
};

export default function ManagerDashboardPage() {
  return (
    <ProtectedRoute allowedRoles={['manager', 'hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <ManagerDashboard />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
