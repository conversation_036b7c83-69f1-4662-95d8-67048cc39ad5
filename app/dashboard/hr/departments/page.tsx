import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import DepartmentsContent from '@/components/hr/DepartmentsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function DepartmentsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <DepartmentsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
