import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeAccountManagement from '@/components/hr/EmployeeAccountManagement';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Employee Account Management | KaziSync',
  description: 'Create and manage employee login accounts',
};

export default function EmployeeAccountManagementPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <EmployeeAccountManagement />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
