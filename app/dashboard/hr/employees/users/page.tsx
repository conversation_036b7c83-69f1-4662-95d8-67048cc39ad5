import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import CompanyUserManagement from '@/components/hr/CompanyUserManagement';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Company User Management | KaziSync',
  description: 'View and manage employee login accounts',
};

export default function CompanyUserManagementPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <CompanyUserManagement />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
