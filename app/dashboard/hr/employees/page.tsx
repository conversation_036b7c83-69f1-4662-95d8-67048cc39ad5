import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeesContent from '@/components/hr/EmployeesContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function EmployeesPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <EmployeesContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
