import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import AnnouncementsContent from '@/components/hr/AnnouncementsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Announcements Management | KaziSync',
  description: 'Manage company announcements and communications',
};

export default function AnnouncementsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <AnnouncementsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
