import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import SubscriptionContent from '@/components/hr/SubscriptionContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Subscription & Billing | KaziSync',
  description: 'Manage your subscription plan and billing information',
};

export default function SubscriptionPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <SubscriptionContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
