import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import PayrollContent from '@/components/hr/PayrollContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Payroll Management | KaziSync',
  description: 'Manage employee salaries and payroll with scenario-based calculations',
};

export default function PayrollPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <PayrollContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
