import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import AttendanceDashboard from '@/components/attendance/AttendanceDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Attendance Statistics | KaziSync',
  description: 'Interactive attendance statistics with visualizations and trends',
};

export default function AttendanceDashboardPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin', 'manager']}>
      <DashboardLayout>
        <AttendanceDashboard />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
