import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeAttendanceStatistics from '@/components/attendance/EmployeeAttendanceStatistics';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

interface EmployeeAttendancePageProps {
  params: Promise<{
    employeeId: string;
  }>;
}

export const metadata = {
  title: 'Employee Attendance Statistics | KaziSync',
  description: 'View individual employee attendance statistics with AI insights',
};

export default async function EmployeeAttendancePage({ params }: EmployeeAttendancePageProps) {
  // Await the params since they're now a Promise in Next.js 15
  const { employeeId } = await params;

  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin', 'manager']}>
      <DashboardLayout>
        <EmployeeAttendanceStatistics initialEmployeeId={employeeId} />
      </DashboardLayout>
    </ProtectedRoute>
  );
}