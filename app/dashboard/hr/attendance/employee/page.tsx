import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeAttendanceStatistics from '@/components/attendance/EmployeeAttendanceStatistics';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Employee Attendance Statistics | KaziSync',
  description: 'View individual employee attendance statistics with AI insights',
};

export default function EmployeeAttendanceStatisticsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin', 'manager']}>
      <DashboardLayout>
        <EmployeeAttendanceStatistics />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
