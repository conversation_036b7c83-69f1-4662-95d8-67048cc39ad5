import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import DailyAttendanceContent from '@/components/attendance/DailyAttendanceContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Daily Attendance | KaziSync',
  description: 'View daily attendance reports',
};

export default function DailyAttendancePage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin', 'manager']}>
      <DashboardLayout>
        <DailyAttendanceContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
