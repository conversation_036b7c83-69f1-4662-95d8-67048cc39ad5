import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import SettingsContent from '@/components/hr/SettingsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function SettingsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <SettingsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
