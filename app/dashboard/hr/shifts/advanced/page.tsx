import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import AdvancedShiftManagement from '@/components/advanced-shift/AdvancedShiftManagement';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Advanced Shift Management | KaziSync',
  description: 'Create sophisticated scheduling templates, generate schedules automatically, and manage advanced shift assignments',
};

export default function AdvancedShiftPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <AdvancedShiftManagement />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
