import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ShiftsContent from '@/components/hr/ShiftsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function ShiftsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <ShiftsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
