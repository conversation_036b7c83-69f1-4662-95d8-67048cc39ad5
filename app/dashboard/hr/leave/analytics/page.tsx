import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import LeaveAnalytics from '@/components/hr/LeaveAnalytics';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Leave Analytics & Reports | KaziSync',
  description: 'Comprehensive leave analytics and reporting dashboard',
};

export default function LeaveAnalyticsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin', 'manager']}>
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Leave Analytics & Reports</h1>
          </div>
          
          {/* Breadcrumbs */}
          <div className="text-sm text-gray-600">
            <a href="/dashboard/hr" className="hover:text-blue-600">Dashboard</a>
            <span className="mx-2">/</span>
            <a href="/dashboard/hr/leave" className="hover:text-blue-600">Leave Management</a>
            <span className="mx-2">/</span>
            <span className="text-gray-900">Analytics & Reports</span>
          </div>

          {/* Info Notice */}
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  <strong>Analytics Dashboard:</strong> Get comprehensive insights into leave patterns, trends, and statistics. 
                  Use the filters to analyze specific time periods and generate detailed reports for decision making.
                </p>
              </div>
            </div>
          </div>

          <LeaveAnalytics />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
