import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import LeaveManagementContent from '@/components/hr/LeaveManagementContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function LeaveManagementPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <LeaveManagementContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
