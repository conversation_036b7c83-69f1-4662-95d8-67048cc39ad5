import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import LeaveBalanceDoctor from '@/components/hr/LeaveBalanceDoctor';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Leave Balance Doctor | KaziSync',
  description: 'Diagnose and fix leave balance inconsistencies',
};

export default function LeaveBalanceDoctorPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Leave Balance Doctor</h1>
          </div>
          
          {/* Breadcrumbs */}
          <div className="text-sm text-gray-600">
            <a href="/dashboard/hr" className="hover:text-blue-600">Dashboard</a>
            <span className="mx-2">/</span>
            <a href="/dashboard/hr/leave" className="hover:text-blue-600">Leave Management</a>
            <span className="mx-2">/</span>
            <span className="text-gray-900">Balance Doctor</span>
          </div>

          {/* Warning Notice */}
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>Important:</strong> The Leave Balance Doctor performs administrative operations on employee leave data. 
                  Always run in dry-run mode first to preview changes before applying them.
                </p>
              </div>
            </div>
          </div>

          <LeaveBalanceDoctor />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
