import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import LeaveBalancesContent from '@/components/hr/LeaveBalancesContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Leave Balances | KaziSync',
  description: 'Manage employee leave balances',
};

export default function LeaveBalancesPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Leave Balances Management</h1>
          </div>
          
          {/* Breadcrumbs */}
          <div className="text-sm text-gray-600">
            <a href="/dashboard/hr" className="hover:text-blue-600">Dashboard</a>
            <span className="mx-2">/</span>
            <a href="/dashboard/hr/leave" className="hover:text-blue-600">Leave Management</a>
            <span className="mx-2">/</span>
            <span className="text-gray-900">Leave Balances</span>
          </div>

          <LeaveBalancesContent />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
