import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ReportsContent from '@/components/hr/ReportsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function ReportsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <ReportsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
