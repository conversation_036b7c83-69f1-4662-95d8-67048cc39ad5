import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EventsContent from '@/components/hr/EventsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function EventsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <EventsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
