import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import AIAnalyticsDashboard from '@/components/ai-analytics/AIAnalyticsDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'AI Analytics | KaziSync',
  description: 'AI-powered attendance analytics and insights',
};

export default function AIAnalyticsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <AIAnalyticsDashboard />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
