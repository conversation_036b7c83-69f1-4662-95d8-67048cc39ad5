import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import HRDashboard from '@/components/dashboards/HRDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'HR Dashboard | KaziSync',
  description: 'HR Dashboard for KaziSync',
};

export default function HRDashboardPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <HRDashboard />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
