"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentUser, getDashboardPathByRole } from '@/lib/auth';

export default function DashboardPage() {
  const router = useRouter();

  useEffect(() => {
    // Immediately start prefetching all possible dashboard routes
    const dashboardRoutes = [
      '/dashboard/super-admin',
      '/dashboard/admin',
      '/dashboard/hr',
      '/dashboard/manager',
      '/dashboard/employee'
    ];

    dashboardRoutes.forEach(route => {
      router.prefetch(route);
    });

    // Get the current user
    const user = getCurrentUser();

    // Use setTimeout to ensure the component has time to mount
    const redirectTimer = setTimeout(() => {
      if (user) {
        // Redirect to the appropriate dashboard based on the user's role
        const dashboardPath = getDashboardPathByRole(user.role);

        // Use replace instead of push to avoid adding to history stack
        router.replace(dashboardPath);
      } else {
        // If not authenticated, redirect to login page
        router.replace('/login');
      }
    }, 100); // Small delay to ensure component is mounted

    // Cleanup function
    return () => {
      clearTimeout(redirectTimer);
    };
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="text-center">
        <div className="animate-spin h-12 w-12 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
        <p className="text-secondary-dark">Redirecting to your dashboard...</p>
        <p className="text-sm text-gray-500 mt-2">
          If you are not redirected automatically,
          <button
            onClick={() => {
              const user = getCurrentUser();
              if (user) {
                router.replace(getDashboardPathByRole(user.role));
              } else {
                router.replace('/login');
              }
            }}
            className="text-primary ml-1 hover:underline"
          >
            click here
          </button>
        </p>
      </div>
    </div>
  );
}
