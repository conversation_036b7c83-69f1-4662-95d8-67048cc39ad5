import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import CompaniesContent from '@/components/super-admin/CompaniesContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Companies | KaziSync',
  description: 'Manage companies and their devices',
};

export default function CompaniesPage() {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <CompaniesContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
