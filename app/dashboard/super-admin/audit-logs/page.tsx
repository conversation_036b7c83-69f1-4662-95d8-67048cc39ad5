import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ComingSoon from '@/components/ui/ComingSoon';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Audit Logs | KaziSync',
  description: 'View system audit logs and activity tracking',
};

export default function AuditLogsPage() {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <ComingSoon
          title="Audit Logs"
          description="Track system activities, user actions, and security events across the platform."
          icon="📝"
          backLink="/dashboard/super-admin"
          backLinkText="Back to Super Admin Dashboard"
        />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
