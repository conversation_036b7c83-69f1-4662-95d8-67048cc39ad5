import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import SubscriptionManagementContent from '@/components/super-admin/SubscriptionManagementContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Subscription Management | KaziSync',
  description: 'Manage subscription plans and billing configurations',
};

export default function SubscriptionsPage() {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <SubscriptionManagementContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
