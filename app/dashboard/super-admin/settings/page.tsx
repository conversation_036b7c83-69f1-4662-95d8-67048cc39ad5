import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ComingSoon from '@/components/ui/ComingSoon';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'System Settings | KaziSync',
  description: 'Configure global system settings and preferences',
};

export default function SystemSettingsPage() {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <ComingSoon
          title="System Settings"
          description="Configure global system settings, security policies, and platform preferences."
          icon="⚙️"
          backLink="/dashboard/super-admin"
          backLinkText="Back to Super Admin Dashboard"
        />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
