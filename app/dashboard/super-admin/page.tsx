import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import SuperAdminDashboard from '@/components/dashboards/SuperAdminDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Super Admin Dashboard | KaziSync',
  description: 'Super Admin Dashboard for KaziSync',
};

export default function SuperAdminDashboardPage() {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <SuperAdminDashboard />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
