import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import CountriesContent from '@/components/super-admin/CountriesContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Countries | KaziSync',
  description: 'Manage countries and their payroll configurations',
};

export default function CountriesPage() {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <CountriesContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
