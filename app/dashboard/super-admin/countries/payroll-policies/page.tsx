import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import PayrollPolicyTypesContent from '@/components/super-admin/PayrollPolicyTypesContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Payroll Policy Types | KaziSync',
  description: 'Manage global payroll policy types',
};

export default function PayrollPolicyTypesPage() {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <PayrollPolicyTypesContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
