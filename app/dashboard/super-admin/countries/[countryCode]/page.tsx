import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import CountryDetailContent from '@/components/super-admin/CountryDetailContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

interface CountryDetailPageProps {
  params: Promise<{
    countryCode: string;
  }>;
}

export async function generateMetadata({ params }: CountryDetailPageProps) {
  const resolvedParams = await params;
  return {
    title: `${resolvedParams.countryCode.toUpperCase()} Payroll Management | KaziSync`,
    description: `Manage payroll policies, tax settings, and employee types for ${resolvedParams.countryCode.toUpperCase()}`,
  };
}

export default async function CountryDetailPage({ params }: CountryDetailPageProps) {
  const resolvedParams = await params;

  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <CountryDetailContent countryCode={resolvedParams.countryCode} />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
