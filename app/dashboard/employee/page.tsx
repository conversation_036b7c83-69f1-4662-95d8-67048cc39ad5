import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeDashboard from '@/components/dashboards/EmployeeDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Employee Dashboard | KaziSync',
  description: 'Employee Dashboard for KaziSync',
};

export default function EmployeeDashboardPage() {
  return (
    <ProtectedRoute allowedRoles={['employee', 'manager', 'hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <EmployeeDashboard />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
