import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeAnnouncementsContent from '@/components/employee/EmployeeAnnouncementsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Announcements | KaziSync',
  description: 'View company announcements and important communications',
};

export default function EmployeeAnnouncementsPage() {
  return (
    <ProtectedRoute allowedRoles={['employee', 'manager', 'hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <EmployeeAnnouncementsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
