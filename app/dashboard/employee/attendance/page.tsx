import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeAttendance from '@/components/employee/EmployeeAttendance';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'My Attendance | KaziSync',
  description: 'Employee Attendance History for KaziSync',
};

export default function EmployeeAttendancePage() {
  return (
    <ProtectedRoute allowedRoles={['employee', 'manager', 'hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <EmployeeAttendance />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
