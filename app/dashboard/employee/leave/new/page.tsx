import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import LeaveRequestForm from '@/components/employee/LeaveRequestForm';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Request Leave | KaziSync',
  description: 'Submit a new leave request',
};

export default function NewLeaveRequestPage() {
  return (
    <ProtectedRoute allowedRoles={['employee', 'manager', 'hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <LeaveRequestForm />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
