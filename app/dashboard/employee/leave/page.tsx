import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeLeave from '@/components/employee/EmployeeLeave';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'My Leave | KaziSync',
  description: 'Employee Leave Management for KaziSync',
};

export default function EmployeeLeavePage() {
  return (
    <ProtectedRoute allowedRoles={['employee', 'manager', 'hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <EmployeeLeave />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
