import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import EmployeeShiftsContent from '@/components/employee/EmployeeShiftsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'My Shifts | KaziSync',
  description: 'View your assigned shifts',
};

export default function EmployeeShiftsPage() {
  return (
    <ProtectedRoute allowedRoles={['employee', 'manager', 'hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <EmployeeShiftsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
