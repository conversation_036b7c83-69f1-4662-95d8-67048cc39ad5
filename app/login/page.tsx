import React, { Suspense } from 'react';
import Link from 'next/link';
import LoginForm from '@/components/auth/LoginForm';
import AuthLayout from '@/components/layouts/AuthLayout';

export const metadata = {
  title: 'Login | KaziSync',
  description: 'Login to your KaziSync account',
};

export default function LoginPage() {
  return (
    <AuthLayout>
      <div className="w-full max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-secondary-dark mb-2">Welcome Back</h1>
          <p className="text-secondary">Sign in to your KaziSync account</p>
        </div>

        <Suspense fallback={<div className="text-center py-4">Loading login form...</div>}>
          <LoginForm />
        </Suspense>

        <div className="mt-6 text-center">
          <p className="text-secondary">
            Don&apos;t have an account?{' '}
            <Link href="/signup" className="text-primary hover:text-primary-dark font-medium">
              Sign up
            </Link>
          </p>
          <Link href="/forgot-password" className="text-primary hover:text-primary-dark text-sm mt-2 inline-block">
            Forgot your password?
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
