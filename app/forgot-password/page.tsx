import React from 'react';
import Link from 'next/link';
import ForgotPasswordForm from '@/components/auth/ForgotPasswordForm';
import AuthLayout from '@/components/layouts/AuthLayout';

export const metadata = {
  title: 'Forgot Password | KaziSync',
  description: 'Reset your KaziSync password',
};

export default function ForgotPasswordPage() {
  return (
    <AuthLayout>
      <div className="w-full max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-secondary-dark mb-2">Forgot Password</h1>
          <p className="text-secondary">Enter your email to receive a password reset link</p>
        </div>
        
        <ForgotPasswordForm />
        
        <div className="mt-6 text-center">
          <p className="text-secondary">
            Remember your password?{' '}
            <Link href="/login" className="text-primary hover:text-primary-dark font-medium">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </AuthLayout>
  );
}
