import React from 'react';
import Link from 'next/link';
import SignupForm from '@/components/auth/SignupForm';
import AuthLayout from '@/components/layouts/AuthLayout';

export const metadata = {
  title: 'Sign Up | KaziSync',
  description: 'Create a new KaziSync account',
};

export default function SignupPage() {
  return (
    <AuthLayout>
      <div className="w-full max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-secondary-dark mb-2">Create an Account</h1>
          <p className="text-secondary">Join KaziSync to manage your workforce efficiently</p>
        </div>

        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md text-sm mb-6">
          <p><strong>Note:</strong> After registration, you'll be redirected to the login page. Once logged in, you can add your company details from the dashboard.</p>
        </div>

        <SignupForm />

        <div className="mt-6 text-center">
          <p className="text-secondary">
            Already have an account?{' '}
            <Link href="/login" className="text-primary hover:text-primary-dark font-medium">
              Sign in
            </Link>
          </p>
          <p className="text-xs text-secondary mt-4">
            By signing up, you agree to our{' '}
            <Link href="/terms" className="text-primary hover:text-primary-dark">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" className="text-primary hover:text-primary-dark">
              Privacy Policy
            </Link>
          </p>
        </div>
      </div>
    </AuthLayout>
  );
}
