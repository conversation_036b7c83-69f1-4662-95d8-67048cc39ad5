import React from 'react';
import Link from 'next/link';
import ResetPasswordForm from '@/components/auth/ResetPasswordForm';
import AuthLayout from '@/components/layouts/AuthLayout';

export const metadata = {
  title: 'Reset Password | KaziSync',
  description: 'Set a new password for your KaziSync account',
};

export default function ResetPasswordPage() {
  return (
    <AuthLayout>
      <div className="w-full max-w-md mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-secondary-dark mb-2">Reset Password</h1>
          <p className="text-secondary">Create a new password for your account</p>
        </div>
        
        <ResetPasswordForm />
        
        <div className="mt-6 text-center">
          <p className="text-secondary">
            Remember your password?{' '}
            <Link href="/login" className="text-primary hover:text-primary-dark font-medium">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </AuthLayout>
  );
}
