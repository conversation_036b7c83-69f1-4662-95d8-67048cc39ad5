import React from 'react';
import { Metadata } from 'next';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import About from '@/components/About';
import Features from '@/components/Features';
import WhyKaziSync from '@/components/WhyKaziSync';
import Benefits from '@/components/Benefits';
// import Testimonials from '@/components/Testimonials'; // Commented out - no testimonials yet
import Pricing from '@/components/Pricing';
import FeatureComparison from '@/components/FeatureComparison';
// import Demo from '@/components/Demo'; // Commented out - demo section removed
import CTA from '@/components/CTA';
import Contact from '@/components/Contact';
import Footer from '@/components/Footer';

export const metadata: Metadata = {
  title: 'KaziSync – Work in Sync | Modern HRMS for Teams of All Sizes',
  description: 'The modern HRMS built for teams of all sizes. From payroll to performance, KaziSync helps you manage people, not paperwork. Cloud-based HR management with real-time attendance, payroll automation, and IoT integration.',
  alternates: {
    canonical: 'https://www.kazisync.com',
  },
};

export default function Home() {
  return (
    <>
      <Header />
      <main role="main">
        <Hero />
        <About />
        <Features />
        <WhyKaziSync />
        <Benefits />
        {/* <Testimonials /> */} {/* Commented out - no testimonials yet */}
        <Pricing />
        <FeatureComparison />
        {/* <Demo /> */} {/* Commented out - demo section removed */}
        <CTA />
        <Contact />
      </main>
      <Footer />
    </>
  );
}
