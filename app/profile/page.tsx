import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ProfileContent from '@/components/profile/ProfileContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function ProfilePage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin', 'manager', 'employee']}>
      <DashboardLayout>
        <ProfileContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
