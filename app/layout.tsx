import type { Metadata } from 'next';
import '../styles/globals.css';
import { AuthProvider } from '@/contexts/AuthContext';

export const metadata: Metadata = {
  metadataBase: new URL('https://www.kazisync.com'),
  title: 'KaziSync – Work in Sync | Modern HRMS for Teams of All Sizes',
  description: 'The modern HRMS built for teams of all sizes. From payroll to performance, KaziSync helps you manage people, not paperwork. Cloud-based HR management with real-time attendance, payroll automation, and IoT integration.',
  keywords: [
    'HRMS',
    'Human Resources Management System',
    'payroll management',
    'attendance tracking',
    'employee management',
    'leave management',
    'performance reviews',
    'HR software',
    'cloud-based HRMS',
    'biometric attendance',
    'IoT integration',
    'workforce management',
    'HR analytics',
    'employee onboarding',
    'time tracking',
    'HR dashboard',
    'QuickBooks integration',
    'salary advance management',
    'document management'
  ],
  authors: [{ name: 'KaziSync Team' }],
  creator: '<PERSON><PERSON><PERSON><PERSON>',
  publisher: 'KaziSync',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://www.kazisync.com',
    siteName: 'KaziSync',
    title: 'KaziSync – Work in Sync | Modern HRMS for Teams of All Sizes',
    description: 'The modern HRMS built for teams of all sizes. From payroll to performance, KaziSync helps you manage people, not paperwork. Cloud-based HR management with real-time attendance, payroll automation, and IoT integration.',
    images: [
      {
        url: '/images/dashboard-preview.png',
        width: 1200,
        height: 675,
        alt: 'KaziSync Dashboard Preview - Modern HRMS Interface',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'KaziSync – Work in Sync | Modern HRMS for Teams of All Sizes',
    description: 'The modern HRMS built for teams of all sizes. From payroll to performance, KaziSync helps you manage people, not paperwork.',
    images: ['/images/dashboard-preview.png'],
    creator: '@kazisync',
    site: '@kazisync',
  },
  alternates: {
    canonical: 'https://www.kazisync.com',
  },
  category: 'Business Software',
  classification: 'Human Resources Management System',
  other: {
    'application-name': 'KaziSync',
    'apple-mobile-web-app-title': 'KaziSync',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'format-detection': 'telephone=no',
    'mobile-web-app-capable': 'yes',
    'msapplication-TileColor': '#007BFF',
    'msapplication-tap-highlight': 'no',
    'theme-color': '#007BFF',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Additional SEO Meta Tags */}
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="theme-color" content="#007BFF" />
        <meta name="msapplication-TileColor" content="#007BFF" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="KaziSync" />
        <meta name="application-name" content="KaziSync" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-tap-highlight" content="no" />

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Preconnect for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "KaziSync",
              "applicationCategory": "BusinessApplication",
              "applicationSubCategory": "Human Resources Management System",
              "operatingSystem": "Web Browser",
              "description": "The modern HRMS built for teams of all sizes. From payroll to performance, KaziSync helps you manage people, not paperwork.",
              "url": "https://www.kazisync.com",
              "author": {
                "@type": "Organization",
                "name": "KaziSync",
                "url": "https://www.kazisync.com"
              },
              "offers": [
                {
                  "@type": "Offer",
                  "name": "Starter Plan",
                  "price": "49",
                  "priceCurrency": "USD",
                  "priceSpecification": {
                    "@type": "UnitPriceSpecification",
                    "price": "49",
                    "priceCurrency": "USD",
                    "unitText": "monthly"
                  }
                },
                {
                  "@type": "Offer",
                  "name": "Growth Plan",
                  "price": "99",
                  "priceCurrency": "USD",
                  "priceSpecification": {
                    "@type": "UnitPriceSpecification",
                    "price": "99",
                    "priceCurrency": "USD",
                    "unitText": "monthly"
                  }
                },
                {
                  "@type": "Offer",
                  "name": "Pro Plan",
                  "price": "199",
                  "priceCurrency": "USD",
                  "priceSpecification": {
                    "@type": "UnitPriceSpecification",
                    "price": "199",
                    "priceCurrency": "USD",
                    "unitText": "monthly"
                  }
                }
              ],
              "featureList": [
                "Real-Time Attendance Tracking",
                "Payroll Management",
                "Leave & Time-Off Management",
                "Performance Reviews",
                "Employee Onboarding",
                "Loan & Advance Management",
                "Centralized Document Storage",
                "HR Analytics Dashboard"
              ]
            })
          }}
        />
      </head>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
