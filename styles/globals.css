@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply font-sans;
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-background text-secondary-dark;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-bold;
  }
  
  h1 {
    @apply text-4xl md:text-5xl;
  }
  
  h2 {
    @apply text-3xl md:text-4xl;
  }
  
  h3 {
    @apply text-2xl md:text-3xl;
  }
  
  h4 {
    @apply text-xl md:text-2xl;
  }
  
  h5 {
    @apply text-lg md:text-xl;
  }
  
  h6 {
    @apply text-base md:text-lg;
  }
  
  a {
    @apply text-primary hover:text-primary-dark transition-colors duration-200;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary text-white hover:bg-primary-dark hover:text-white focus:ring-primary;
  }
  
  .btn-secondary {
    @apply btn bg-secondary text-white hover:bg-secondary-dark hover:text-white focus:ring-secondary;
  }
  
  .btn-outline {
    @apply btn border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary;
  }
  
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section {
    @apply py-12 md:py-16 lg:py-20;
  }
  
  .card {
    @apply bg-white rounded-xl border border-gray-200 overflow-hidden;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
