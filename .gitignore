# dependencies
node_modules
node-v22.16.0-linux-x64
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
# Keep production env file for deployment
!.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# IDE files
.idea/
.vscode/
*.swp
*.swo
