/**
 * Format a date string based on the selected period
 * @param dateString ISO date string (YYYY-MM-DD)
 * @param period The selected period (weekly, monthly, annual, custom)
 * @returns Formatted date string
 */
export const formatDate = (dateString: string, period: 'weekly' | 'monthly' | 'annual' | 'custom'): string => {
  const date = new Date(dateString);
  
  // Format based on period
  switch (period) {
    case 'weekly':
      // Format as "MMM DD" (e.g., "Jan 15")
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      
    case 'monthly':
      // Format as "DD" (e.g., "15")
      return date.toLocaleDateString('en-US', { day: 'numeric' });
      
    case 'annual':
      // Format as "MMM" (e.g., "Jan")
      return date.toLocaleDateString('en-US', { month: 'short' });
      
    case 'custom':
      // Format as "MMM DD" (e.g., "Jan 15")
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      
    default:
      return dateString;
  }
};

/**
 * Format a date range for display
 * @param startDate Start date
 * @param endDate End date
 * @returns Formatted date range string
 */
export const formatDateRange = (startDate: Date, endDate: Date): string => {
  const start = startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  const end = endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  
  return `${start} - ${end}`;
};

/**
 * Get the first day of the current week (Monday)
 * @returns Date object for Monday of the current week
 */
export const getFirstDayOfWeek = (): Date => {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const diff = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Adjust for Monday as first day
  
  const monday = new Date(today);
  monday.setDate(today.getDate() - diff);
  monday.setHours(0, 0, 0, 0);
  
  return monday;
};

/**
 * Get the last day of the current week (Sunday)
 * @returns Date object for Sunday of the current week
 */
export const getLastDayOfWeek = (): Date => {
  const firstDay = getFirstDayOfWeek();
  const lastDay = new Date(firstDay);
  lastDay.setDate(firstDay.getDate() + 6);
  lastDay.setHours(23, 59, 59, 999);
  
  return lastDay;
};

/**
 * Get the first day of the current month
 * @returns Date object for the first day of the current month
 */
export const getFirstDayOfMonth = (): Date => {
  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth(), 1);
};

/**
 * Get the last day of the current month
 * @returns Date object for the last day of the current month
 */
export const getLastDayOfMonth = (): Date => {
  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth() + 1, 0);
};

/**
 * Get the first day of the current year
 * @returns Date object for January 1st of the current year
 */
export const getFirstDayOfYear = (): Date => {
  const today = new Date();
  return new Date(today.getFullYear(), 0, 1);
};

/**
 * Get the last day of the current year
 * @returns Date object for December 31st of the current year
 */
export const getLastDayOfYear = (): Date => {
  const today = new Date();
  return new Date(today.getFullYear(), 11, 31);
};
