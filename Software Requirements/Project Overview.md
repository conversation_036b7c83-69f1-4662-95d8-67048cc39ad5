# Project Overview

## 1. Introduction

### 1.1 Purpose
This Software Requirements Specification (SRS) describes the requirements for KaziSync, a multi-tenant web-based attendance management system for organizations to manage employee attendance, shifts, leaves, and biometric integration.

### 1.2 Scope
KaziSync is designed to support organizations by simplifying attendance tracking, leave requests, shift planning, and biometric device logging. The system supports different user roles (<PERSON> Admin, Company Admin, HR, Manager, and Employee) with role-based permissions. It supports both in-office and remote (GPS) attendance, shift customization, audit logging, and multi-location support.

### 1.3 Definitions, Acronyms, and Abbreviations

- SRS: Software Requirements Specification
- HR: Human Resources
- GPS: Global Positioning System
- API: Application Programming Interface
- UI: User Interface
- MVP: Minimum Viable Product