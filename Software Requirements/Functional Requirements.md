# Functional Requirements

## 3. Specific Requirements

### 3.1 Functional Requirements

#### FR-1. User Authentication
- Users must log in using email and password
- Password recovery option required
- Authentication must return a session token or JWT

#### FR-2. Role-Based Access
- Each role has restricted access to specific pages and actions
- Unauthorized actions must be blocked

#### FR-3. Employee Management
- HR/Admin can add/edit/delete employee records
- Assign department, location, and shift

#### FR-4. Clock In/Out
- Biometric or GPS-based clock in/out
- Handle missed clock-outs and notify users
- Prevent multiple clock-ins without clocking out

#### FR-5. Shift Management
- Create shift templates (e.g., day, night)
- Assign shifts individually or in bulk
- View shift calendar

#### FR-6. Leave Management
- Employees request leave through portal
- Manager receives and approves/rejects requests
- Leave types and balances configurable

#### FR-7. Attendance Correction
- Employees can request corrections
- HR/Manager approves or rejects with reason
- Audit log stores change history

#### FR-8. Notifications
- Alerts for missed clock-outs, leave decisions, upcoming shifts
- Email and/or SMS integrations

#### FR-9. Reports & Analytics
- Downloadable reports for attendance, shifts, and leave
- Visual dashboards (charts, graphs)

#### FR-10. Admin Dashboard
- View company metrics: attendance rate, shift adherence, leave trends
- Device activity overview

#### FR-11. Audit Logging
- All major actions logged with timestamp, user, and change made
- Viewable by Super Admin and Company Admin

#### FR-12. Subscription & Billing
- Companies can subscribe to monthly/yearly plans
- Payment integration for billing
- Plan change, cancellation, and invoice download supported

## Feature Details

### Feature 1: User Authentication

#### Description
Users must be able to log in securely using their email and password. A password recovery option should be available. The authentication process must return a session token or JWT for subsequent API calls.

#### Requirements List
- FR-1. User Authentication
    - Users must log in using email and password
    - Password recovery option required
    - Authentication must return a session token or JWT

### Feature 2: Role-Based Access

#### Description
The system must implement role-based access control, restricting access to specific pages and actions based on the user's role. Unauthorized actions must be blocked to ensure data security and prevent unauthorized modifications.

#### Requirements List
- FR-2. Role-Based Access
    - Each role has restricted access to specific pages and actions
    - Unauthorized actions must be blocked

### Feature 3: Employee Management

#### Description
HR and Admin roles must be able to add, edit, and delete employee records. They should also be able to assign employees to departments, locations, and shifts.

#### Requirements List
- FR-3. Employee Management
    - HR/Admin can add/edit/delete employee records
    - Assign department, location, and shift

### Feature 4: Clock In/Out

#### Description
The system must support biometric or GPS-based clock in/out functionality. It should handle missed clock-outs by notifying users and prevent multiple clock-ins without clocking out.

#### Requirements List
- FR-4. Clock In/Out
    - Biometric or GPS-based clock in/out
    - Handle missed clock-outs and notify users
    - Prevent multiple clock-ins without clocking out

### Feature 5: Shift Management

#### Description
The system must allow the creation of shift templates (e.g., day, night) and the assignment of shifts to employees individually or in bulk. A shift calendar should be available for viewing assigned shifts.

#### Requirements List
- FR-5. Shift Management
    - Create shift templates (e.g., day, night)
    - Assign shifts individually or in bulk
    - View shift calendar

### Feature 6: Leave Management

#### Description
Employees must be able to request leave through a portal. Managers should receive these requests and be able to approve or reject them. The system should allow configuration of leave types and balances.

#### Requirements List
- FR-6. Leave Management
    - Employees request leave through portal
    - Manager receives and approves/rejects requests
    - Leave types and balances configurable

### Feature 7: Attendance Correction

#### Description
Employees must be able to request corrections to their attendance records. HR or Managers should be able to approve or reject these requests, providing a reason for their decision. The system should maintain an audit log of all changes.

#### Requirements List
- FR-7. Attendance Correction
    - Employees can request corrections
    - HR/Manager approves or rejects with reason
    - Audit log stores change history

### Feature 8: Notifications

#### Description
The system must provide alerts for missed clock-outs, leave decisions, and upcoming shifts. Notifications should be delivered via email and/or SMS integrations.

#### Requirements List
- FR-8. Notifications
    - Alerts for missed clock-outs, leave decisions, upcoming shifts
    - Email and/or SMS integrations

### Feature 9: Reports & Analytics

#### Description
The system must provide downloadable reports for attendance, shifts, and leave. Visual dashboards with charts and graphs should also be available.

#### Requirements List
- FR-9. Reports & Analytics
    - Downloadable reports for attendance, shifts, and leave
    - Visual dashboards (charts, graphs)

### Feature 10: Admin Dashboard

#### Description
The admin dashboard must provide a view of company metrics, including attendance rate, shift adherence, and leave trends. It should also provide an overview of device activity.

#### Requirements List
- FR-10. Admin Dashboard
    - View company metrics: attendance rate, shift adherence, leave trends
    - Device activity overview

### Feature 11: Audit Logging

#### Description
The system must log all major actions with a timestamp, the user who performed the action, and the changes made. This log should be viewable by Super Admin and Company Admin roles.

#### Requirements List
- FR-11. Audit Logging
    - All major actions logged with timestamp, user, and change made
    - Viewable by Super Admin and Company Admin

### Feature 12: Subscription & Billing

#### Description
The system must allow companies to subscribe to monthly or yearly plans. Payment integration for billing should be included. The system should support plan changes, cancellation, and invoice downloads.

#### Requirements List
- FR-12. Subscription & Billing
    - Companies can subscribe to monthly/yearly plans
    - Payment integration for billing
    - Plan change, cancellation, and invoice download supported