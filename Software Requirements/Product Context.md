# Product Context

## 2. Overall Description

### 2.1 Product Perspective
KaziSync is a standalone SaaS web application accessible via desktop and mobile devices. It uses biometric integration and supports GPS-based clock-ins for remote workers. The system includes a centralized dashboard per user role.

### 2.2 Product Functions

- Secure login & user authentication
- Role-based dashboards and access control
- Employee management (add, assign, update)
- Shift management (templates, assignments)
- Biometric attendance logging (fingerprint/face recognition)
- Clock in/out tracking with GPS support
- Leave request & approval workflow
- Attendance correction submission and approval
- Notifications (email/SMS/onsite)
- Audit logs
- Reports & analytics
- Multi-tenant data separation
- Subscription management and billing

### 2.3 User Classes and Characteristics

- Super Admin: Manages all companies and subscriptions
- Company Admin: Manages company-specific data, settings, and users
- HR: Manages employee records, shifts, and attendance
- Manager: Manages assigned team attendance and leave requests
- Employee: Clocks in/out, views attendance, requests leave

### 2.4 Operating Environment

- Platform: Web browsers (Chrome, Firefox, Safari), optional mobile app
- Server: Ubuntu/Linux with Nginx and <PERSON><PERSON> (for deployment)
- Backend: Python (Django or Flask)
- Frontend: React or similar JS framework
- Database: PostgreSQL (primary), Redis (for caching)
- Third-party: Twilio/Postmark for SMS/email, Cloudinary for media storage

### 2.5 Design and Implementation Constraints

- Responsive design for mobile and desktop
- Secure data storage and GDPR compliance
- Timezone-aware shift and attendance handling
- Must support biometric device APIs (face/fingerprint)