# Open Questions & Decisions

## Questions Database

| Question | Status | Impact | Owner | Resolution | Decision Date |
|---|---|---|---|---|---|
| Which biometric devices should be supported? | Open | High | Product Owner |  |  |
| Should we use a native mobile app or a responsive web app? | Open | High | Development Team |  |  |
| What payment gateway should be integrated? | Open | Medium | Finance Team |  |  |

## Decision Log

| Decision | Rationale | Alternatives Considered | Impact | Date |
|---|---|---|---|---|
| Use PostgreSQL as the primary database | Scalability and reliability | MySQL, MongoDB | High | 2024-01-15 |
| Use React for the frontend | Component-based architecture and large community | Angular, Vue.js | High | 2024-01-22 |
| Deploy to AWS | Scalability and cost-effectiveness | Azure, Google Cloud | High | 2024-01-29 |