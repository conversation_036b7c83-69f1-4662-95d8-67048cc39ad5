# User Stories & Use Cases

## User Stories Database

| ID | User Role | Action | Benefit/Value | Priority | Related Requirements | Status | Acceptance Criteria |
|---|---|---|---|---|---|---|---|
| 1 | Employee | Clock in/out | Accurately record work hours | High | FR-4 | To Do | System records time and location |
| 2 | Manager | Approve leave request | Ensure adequate staffing levels | High | FR-6 | To Do | Employee notified of decision |
| 3 | HR | Generate attendance report | Track employee attendance trends | Medium | FR-9 | To Do | Report includes all employees |

## Use Cases Database

| ID | Name | Actor | Description | Preconditions | Main Flow | Alternative Flows | Postconditions | Related User Stories |
|---|---|---|---|---|---|---|---|---|
| 1 | Clock In | Employee | Record start of work | Employee logged in | System records time and location | GPS unavailable | Work hours recorded | 1 |
| 2 | Request Leave | Employee | Submit leave request | Employee logged in | Request sent to manager | Insufficient leave balance | Request recorded | 2 |
| 3 | Generate Report | HR | Create attendance report | HR logged in | Report generated | No employees found | Report downloaded | 3 |

## User Flow Diagrams