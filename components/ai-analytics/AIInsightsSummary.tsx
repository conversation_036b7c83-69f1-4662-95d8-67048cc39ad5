'use client';

import React from 'react';
import { AIInsights } from '@/types/subscription';
import { getPriorityColorClass, extractKeyMetrics } from '@/lib/ai-analytics';

interface AIInsightsSummaryProps {
  insights: AIInsights;
}

const AIInsightsSummary: React.FC<AIInsightsSummaryProps> = ({ insights }) => {
  const priorityClass = getPriorityColorClass(insights.priority);
  const keyMetrics = extractKeyMetrics(insights.content);

  return (
    <div className="space-y-6">
      {/* Priority Badge and Summary */}
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">AI Analysis Summary</h3>
          <p className="text-gray-600 text-sm leading-relaxed">
            {insights.summary}
          </p>
        </div>
        <div className={`px-3 py-1 rounded-full text-xs font-medium border ${priorityClass} ml-4`}>
          {insights.priority.toUpperCase()} PRIORITY
        </div>
      </div>

      {/* Key Metrics */}
      {Object.keys(keyMetrics).length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Key Metrics</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(keyMetrics).map(([key, value]) => (
              <div key={key} className="text-center">
                <div className="text-lg font-semibold text-gray-900">{value}</div>
                <div className="text-xs text-gray-500">{key}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Alerts */}
      {insights.alerts && insights.alerts.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-red-800 mb-2 flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            Critical Alerts
          </h4>
          <ul className="space-y-1">
            {insights.alerts.map((alert, index) => (
              <li key={index} className="text-sm text-red-700 flex items-start">
                <span className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                {alert}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Recommendations */}
      {insights.recommendations && insights.recommendations.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            AI Recommendations
          </h4>
          <ul className="space-y-1">
            {insights.recommendations.map((recommendation, index) => (
              <li key={index} className="text-sm text-blue-700 flex items-start">
                <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                {recommendation}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Cache Status */}
      <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
        <span>
          {insights.cached ? 'Cached result' : 'Fresh analysis'}
        </span>
        <span>
          Generated by AI Analytics
        </span>
      </div>
    </div>
  );
};

export default AIInsightsSummary;
