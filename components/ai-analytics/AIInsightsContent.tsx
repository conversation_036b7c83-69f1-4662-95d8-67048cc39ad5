'use client';

import React, { useState } from 'react';
import { AIInsights } from '@/types/subscription';
import { formatAIInsightsContent } from '@/lib/ai-analytics';

interface AIInsightsContentProps {
  insights: AIInsights;
}

const AIInsightsContent: React.FC<AIInsightsContentProps> = ({ insights }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const formattedContent = formatAIInsightsContent(insights.content);
  
  // Show first 500 characters as preview
  const previewContent = formattedContent.substring(0, 500);
  const hasMoreContent = formattedContent.length > 500;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Detailed AI Analysis</h3>
        <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
          {insights.cached ? 'Cached Analysis' : 'Live Analysis'}
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="prose prose-sm max-w-none">
          <div className="text-gray-700 leading-relaxed whitespace-pre-line">
            {isExpanded ? formattedContent : previewContent}
            {!isExpanded && hasMoreContent && '...'}
          </div>
          
          {hasMoreContent && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="mt-4 text-primary hover:text-primary-dark text-sm font-medium flex items-center"
            >
              {isExpanded ? (
                <>
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                  </svg>
                  Show Less
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  Read Full Analysis
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Analysis Metadata */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Analysis Details</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Priority Level:</span>
            <div className="font-medium capitalize text-gray-900">{insights.priority}</div>
          </div>
          <div>
            <span className="text-gray-500">Alerts:</span>
            <div className="font-medium text-gray-900">{insights.alerts?.length || 0}</div>
          </div>
          <div>
            <span className="text-gray-500">Recommendations:</span>
            <div className="font-medium text-gray-900">{insights.recommendations?.length || 0}</div>
          </div>
          <div>
            <span className="text-gray-500">Analysis Type:</span>
            <div className="font-medium text-gray-900">{insights.cached ? 'Cached' : 'Real-time'}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIInsightsContent;
