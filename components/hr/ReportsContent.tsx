'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';
import Link from 'next/link';

const ReportsContent: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Reports</h1>
        <div>
          <button className="btn-primary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Generate Report
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Reports</span>
      </div>

      <DashboardCard title="Reports">
        <div className="py-8 text-center">
          <p className="text-secondary">Reporting features will be implemented in a future update.</p>
          <p className="text-secondary mt-2">This page will include various HR reports such as attendance summaries, employee statistics, and more.</p>
        </div>
      </DashboardCard>
    </div>
  );
};

export default ReportsContent;
