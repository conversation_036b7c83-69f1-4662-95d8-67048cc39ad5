'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

interface CompanyUser {
  created_at: string;
  email: string | null;
  employee: {
    department_id: string | null;
    first_name: string;
    full_name: string;
    id: string;
    last_name: string;
  };
  employee_id: string;
  first_name: string;
  full_name: string;
  id: string;
  is_active: boolean;
  last_name: string;
  login_username: string;
  phone_number: string | null;
  role: string;
  updated_at: string | null;
  username: string;
}

interface CompanyUserDetailsProps {
  user: CompanyUser;
  onEdit: () => void;
  onBack: () => void;
  onDelete?: (user: CompanyUser) => void;
}

const CompanyUserDetails: React.FC<CompanyUserDetailsProps> = ({
  user,
  onEdit,
  onBack,
  onDelete
}) => {
  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get role badge color
  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
      case 'super-admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'hr':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'manager':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'employee':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <DashboardCard title="User Details">
        <div className="space-y-6">
          {/* User Profile Section */}
          <div className="flex items-start space-x-6">
            <div className="flex-shrink-0">
              <div className="h-20 w-20 rounded-full bg-primary-100 flex items-center justify-center">
                <span className="text-2xl font-bold text-primary">
                  {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                </span>
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{user.full_name}</h2>
                  <p className="text-gray-600">{user.email || 'No email address'}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRoleBadgeColor(user.role)}`}>
                    {user.role}
                  </span>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${
                    user.is_active
                      ? 'bg-green-100 text-green-800 border-green-200'
                      : 'bg-red-100 text-red-800 border-red-200'
                  }`}>
                    {user.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onBack}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to List
            </button>
            {onDelete && (
              <button
                onClick={() => onDelete(user)}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete User
              </button>
            )}
            <button
              onClick={onEdit}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit User
            </button>
          </div>
        </div>
      </DashboardCard>

      {/* Account Information */}
      <DashboardCard title="Account Information">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
            <div className="text-sm text-gray-900 font-mono bg-gray-50 px-3 py-2 rounded-md">
              {user.login_username}
            </div>
          </div>
        </div>
      </DashboardCard>

      {/* Personal Information */}
      <DashboardCard title="Personal Information">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
            <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
              {user.first_name}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
            <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
              {user.last_name}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
            <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
              {user.email || 'Not provided'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
            <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
              {user.phone_number || 'Not provided'}
            </div>
          </div>
        </div>
      </DashboardCard>

      {/* Employee Information */}
      <DashboardCard title="Employee Information">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Employee Full Name</label>
            <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
              {user.employee.full_name}
            </div>
          </div>
        </div>
      </DashboardCard>

      {/* Account Activity */}
      <DashboardCard title="Account Activity">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Account Created</label>
            <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
              {formatDate(user.created_at)}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
            <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
              {formatDate(user.updated_at)}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Account Status</label>
            <div className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium ${
              user.is_active
                ? 'bg-green-50 text-green-800'
                : 'bg-red-50 text-red-800'
            }`}>
              {user.is_active ? '✓ Active Account' : '✗ Inactive Account'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <div className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium ${getRoleBadgeColor(user.role).replace('border-', 'border ')}`}>
              {user.role.charAt(0).toUpperCase() + user.role.slice(1)} Access
            </div>
          </div>
        </div>
      </DashboardCard>
    </div>
  );
};

export default CompanyUserDetails;
