'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';
import Link from 'next/link';

const SettingsContent: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Settings</h1>
        <div className="flex space-x-2">
          <button className="btn-primary py-1 px-3 text-sm">Save Changes</button>
        </div>
      </div>
      
      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Settings</span>
      </div>
      
      {/* Settings Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Subscription & Billing */}
        <Link href="/dashboard/hr/subscription">
          <div className="card p-6 hover:border-primary transition-colors cursor-pointer">
            <div className="flex items-center mb-4">
              <div className="bg-blue-100 p-3 rounded-lg mr-4">
                <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-secondary-dark">Subscription & Billing</h3>
                <p className="text-sm text-secondary">Manage your plan and billing</p>
              </div>
            </div>
            <p className="text-sm text-secondary">
              View your current subscription plan, upgrade or downgrade, and manage billing information.
            </p>
          </div>
        </Link>

        {/* Company Settings */}
        <div className="card p-6 opacity-50">
          <div className="flex items-center mb-4">
            <div className="bg-gray-100 p-3 rounded-lg mr-4">
              <svg className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-600">Company Settings</h3>
              <p className="text-sm text-gray-500">Configure company details</p>
            </div>
          </div>
          <p className="text-sm text-gray-500">
            Manage company information, policies, and organizational settings.
          </p>
          <div className="mt-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              Coming Soon
            </span>
          </div>
        </div>

        {/* User Preferences */}
        <div className="card p-6 opacity-50">
          <div className="flex items-center mb-4">
            <div className="bg-gray-100 p-3 rounded-lg mr-4">
              <svg className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-600">User Preferences</h3>
              <p className="text-sm text-gray-500">Personal settings</p>
            </div>
          </div>
          <p className="text-sm text-gray-500">
            Customize your dashboard, notifications, and personal preferences.
          </p>
          <div className="mt-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              Coming Soon
            </span>
          </div>
        </div>

        {/* Security Settings */}
        <div className="card p-6 opacity-50">
          <div className="flex items-center mb-4">
            <div className="bg-gray-100 p-3 rounded-lg mr-4">
              <svg className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-600">Security Settings</h3>
              <p className="text-sm text-gray-500">Password and security</p>
            </div>
          </div>
          <p className="text-sm text-gray-500">
            Manage passwords, two-factor authentication, and security preferences.
          </p>
          <div className="mt-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              Coming Soon
            </span>
          </div>
        </div>

        {/* Notifications */}
        <div className="card p-6 opacity-50">
          <div className="flex items-center mb-4">
            <div className="bg-gray-100 p-3 rounded-lg mr-4">
              <svg className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V16a2 2 0 002 2h8a2 2 0 002-2V9h1v7a3 3 0 01-3 3H4a3 3 0 01-3-3V7.414a2 2 0 01.586-1.414l.242-.242z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-600">Notifications</h3>
              <p className="text-sm text-gray-500">Email and push settings</p>
            </div>
          </div>
          <p className="text-sm text-gray-500">
            Configure email notifications, push notifications, and communication preferences.
          </p>
          <div className="mt-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              Coming Soon
            </span>
          </div>
        </div>

        {/* Integrations */}
        <div className="card p-6 opacity-50">
          <div className="flex items-center mb-4">
            <div className="bg-gray-100 p-3 rounded-lg mr-4">
              <svg className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-600">Integrations</h3>
              <p className="text-sm text-gray-500">Third-party connections</p>
            </div>
          </div>
          <p className="text-sm text-gray-500">
            Connect with payroll systems, accounting software, and other business tools.
          </p>
          <div className="mt-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
              Coming Soon
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsContent;
