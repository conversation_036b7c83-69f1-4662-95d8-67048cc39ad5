'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  id_number?: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
}

interface AccountCreationResult {
  created_users: Array<{
    department: string | null;
    employee_id: string;
    employee_name: string;
    generated_password: string;
    login_username: string;
    role: string;
    username: string;
  }>;
  skipped_users: Array<{
    employee_id: string;
    employee_name: string;
    existing_username: string;
    reason: string;
  }>;
  failed_users: Array<{
    employee_id: string;
    employee_name: string;
    error: string;
    reason: string;
  }>;
  success: boolean;
  summary: {
    created: number;
    failed: number;
    skipped: number;
    total_requested: number;
  };
}

interface CreateBulkAccountsProps {
  employees: Employee[];
  isLoadingEmployees: boolean;
  onResult: (result: AccountCreationResult) => void;
}

const CreateBulkAccounts: React.FC<CreateBulkAccountsProps> = ({
  employees,
  isLoadingEmployees,
  onResult
}) => {
  const { companies } = useAuth();
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState('');

  // Filter employees based on search term
  const filteredEmployees = employees.filter(employee =>
    employee.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.position || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle employee selection
  const handleEmployeeToggle = (employeeId: string) => {
    setSelectedEmployees(prev => 
      prev.includes(employeeId)
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  // Select all filtered employees
  const handleSelectAll = () => {
    const allFilteredIds = filteredEmployees.map(emp => emp.employee_id);
    setSelectedEmployees(allFilteredIds);
  };

  // Clear all selections
  const handleClearAll = () => {
    setSelectedEmployees([]);
  };

  // Create accounts for selected employees
  const handleCreateAccounts = async () => {
    try {
      if (selectedEmployees.length === 0) {
        setError('Please select at least one employee');
        return;
      }

      if (!companies || companies.length === 0) {
        setError('No company available');
        return;
      }

      setIsCreating(true);
      setError('');

      const { apiPost } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const requestData = {
        company_id: companies[0].company_id,
        employee_ids: selectedEmployees
      };

      console.log('Creating bulk accounts with data:', requestData);

      const response = await apiPost<AccountCreationResult>(
        'api/company-users/bulk',
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Bulk account creation response:', response);

      if (response.success) {
        onResult(response);
        setSelectedEmployees([]);
        setSearchTerm('');
      } else {
        setError('Failed to create accounts');
      }
    } catch (error: any) {
      console.error('Error creating bulk accounts:', error);
      setError(error.message || 'Failed to create accounts');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-secondary-dark mb-4">
          Create Accounts for Multiple Employees
        </h3>
        <p className="text-sm text-gray-600 mb-6">
          Select multiple employees to create login accounts for them. Employees who already have accounts will be skipped.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Search and Selection Controls */}
      <div className="space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search employees by name, email, or position..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={isLoadingEmployees || isCreating}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            />
          </div>
          <div className="flex gap-2">
            <button
              onClick={handleSelectAll}
              disabled={isLoadingEmployees || isCreating || filteredEmployees.length === 0}
              className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Select All ({filteredEmployees.length})
            </button>
            <button
              onClick={handleClearAll}
              disabled={isLoadingEmployees || isCreating || selectedEmployees.length === 0}
              className="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear All
            </button>
          </div>
        </div>

        {/* Selection Summary */}
        {selectedEmployees.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <p className="text-sm text-green-700">
              <span className="font-medium">{selectedEmployees.length}</span> employee{selectedEmployees.length !== 1 ? 's' : ''} selected for account creation
            </p>
          </div>
        )}
      </div>

      {/* Employee List */}
      <div className="border border-gray-200 rounded-lg">
        <div className="max-h-96 overflow-y-auto">
          {isLoadingEmployees ? (
            <div className="p-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="mt-2 text-secondary">Loading employees...</p>
            </div>
          ) : filteredEmployees.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-gray-500">
                {searchTerm ? `No employees found matching "${searchTerm}"` : 'No employees available'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredEmployees.map((employee) => (
                <div
                  key={employee.employee_id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer ${
                    selectedEmployees.includes(employee.employee_id) ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                  onClick={() => handleEmployeeToggle(employee.employee_id)}
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedEmployees.includes(employee.employee_id)}
                      onChange={() => handleEmployeeToggle(employee.employee_id)}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <div className="ml-3 flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">{employee.full_name}</h4>
                          <p className="text-sm text-gray-500">
                            {employee.position || 'No Position'} • {employee.email || 'No Email'}
                          </p>
                        </div>
                        <div className="text-right">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            employee.status === 'active' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {employee.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Action Button */}
      <div className="flex justify-end">
        <button
          onClick={handleCreateAccounts}
          disabled={selectedEmployees.length === 0 || isCreating || isLoadingEmployees}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isCreating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Creating Accounts...
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              Create Accounts ({selectedEmployees.length})
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default CreateBulkAccounts;
