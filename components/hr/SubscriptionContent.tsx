'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { SubscriptionPlan } from '@/types/subscription';
import { getSubscriptionPlans, formatFeaturesForDisplay, formatPrice } from '@/lib/subscription';
import DashboardCard from '@/components/ui/DashboardCard';

const SubscriptionContent: React.FC = () => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const fetchedPlans = await getSubscriptionPlans();
        const activePlans = fetchedPlans
          .filter(plan => plan.is_active)
          .sort((a, b) => a.sort_order - b.sort_order);
        setPlans(activePlans);
        
        // For demo purposes, set the first plan as selected
        // In a real app, this would come from the company's current subscription
        if (activePlans.length > 0) {
          setSelectedPlan(activePlans[0]);
        }
      } catch (err) {
        setError('Failed to load subscription plans. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlans();
  }, []);

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Subscription & Billing</h1>
      </div>
      
      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <Link href="/dashboard/hr/settings" className="hover:text-primary">Settings</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Subscription</span>
      </div>

      {/* Current Plan */}
      {selectedPlan && (
        <DashboardCard title="Current Plan">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-xl font-bold text-blue-900 mb-2">{selectedPlan.name}</h3>
                <p className="text-blue-700 mb-4">{selectedPlan.description}</p>
                <div className="flex items-baseline space-x-2">
                  <span className="text-2xl font-bold text-blue-900">
                    {formatPrice(selectedPlan.flat_price)}
                  </span>
                  <span className="text-blue-700">/month</span>
                  <span className="text-sm text-blue-600">
                    + {formatPrice(selectedPlan.price_per_employee)}/employee
                  </span>
                </div>
                {selectedPlan.max_employees && (
                  <p className="text-sm text-blue-600 mt-1">
                    Up to {selectedPlan.max_employees} employees
                  </p>
                )}
              </div>
              <div className="text-right">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Active
                </span>
              </div>
            </div>
          </div>
        </DashboardCard>
      )}

      {/* Plan Features */}
      {selectedPlan && (
        <DashboardCard title="Plan Features">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {formatFeaturesForDisplay(selectedPlan.features).map((feature, index) => (
              <div key={index} className="flex items-start space-x-3">
                <svg
                  className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <div>
                  <p className="font-medium text-gray-900">{feature.name}</p>
                  {feature.description && (
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>
      )}

      {/* Available Plans */}
      <DashboardCard title="Available Plans">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading plans...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <p className="font-medium">Unable to load plans</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {plans.map((plan) => {
              const isCurrentPlan = selectedPlan?.plan_id === plan.plan_id;
              const features = formatFeaturesForDisplay(plan.features);
              
              return (
                <div
                  key={plan.plan_id}
                  className={`border rounded-lg p-6 transition-all duration-200 ${
                    isCurrentPlan
                      ? 'border-primary bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center mb-4">
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{plan.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">{plan.description}</p>
                    <div className="mb-4">
                      <div className="text-2xl font-bold text-gray-900">
                        {formatPrice(plan.flat_price)}
                        <span className="text-sm font-normal">/mo</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        + {formatPrice(plan.price_per_employee)}/employee
                      </div>
                    </div>
                  </div>
                  
                  <ul className="space-y-2 mb-6">
                    {features.slice(0, 5).map((feature, index) => (
                      <li key={index} className="flex items-start text-sm">
                        <svg
                          className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <span className="text-gray-700">{feature.name}</span>
                      </li>
                    ))}
                    {features.length > 5 && (
                      <li className="text-sm text-gray-500 ml-6">
                        +{features.length - 5} more features
                      </li>
                    )}
                  </ul>
                  
                  {isCurrentPlan ? (
                    <div className="text-center">
                      <span className="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-primary text-white">
                        Current Plan
                      </span>
                    </div>
                  ) : (
                    <button
                      onClick={() => handlePlanSelect(plan)}
                      className="w-full py-2 px-4 border border-primary text-primary rounded-lg hover:bg-primary hover:text-white transition-colors duration-200"
                    >
                      Select Plan
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </DashboardCard>

      {/* Billing Information */}
      <DashboardCard title="Billing Information">
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
          <p className="text-gray-600 mb-4">Billing management will be available soon</p>
          <p className="text-sm text-gray-500">
            You'll be able to view invoices, update payment methods, and manage billing preferences.
          </p>
        </div>
      </DashboardCard>
    </div>
  );
};

export default SubscriptionContent;
