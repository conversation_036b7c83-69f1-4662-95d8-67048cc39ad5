'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet, apiPost, apiPatch, apiDelete } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import LeaveBalancesContent from './LeaveBalancesContent';
import LeaveBalanceDoctor from './LeaveBalanceDoctor';
import LeaveAnalytics from './LeaveAnalytics';
import LeaveTrendsAnalytics from './LeaveTrendsAnalytics';
import Link from 'next/link';

interface LeaveType {
  leave_type_id: string;
  name: string;
  code: string;
  description: string;
  is_paid: boolean;
  requires_approval: boolean;
  requires_documentation: boolean;
  created_at: string;
  updated_at: string;
}

interface LeaveTypesResponse {
  code: number;
  extend: {
    leave_types: LeaveType[];
  };
  msg: string;
}

interface LeaveRequest {
  request_id: string;
  employee_id: string;
  employee_name: string;
  leave_type_id: string;
  leave_type_name: string;
  start_date: string;
  end_date: string;
  total_days: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  approved_by?: string;
  approved_at?: string;
  rejection_reason?: string;
  documentation_path?: string;
  created_at: string;
  updated_at: string;
}

interface LeaveRequestsResponse {
  extend: {
    leave_requests: LeaveRequest[];
    pagination: {
      has_next: boolean;
      has_prev: boolean;
      page: number;
      pages: number;
      per_page: number;
      total_count: number;
    };
  };
  msg: string;
}

interface LeaveTypeFormData {
  name: string;
  code: string;
  description: string;
  is_paid: boolean;
  requires_approval: boolean;
  requires_documentation: boolean;
}

const LeaveManagementContent: React.FC = () => {
  const { companies } = useAuth();
  const [activeTab, setActiveTab] = useState<'types' | 'requests' | 'balances' | 'doctor' | 'analytics' | 'trends'>('types');
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [requestsLoading, setRequestsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingLeaveType, setEditingLeaveType] = useState<LeaveType | null>(null);
  const [formData, setFormData] = useState<LeaveTypeFormData>({
    name: '',
    code: '',
    description: '',
    is_paid: true,
    requires_approval: true,
    requires_documentation: false
  });
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [leaveTypeToDelete, setLeaveTypeToDelete] = useState<LeaveType | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [requestToReject, setRequestToReject] = useState<LeaveRequest | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [rejecting, setRejecting] = useState(false);

  // Fetch leave types
  const fetchLeaveTypes = async () => {
    try {
      if (!companies || companies.length === 0) {
        setLoading(false);
        return;
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      setLoading(true);
      setError('');

      const response = await apiGet<LeaveTypesResponse>(
        `api/leave/types?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.leave_types) {
        setLeaveTypes(response.extend.leave_types);
      }
    } catch (error: any) {
      console.error('Error fetching leave types:', error);
      setError(error.message || 'Failed to fetch leave types');
    } finally {
      setLoading(false);
    }
  };

  // Fetch leave requests
  const fetchLeaveRequests = async (status?: string) => {
    try {
      if (!companies || companies.length === 0) {
        setRequestsLoading(false);
        return;
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      setRequestsLoading(true);

      let url = `api/leave/requests?company_id=${companyId}`;
      if (status) {
        url += `&status=${status}`;
      }

      const response = await apiGet<LeaveRequestsResponse>(url, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.extend && response.extend.leave_requests) {
        setLeaveRequests(response.extend.leave_requests);
      } else {
        // Set mock data if no requests found
        setLeaveRequests([]);
      }
    } catch (error: any) {
      console.error('Error fetching leave requests:', error);
      // Set mock data as fallback
      const mockRequests: LeaveRequest[] = [
        {
          request_id: 'mock-1',
          employee_id: 'emp-1',
          employee_name: 'John Doe',
          leave_type_id: 'annual',
          leave_type_name: 'Annual Leave',
          start_date: '2025-07-01',
          end_date: '2025-07-05',
          total_days: 5,
          reason: 'Family vacation',
          status: 'pending',
          created_at: '2025-06-01T10:00:00Z',
          updated_at: '2025-06-01T10:00:00Z'
        },
        {
          request_id: 'mock-2',
          employee_id: 'emp-2',
          employee_name: 'Jane Smith',
          leave_type_id: 'sick',
          leave_type_name: 'Sick Leave',
          start_date: '2025-06-15',
          end_date: '2025-06-16',
          total_days: 2,
          reason: 'Medical appointment',
          status: 'approved',
          approved_by: 'hr-manager',
          approved_at: '2025-06-02T14:30:00Z',
          created_at: '2025-06-01T09:00:00Z',
          updated_at: '2025-06-02T14:30:00Z'
        },
        {
          request_id: 'mock-3',
          employee_id: 'emp-3',
          employee_name: 'Mike Johnson',
          leave_type_id: 'personal',
          leave_type_name: 'Personal Leave',
          start_date: '2025-06-20',
          end_date: '2025-06-20',
          total_days: 1,
          reason: 'Personal matters',
          status: 'pending',
          created_at: '2025-06-03T11:00:00Z',
          updated_at: '2025-06-03T11:00:00Z'
        }
      ];
      setLeaveRequests(mockRequests);
    } finally {
      setRequestsLoading(false);
    }
  };

  // Approve leave request
  const handleApproveRequest = async (requestId: string) => {
    try {
      if (!companies || companies.length === 0) {
        throw new Error('Company information not available');
      }

      const token = getAccessToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies[0].company_id;

      await apiPost(
        `api/leave/requests/${requestId}/approve`,
        { company_id: companyId },
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      // Refresh the requests list
      fetchLeaveRequests();
    } catch (error: any) {
      console.error('Error approving leave request:', error);
      alert(error.message || 'Failed to approve leave request');
    }
  };

  // Show reject modal
  const handleRejectClick = (request: LeaveRequest) => {
    setRequestToReject(request);
    setRejectionReason('');
    setShowRejectModal(true);
  };

  // Reject leave request
  const handleConfirmReject = async () => {
    if (!requestToReject || !rejectionReason.trim()) {
      alert('Please provide a rejection reason');
      return;
    }

    setRejecting(true);
    try {
      if (!companies || companies.length === 0) {
        throw new Error('Company information not available');
      }

      const token = getAccessToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies[0].company_id;

      await apiPost(
        `api/leave/requests/${requestToReject.request_id}/reject`,
        {
          company_id: companyId,
          rejection_reason: rejectionReason
        },
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      setShowRejectModal(false);
      setRequestToReject(null);
      setRejectionReason('');
      fetchLeaveRequests(); // Refresh the requests list
    } catch (error: any) {
      console.error('Error rejecting leave request:', error);
      alert(error.message || 'Failed to reject leave request');
    } finally {
      setRejecting(false);
    }
  };

  // Cancel reject
  const handleCancelReject = () => {
    setShowRejectModal(false);
    setRequestToReject(null);
    setRejectionReason('');
  };

  useEffect(() => {
    fetchLeaveTypes();
    if (activeTab === 'requests') {
      fetchLeaveRequests();
    }
  }, [companies, activeTab]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  // Open modal for creating new leave type
  const handleCreateNew = () => {
    setEditingLeaveType(null);
    setFormData({
      name: '',
      code: '',
      description: '',
      is_paid: true,
      requires_approval: true,
      requires_documentation: false
    });
    setShowModal(true);
    setError('');
  };

  // Open modal for editing leave type
  const handleEdit = (leaveType: LeaveType) => {
    setEditingLeaveType(leaveType);
    setFormData({
      name: leaveType.name,
      code: leaveType.code,
      description: leaveType.description,
      is_paid: leaveType.is_paid,
      requires_approval: leaveType.requires_approval,
      requires_documentation: leaveType.requires_documentation
    });
    setShowModal(true);
    setError('');
  };

  // Submit form (create or update)
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!companies || companies.length === 0) {
      setError('Company information not available');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const token = getAccessToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies[0].company_id;
      const requestData = {
        company_id: companyId,
        ...formData
      };

      if (editingLeaveType) {
        // Update existing leave type
        await apiPatch(
          `api/leave/types/${editingLeaveType.leave_type_id}`,
          requestData,
          {
            headers: { 'Authorization': `Bearer ${token}` }
          }
        );
      } else {
        // Create new leave type
        await apiPost(
          'api/leave/types',
          requestData,
          {
            headers: { 'Authorization': `Bearer ${token}` }
          }
        );
      }

      setShowModal(false);
      fetchLeaveTypes(); // Refresh the list
    } catch (error: any) {
      console.error('Error saving leave type:', error);
      if (error.message.includes('409')) {
        setError('Leave type code already exists. Please use a unique code.');
      } else {
        setError(error.message || 'Failed to save leave type');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Show delete confirmation modal
  const handleDeleteClick = (leaveType: LeaveType) => {
    setLeaveTypeToDelete(leaveType);
    setShowDeleteModal(true);
  };

  // Delete leave type
  const handleConfirmDelete = async () => {
    if (!leaveTypeToDelete) return;

    setDeleting(true);
    try {
      if (!companies || companies.length === 0) {
        throw new Error('Company information not available');
      }

      const token = getAccessToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies[0].company_id;

      await apiDelete(
        `api/leave/types/${leaveTypeToDelete.leave_type_id}?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      setShowDeleteModal(false);
      setLeaveTypeToDelete(null);
      fetchLeaveTypes(); // Refresh the list
    } catch (error: any) {
      console.error('Error deleting leave type:', error);
      setError(error.message || 'Failed to delete leave type');
    } finally {
      setDeleting(false);
    }
  };

  // Cancel delete
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setLeaveTypeToDelete(null);
  };

  // Close modal
  const handleCloseModal = () => {
    setShowModal(false);
    setEditingLeaveType(null);
    setError('');
  };

  // Stats
  const totalLeaveTypes = leaveTypes.length;
  const paidLeaveTypes = leaveTypes.filter(lt => lt.is_paid).length;
  const approvalRequiredTypes = leaveTypes.filter(lt => lt.requires_approval).length;

  const stats = [
    { title: 'Total Leave Types', value: totalLeaveTypes.toString(), change: '', changeType: 'neutral' as const },
    { title: 'Paid Leave Types', value: paidLeaveTypes.toString(), change: '', changeType: 'positive' as const },
    { title: 'Require Approval', value: approvalRequiredTypes.toString(), change: '', changeType: 'neutral' as const },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Leave Management</h1>
        {activeTab === 'types' && (
          <button
            onClick={handleCreateNew}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-colors flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Leave Type
          </button>
        )}
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-gray-600">
        <Link href="/dashboard/hr" className="hover:text-blue-600">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-gray-900">Leave Management</span>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('types')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'types'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Leave Types
          </button>
          <button
            onClick={() => setActiveTab('requests')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'requests'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Leave Requests
          </button>
          <button
            onClick={() => setActiveTab('balances')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'balances'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Leave Balances
          </button>
          <button
            onClick={() => setActiveTab('doctor')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'doctor'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Balance Doctor
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analytics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Analytics & Reports
          </button>
          <button
            onClick={() => setActiveTab('trends')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'trends'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Trends Analytics
          </button>
        </nav>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Content based on active tab */}
      {activeTab === 'types' ? (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {stats.map((stat, index) => (
              <DashboardStats
                key={index}
                title={stat.title}
                value={stat.value}
                change={stat.change}
                changeType={stat.changeType}
                loading={loading}
              />
            ))}
          </div>

      {/* Leave Types Table */}
      <DashboardCard title="Leave Types" loading={loading}>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {['Name', 'Code', 'Description', 'Paid', 'Requires Approval', 'Requires Documentation', 'Actions'].map((header) => (
                  <th
                    key={header}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {leaveTypes.length > 0 ? (
                leaveTypes.map((leaveType) => (
                  <tr key={leaveType.leave_type_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{leaveType.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        {leaveType.code}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 max-w-xs truncate" title={leaveType.description}>
                        {leaveType.description}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        leaveType.is_paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {leaveType.is_paid ? 'Yes' : 'No'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        leaveType.requires_approval ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {leaveType.requires_approval ? 'Yes' : 'No'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        leaveType.requires_documentation ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {leaveType.requires_documentation ? 'Yes' : 'No'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(leaveType)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteClick(leaveType)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    {loading ? 'Loading...' : 'No leave types found'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </DashboardCard>
        </>
      ) : activeTab === 'requests' ? (
        /* Leave Requests Tab */
        <DashboardCard title="Leave Requests" loading={requestsLoading}>
          {/* Debug Info */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Debug:</strong> Total requests: {leaveRequests.length} |
                Pending: {leaveRequests.filter(r => r.status === 'pending').length} |
                Approved: {leaveRequests.filter(r => r.status === 'approved').length} |
                Rejected: {leaveRequests.filter(r => r.status === 'rejected').length}
              </p>
            </div>
          )}

          <div className="mb-4 flex space-x-2">
            <button
              onClick={() => fetchLeaveRequests()}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              All
            </button>
            <button
              onClick={() => fetchLeaveRequests('pending')}
              className="px-3 py-1 text-sm bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200"
            >
              Pending
            </button>
            <button
              onClick={() => fetchLeaveRequests('approved')}
              className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200"
            >
              Approved
            </button>
            <button
              onClick={() => fetchLeaveRequests('rejected')}
              className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200"
            >
              Rejected
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {['Employee', 'Leave Type', 'Start Date', 'End Date', 'Days', 'Reason', 'Status', 'Actions'].map((header) => (
                    <th
                      key={header}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {leaveRequests.length > 0 ? (
                  leaveRequests.map((request) => (
                    <tr key={request.request_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{request.employee_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{request.leave_type_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{request.start_date}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{request.end_date}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{request.total_days} days</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-500 max-w-xs truncate" title={request.reason}>
                          {request.reason}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            request.status === 'approved'
                              ? 'bg-green-100 text-green-800'
                              : request.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {request.status === 'pending' ? (
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleApproveRequest(request.request_id)}
                              className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            >
                              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                              Approve
                            </button>
                            <button
                              onClick={() => handleRejectClick(request)}
                              className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                              Reject
                            </button>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              request.status === 'approved'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {request.status === 'approved' ? (
                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              ) : (
                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                              )}
                              {request.status === 'approved' ? 'Approved' : 'Rejected'}
                            </span>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-sm text-gray-500">
                      {requestsLoading ? 'Loading...' : 'No leave requests found'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </DashboardCard>
      ) : activeTab === 'balances' ? (
        /* Leave Balances Tab */
        <LeaveBalancesContent />
      ) : activeTab === 'doctor' ? (
        /* Balance Doctor Tab */
        <LeaveBalanceDoctor />
      ) : activeTab === 'analytics' ? (
        /* Analytics Tab */
        <LeaveAnalytics />
      ) : (
        /* Trends Analytics Tab */
        <LeaveTrendsAnalytics />
      )}

      {/* Modal for Create/Edit Leave Type */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border border-gray-300 w-96 rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingLeaveType ? 'Edit Leave Type' : 'Create New Leave Type'}
              </h3>

              {error && (
                <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Annual Leave"
                  />
                </div>

                <div>
                  <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-1">
                    Code * (must be unique)
                  </label>
                  <input
                    type="text"
                    id="code"
                    name="code"
                    value={formData.code}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., ANNUAL"
                    style={{ textTransform: 'uppercase' }}
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    required
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe this leave type..."
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="is_paid"
                      name="is_paid"
                      checked={formData.is_paid}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="is_paid" className="ml-2 block text-sm text-gray-900">
                      Is Paid Leave
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="requires_approval"
                      name="requires_approval"
                      checked={formData.requires_approval}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="requires_approval" className="ml-2 block text-sm text-gray-900">
                      Requires Approval
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="requires_documentation"
                      name="requires_documentation"
                      checked={formData.requires_documentation}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="requires_documentation" className="ml-2 block text-sm text-gray-900">
                      Requires Documentation
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={handleCloseModal}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {submitting ? 'Saving...' : (editingLeaveType ? 'Update' : 'Create')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && leaveTypeToDelete && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Icon */}
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>

              {/* Title */}
              <h3 className="text-lg font-medium text-gray-900 text-center mb-2">
                Delete Leave Type
              </h3>

              {/* Message */}
              <div className="text-center mb-6">
                <p className="text-sm text-gray-600 mb-3">
                  Are you sure you want to delete the leave type:
                </p>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-3">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-900">{leaveTypeToDelete.name}</span>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      {leaveTypeToDelete.code}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{leaveTypeToDelete.description}</p>
                </div>
                <p className="text-sm text-red-600 font-medium">
                  This action cannot be undone. All associated data will be permanently removed.
                </p>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              )}

              {/* Buttons */}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleCancelDelete}
                  disabled={deleting}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleConfirmDelete}
                  disabled={deleting}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {deleting ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </div>
                  ) : (
                    'Delete Leave Type'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reject Leave Request Modal */}
      {showRejectModal && requestToReject && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Icon */}
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>

              {/* Title */}
              <h3 className="text-lg font-medium text-gray-900 text-center mb-2">
                Reject Leave Request
              </h3>

              {/* Request Details */}
              <div className="text-center mb-4">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mb-3">
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{requestToReject.employee_name}</p>
                    <p className="text-gray-600">{requestToReject.leave_type_name}</p>
                    <p className="text-gray-600">{requestToReject.start_date} to {requestToReject.end_date}</p>
                    <p className="text-gray-600">{requestToReject.total_days} days</p>
                  </div>
                </div>
              </div>

              {/* Rejection Reason */}
              <div className="mb-4">
                <label htmlFor="rejection_reason" className="block text-sm font-medium text-gray-700 mb-2">
                  Rejection Reason *
                </label>
                <textarea
                  id="rejection_reason"
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  placeholder="Please provide a reason for rejecting this leave request..."
                  required
                />
              </div>

              {/* Buttons */}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleCancelReject}
                  disabled={rejecting}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleConfirmReject}
                  disabled={rejecting || !rejectionReason.trim()}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {rejecting ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Rejecting...
                    </div>
                  ) : (
                    'Reject Request'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LeaveManagementContent;
