'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Announcement } from '@/types/announcement';
import { 
  getAnnouncements, 
  deleteAnnouncement, 
  publishAnnouncement, 
  unpublishAnnouncement, 
  archiveAnnouncement 
} from '@/lib/announcements';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import AnnouncementCard from '@/components/announcements/AnnouncementCard';
import AnnouncementModal from '@/components/announcements/AnnouncementModal';
import AnnouncementAnalytics from '@/components/announcements/AnnouncementAnalytics';

const AnnouncementsContent = () => {
  const { companies } = useAuth();
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAnalyticsModalOpen, setIsAnalyticsModalOpen] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | undefined>();
  
  // Filter states
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft'>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  // Delete confirmation state
  const [announcementToDelete, setAnnouncementToDelete] = useState<Announcement | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);

  // Fetch announcements
  const fetchAnnouncements = async () => {
    try {
      setIsLoading(true);
      setError('');

      if (!companies || companies.length === 0) {
        setError('No company found');
        return;
      }

      const companyId = companies[0].company_id;
      const options: any = {};

      if (statusFilter === 'published') {
        options.is_published = true;
      } else if (statusFilter === 'draft') {
        options.is_published = false;
      }
      if (priorityFilter !== 'all') {
        options.priority = priorityFilter;
      }
      if (typeFilter !== 'all') {
        options.announcement_type = typeFilter;
      }

      const response = await getAnnouncements(companyId, options);
      setAnnouncements(response.announcements);
    } catch (error: any) {
      console.error('Error fetching announcements:', error);
      setError(error.message || 'Failed to fetch announcements');
      setAnnouncements([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch announcements when component mounts or filters change
  useEffect(() => {
    fetchAnnouncements();
  }, [companies, statusFilter, priorityFilter, typeFilter]);

  // Modal handlers
  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  const openEditModal = (announcement: Announcement) => {
    setSelectedAnnouncement(announcement);
    setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
    setSelectedAnnouncement(undefined);
    setIsEditModalOpen(false);
  };

  const openAnalyticsModal = (announcement: Announcement) => {
    setSelectedAnnouncement(announcement);
    setIsAnalyticsModalOpen(true);
  };
  const closeAnalyticsModal = () => {
    setSelectedAnnouncement(undefined);
    setIsAnalyticsModalOpen(false);
  };

  // Delete handlers
  const openDeleteConfirm = (announcement: Announcement) => {
    setAnnouncementToDelete(announcement);
    setIsDeleteConfirmOpen(true);
  };
  const closeDeleteConfirm = () => {
    setAnnouncementToDelete(null);
    setIsDeleteConfirmOpen(false);
  };

  const handleDeleteAnnouncement = async () => {
    if (!announcementToDelete || !companies || companies.length === 0) return;

    try {
      setIsLoading(true);
      const companyId = companies[0].company_id;
      
      await deleteAnnouncement(announcementToDelete.announcement_id, companyId);
      
      // Remove the deleted announcement from the state
      setAnnouncements(announcements.filter(a => a.announcement_id !== announcementToDelete.announcement_id));
      closeDeleteConfirm();
    } catch (error: any) {
      console.error('Error deleting announcement:', error);
      setError(error.message || 'Failed to delete announcement');
    } finally {
      setIsLoading(false);
    }
  };

  // Action handlers
  const handlePublish = async (announcement: Announcement) => {
    if (!companies || companies.length === 0) return;

    try {
      const companyId = companies[0].company_id;
      await publishAnnouncement(announcement.announcement_id, companyId);
      fetchAnnouncements(); // Refresh the list
    } catch (error: any) {
      console.error('Error publishing announcement:', error);
      setError(error.message || 'Failed to publish announcement');
    }
  };

  const handleUnpublish = async (announcement: Announcement) => {
    if (!companies || companies.length === 0) return;

    try {
      const companyId = companies[0].company_id;
      await unpublishAnnouncement(announcement.announcement_id, companyId);
      fetchAnnouncements(); // Refresh the list
    } catch (error: any) {
      console.error('Error unpublishing announcement:', error);
      setError(error.message || 'Failed to unpublish announcement');
    }
  };

  const handleArchive = async (announcement: Announcement) => {
    if (!companies || companies.length === 0) return;

    try {
      const companyId = companies[0].company_id;
      await archiveAnnouncement(announcement.announcement_id, companyId);
      fetchAnnouncements(); // Refresh the list
    } catch (error: any) {
      console.error('Error archiving announcement:', error);
      setError(error.message || 'Failed to archive announcement');
    }
  };

  // Calculate stats
  const totalAnnouncements = announcements.length;
  const publishedAnnouncements = announcements.filter(a => a.is_published).length;
  const draftAnnouncements = announcements.filter(a => !a.is_published).length;
  const urgentAnnouncements = announcements.filter(a => a.priority === 'URGENT').length;
  const totalReads = announcements.reduce((sum, a) => sum + a.read_count, 0);
  const totalAcknowledgments = announcements.reduce((sum, a) => sum + a.acknowledgment_count, 0);

  const stats = [
    { title: 'Total Announcements', value: totalAnnouncements.toString(), change: '', changeType: 'neutral' as const },
    { title: 'Published', value: publishedAnnouncements.toString(), change: '', changeType: 'positive' as const },
    { title: 'Total Reads', value: totalReads.toString(), change: '', changeType: 'positive' as const },
    { title: 'Acknowledgments', value: totalAcknowledgments.toString(), change: '', changeType: 'positive' as const },
  ];

  return (
    <div className="space-y-6">
      {/* Create Announcement Modal */}
      <AnnouncementModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        onSuccess={() => {
          closeCreateModal();
          fetchAnnouncements();
        }}
      />

      {/* Edit Announcement Modal */}
      <AnnouncementModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSuccess={() => {
          closeEditModal();
          fetchAnnouncements();
        }}
        announcement={selectedAnnouncement}
        isEditing={true}
      />

      {/* Analytics Modal */}
      {selectedAnnouncement && (
        <AnnouncementAnalytics
          announcement={selectedAnnouncement}
          isOpen={isAnalyticsModalOpen}
          onClose={closeAnalyticsModal}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {isDeleteConfirmOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" onClick={closeDeleteConfirm}>
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Announcement</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete "{announcementToDelete?.title}"? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDeleteAnnouncement}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deleting...' : 'Delete'}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeDeleteConfirm}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Announcements Management</h1>
        <button
          onClick={openCreateModal}
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-colors flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Create Announcement
        </button>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-gray-600">
        <Link href="/dashboard/hr" className="hover:text-blue-600">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-gray-900">Announcements</span>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            loading={isLoading}
          />
        ))}
      </div>

      {/* Filters */}
      <DashboardCard title="Filters">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status-filter"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
            </select>
          </div>

          <div>
            <label htmlFor="priority-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Priority
            </label>
            <select
              id="priority-filter"
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All</option>
              <option value="URGENT">Urgent</option>
              <option value="HIGH">High</option>
              <option value="MEDIUM">Medium</option>
              <option value="LOW">Low</option>
            </select>
          </div>

          <div>
            <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              id="type-filter"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All</option>
              <option value="GENERAL">General</option>
              <option value="POLICY">Policy</option>
              <option value="EVENT">Event</option>
              <option value="URGENT">Urgent</option>
              <option value="MAINTENANCE">Maintenance</option>
              <option value="CELEBRATION">Celebration</option>
              <option value="TRAINING">Training</option>
              <option value="MEETING">Meeting</option>
            </select>
          </div>
        </div>
      </DashboardCard>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Announcements List */}
      <DashboardCard title="Announcements" loading={isLoading}>
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Loading announcements...</p>
          </div>
        ) : announcements.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-gray-600">No announcements found.</p>
            <button
              onClick={openCreateModal}
              className="mt-2 text-blue-600 hover:text-blue-800"
            >
              Create your first announcement
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {announcements.map((announcement) => (
              <AnnouncementCard
                key={announcement.announcement_id}
                announcement={announcement}
                onEdit={openEditModal}
                onDelete={openDeleteConfirm}
                onPublish={handlePublish}
                onUnpublish={handleUnpublish}
                onArchive={handleArchive}
                onAnalytics={openAnalyticsModal}
              />
            ))}
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default AnnouncementsContent;
