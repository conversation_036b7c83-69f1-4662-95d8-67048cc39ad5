'use client';

import React, { useState } from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

interface CompanyUser {
  created_at: string;
  email: string | null;
  employee: {
    department_id: string | null;
    first_name: string;
    full_name: string;
    id: string;
    last_name: string;
  };
  employee_id: string;
  first_name: string;
  full_name: string;
  id: string;
  is_active: boolean;
  last_name: string;
  login_username: string;
  phone_number: string | null;
  role: string;
  updated_at: string | null;
  username: string;
}

interface CompanyUserEditProps {
  user: CompanyUser;
  onUpdate: (userId: string, updateData: any) => Promise<CompanyUser>;
  onBack: () => void;
  onCancel: () => void;
}

const CompanyUserEdit: React.FC<CompanyUserEditProps> = ({
  user,
  onUpdate,
  onBack,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    email: user.email || '',
    phone_number: user.phone_number || '',
    role: user.role,
    is_active: user.is_active,
    password: ''
  });
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsUpdating(true);
      setError('');
      setSuccess('');

      // Prepare update data (only include fields that have values)
      const updateData: any = {};
      
      if (formData.email.trim() !== (user.email || '')) {
        updateData.email = formData.email.trim() || null;
      }
      
      if (formData.phone_number.trim() !== (user.phone_number || '')) {
        updateData.phone = formData.phone_number.trim() || null;
      }
      
      if (formData.role !== user.role) {
        updateData.role = formData.role;
      }
      
      if (formData.is_active !== user.is_active) {
        updateData.is_active = formData.is_active;
      }
      
      if (formData.password.trim()) {
        updateData.password = formData.password.trim();
      }

      // Only proceed if there are changes
      if (Object.keys(updateData).length === 0) {
        setError('No changes detected');
        return;
      }

      console.log('Updating user with data:', updateData);

      await onUpdate(user.id, updateData);
      
      setSuccess('User updated successfully');
      
      // Clear password field after successful update
      setFormData(prev => ({ ...prev, password: '' }));
      
      // Redirect back after a short delay
      setTimeout(() => {
        onBack();
      }, 1500);
      
    } catch (error: any) {
      console.error('Error updating user:', error);
      setError(error.message || 'Failed to update user');
    } finally {
      setIsUpdating(false);
    }
  };

  // Get role badge color
  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
      case 'super-admin':
        return 'bg-red-100 text-red-800';
      case 'hr':
        return 'bg-purple-100 text-purple-800';
      case 'manager':
        return 'bg-blue-100 text-blue-800';
      case 'employee':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <DashboardCard title="Edit User Account">
        <div className="space-y-6">
          {/* User Profile Section */}
          <div className="flex items-start space-x-6">
            <div className="flex-shrink-0">
              <div className="h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center">
                <span className="text-xl font-bold text-primary">
                  {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                </span>
              </div>
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-bold text-gray-900">{user.full_name}</h2>
              <p className="text-gray-600">@{user.username}</p>
              <div className="mt-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeColor(user.role)}`}>
                  Current Role: {user.role}
                </span>
              </div>
            </div>
          </div>

          {/* Status Messages */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
              {success}
            </div>
          )}

          {/* Edit Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Contact Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    disabled={isUpdating}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Enter email address"
                  />
                </div>
                
                <div>
                  <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone_number"
                    name="phone_number"
                    value={formData.phone_number}
                    onChange={handleChange}
                    disabled={isUpdating}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                    placeholder="Enter phone number"
                  />
                </div>
              </div>
            </div>

            {/* Account Settings */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Account Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                    Role
                  </label>
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleChange}
                    disabled={isUpdating}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <option value="employee">Employee</option>
                    <option value="manager">Manager</option>
                    <option value="hr">HR</option>
                    <option value="admin">Admin</option>
                    <option value="super-admin">Super Admin</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Status
                  </label>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="is_active"
                      name="is_active"
                      checked={formData.is_active}
                      onChange={handleChange}
                      disabled={isUpdating}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded disabled:opacity-50"
                    />
                    <label htmlFor="is_active" className="ml-2 text-sm text-gray-700">
                      Account is active
                    </label>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Inactive accounts cannot log in to the system
                  </p>
                </div>
              </div>
            </div>

            {/* Password Reset */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Password Reset</h3>
              <div className="max-w-md">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  New Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  disabled={isUpdating}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                  placeholder="Enter new password (leave blank to keep current)"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Leave blank to keep the current password unchanged
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onCancel}
                disabled={isUpdating}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isUpdating}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md shadow-sm hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isUpdating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Update User
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </DashboardCard>
    </div>
  );
};

export default CompanyUserEdit;
