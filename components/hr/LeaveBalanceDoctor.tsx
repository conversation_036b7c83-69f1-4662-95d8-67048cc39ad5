'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiPost } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';

interface DoctorAction {
  action: string;
  success: boolean;
  details: {
    success: boolean;
    message: string;
    created_balances?: Array<{
      employee_id: string;
      employee_name: string;
      leave_type: string;
      total_days: number;
      year: number;
    }>;
    skipped_employees?: Array<{
      employee_id: string;
      name: string;
      reason: string;
    }>;
  };
}

interface DoctorResults {
  actions_performed: DoctorAction[];
  company_id: string;
  dry_run: boolean;
  employee_id: string | null;
  summary: {
    employees_processed: number;
    total_issues_fixed: number;
    total_issues_found: number;
  };
  year: number;
}

interface DoctorResponse {
  code: number;
  extend: {
    doctor_results: DoctorResults;
  };
  msg: string;
}

interface LeaveBalanceDoctorProps {
  onClose?: () => void;
}

const LeaveBalanceDoctor: React.FC<LeaveBalanceDoctorProps> = ({ onClose }) => {
  const { companies } = useAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<DoctorResults | null>(null);
  const [error, setError] = useState('');
  const [selectedActions, setSelectedActions] = useState<string[]>(['initialize_missing']);
  const [dryRun, setDryRun] = useState(true);

  const availableActions = [
    {
      id: 'initialize_missing',
      name: 'Initialize Missing Balances',
      description: 'Create leave balances for employees who don\'t have them for the current year',
      recommended: true
    },
    {
      id: 'fix_inconsistencies',
      name: 'Fix Inconsistencies',
      description: 'Fix calculation errors and data inconsistencies in existing balances',
      recommended: false
    },
    {
      id: 'recalculate_available',
      name: 'Recalculate Available Days',
      description: 'Recalculate available days based on total, used, and pending days',
      recommended: false
    }
  ];

  const handleActionToggle = (actionId: string) => {
    setSelectedActions(prev => 
      prev.includes(actionId)
        ? prev.filter(id => id !== actionId)
        : [...prev, actionId]
    );
  };

  const runDoctor = async () => {
    if (selectedActions.length === 0) {
      setError('Please select at least one action to perform');
      return;
    }

    try {
      if (!companies || companies.length === 0) {
        throw new Error('Company information not available');
      }

      const companyId = companies[0].company_id;
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      setLoading(true);
      setError('');
      setResults(null);

      const payload = {
        company_id: companyId,
        actions: selectedActions,
        dry_run: dryRun
      };

      const response = await apiPost<DoctorResponse>(
        'api/leave/balances/doctor',
        payload,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.doctor_results) {
        setResults(response.extend.doctor_results);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error: any) {
      console.error('Error running leave balance doctor:', error);
      setError(error.message || 'Failed to run leave balance doctor');
    } finally {
      setLoading(false);
    }
  };

  const getActionIcon = (success: boolean) => {
    if (success) {
      return (
        <svg className="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      );
    } else {
      return (
        <svg className="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Leave Balance Doctor</h2>
          <p className="text-sm text-gray-600 mt-1">
            Diagnose and fix issues with employee leave balances
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Configuration */}
      <DashboardCard title="Configuration">
        <div className="space-y-6">
          {/* Actions Selection */}
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-3">Select Actions to Perform</h3>
            <div className="space-y-3">
              {availableActions.map((action) => (
                <div key={action.id} className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id={action.id}
                      type="checkbox"
                      checked={selectedActions.includes(action.id)}
                      onChange={() => handleActionToggle(action.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor={action.id} className="font-medium text-gray-700 flex items-center">
                      {action.name}
                      {action.recommended && (
                        <span className="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          Recommended
                        </span>
                      )}
                    </label>
                    <p className="text-gray-500">{action.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Dry Run Toggle */}
          <div className="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-center">
              <input
                id="dry_run"
                type="checkbox"
                checked={dryRun}
                onChange={(e) => setDryRun(e.target.checked)}
                className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
              />
              <div className="ml-3">
                <label htmlFor="dry_run" className="text-sm font-medium text-yellow-800">
                  Dry Run Mode
                </label>
                <p className="text-xs text-yellow-700">
                  Preview changes without actually modifying data
                </p>
              </div>
            </div>
            <svg className="h-5 w-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Run Button */}
          <div className="flex justify-end">
            <button
              onClick={runDoctor}
              disabled={loading || selectedActions.length === 0}
              className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Running Doctor...
                </>
              ) : (
                <>
                  <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {dryRun ? 'Preview Changes' : 'Run Doctor'}
                </>
              )}
            </button>
          </div>
        </div>
      </DashboardCard>

      {/* Results */}
      {results && (
        <DashboardCard title={`Doctor Results ${results.dry_run ? '(Preview Mode)' : ''}`}>
          <div className="space-y-6">
            {/* Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-900">{results.summary.employees_processed}</div>
                <div className="text-sm text-blue-700">Employees Processed</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-900">{results.summary.total_issues_fixed}</div>
                <div className="text-sm text-green-700">Issues Fixed</div>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-yellow-900">{results.summary.total_issues_found}</div>
                <div className="text-sm text-yellow-700">Issues Found</div>
              </div>
            </div>

            {/* Actions Performed */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Actions Performed</h3>
              <div className="space-y-4">
                {results.actions_performed.map((actionResult, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        {getActionIcon(actionResult.success)}
                        <h4 className="ml-2 text-sm font-medium text-gray-900">
                          {availableActions.find(a => a.id === actionResult.action)?.name || actionResult.action}
                        </h4>
                      </div>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        actionResult.success 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {actionResult.success ? 'Success' : 'Failed'}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">{actionResult.details.message}</p>

                    {/* Created Balances */}
                    {actionResult.details.created_balances && actionResult.details.created_balances.length > 0 && (
                      <div className="mt-3">
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Created Balances:</h5>
                        <div className="bg-gray-50 rounded-md p-3 max-h-40 overflow-y-auto">
                          {actionResult.details.created_balances.map((balance, idx) => (
                            <div key={idx} className="text-xs text-gray-600 py-1">
                              <span className="font-medium">{balance.employee_name}</span> - 
                              {balance.leave_type} ({balance.total_days} days, {balance.year})
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Skipped Employees */}
                    {actionResult.details.skipped_employees && actionResult.details.skipped_employees.length > 0 && (
                      <div className="mt-3">
                        <h5 className="text-sm font-medium text-gray-700 mb-2">Skipped Employees:</h5>
                        <div className="bg-yellow-50 rounded-md p-3 max-h-40 overflow-y-auto">
                          {actionResult.details.skipped_employees.map((employee, idx) => (
                            <div key={idx} className="text-xs text-yellow-700 py-1">
                              <span className="font-medium">{employee.name}</span> - {employee.reason}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </DashboardCard>
      )}
    </div>
  );
};

export default LeaveBalanceDoctor;
