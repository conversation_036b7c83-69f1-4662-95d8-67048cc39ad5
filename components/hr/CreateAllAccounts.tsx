'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  id_number?: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
}

interface AccountCreationResult {
  created_users: Array<{
    department: string | null;
    employee_id: string;
    employee_name: string;
    generated_password: string;
    login_username: string;
    role: string;
    username: string;
  }>;
  skipped_users: Array<{
    employee_id: string;
    employee_name: string;
    existing_username: string;
    reason: string;
  }>;
  failed_users: Array<{
    employee_id: string;
    employee_name: string;
    error: string;
    reason: string;
  }>;
  success: boolean;
  summary: {
    created: number;
    failed: number;
    skipped: number;
    total_employees: number;
  };
}

interface CreateAllAccountsProps {
  employees: Employee[];
  isLoadingEmployees: boolean;
  onResult: (result: AccountCreationResult) => void;
}

const CreateAllAccounts: React.FC<CreateAllAccountsProps> = ({
  employees,
  isLoadingEmployees,
  onResult
}) => {
  const { companies } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [error, setError] = useState('');

  // Create accounts for all employees
  const handleCreateAllAccounts = async () => {
    try {
      if (!companies || companies.length === 0) {
        setError('No company available');
        return;
      }

      setIsCreating(true);
      setError('');
      setShowConfirmation(false);

      const { apiPost } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const requestData = {
        company_id: companies[0].company_id
      };

      console.log('Creating accounts for all employees with data:', requestData);

      const response = await apiPost<AccountCreationResult>(
        'api/company-users/create-all',
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Create all accounts response:', response);

      if (response.success) {
        onResult(response);
      } else {
        setError('Failed to create accounts');
      }
    } catch (error: any) {
      console.error('Error creating all accounts:', error);
      setError(error.message || 'Failed to create accounts');
    } finally {
      setIsCreating(false);
    }
  };

  // Get employee statistics
  const activeEmployees = employees.filter(emp => emp.status === 'active');
  const inactiveEmployees = employees.filter(emp => emp.status !== 'active');

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-secondary-dark mb-4">
          Create Accounts for All Employees
        </h3>
        <p className="text-sm text-gray-600 mb-6">
          This will create login accounts for all employees in your company who don't already have accounts. 
          Employees who already have accounts will be automatically skipped.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Employee Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {isLoadingEmployees ? '...' : employees.length}
            </div>
            <div className="text-sm text-blue-700">Total Employees</div>
          </div>
        </div>
        
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {isLoadingEmployees ? '...' : activeEmployees.length}
            </div>
            <div className="text-sm text-green-700">Active Employees</div>
          </div>
        </div>
        
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">
              {isLoadingEmployees ? '...' : inactiveEmployees.length}
            </div>
            <div className="text-sm text-gray-700">Inactive Employees</div>
          </div>
        </div>
      </div>

      {/* Warning Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Important Notice</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <ul className="list-disc list-inside space-y-1">
                <li>This action will create accounts for ALL employees who don't have accounts yet</li>
                <li>Generated passwords will be sent to employees via email</li>
                <li>Employees who already have accounts will be skipped automatically</li>
                <li>All new accounts will be created with the "employee" role by default</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Employee Preview */}
      {!isLoadingEmployees && employees.length > 0 && (
        <div className="border border-gray-200 rounded-lg">
          <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
            <h4 className="text-sm font-medium text-gray-900">Employee Preview</h4>
          </div>
          <div className="max-h-48 overflow-y-auto">
            <div className="divide-y divide-gray-200">
              {employees.slice(0, 10).map((employee) => (
                <div key={employee.employee_id} className="px-4 py-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{employee.full_name}</div>
                      <div className="text-sm text-gray-500">
                        {employee.position || 'No Position'} • {employee.email || 'No Email'}
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      employee.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {employee.status}
                    </span>
                  </div>
                </div>
              ))}
              {employees.length > 10 && (
                <div className="px-4 py-3 text-center text-sm text-gray-500">
                  ... and {employees.length - 10} more employees
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      {showConfirmation && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">Confirm Account Creation</h3>
              <p className="mt-2 text-sm text-red-700">
                Are you sure you want to create accounts for all employees? This action cannot be undone.
              </p>
              <div className="mt-4 flex space-x-3">
                <button
                  onClick={handleCreateAllAccounts}
                  disabled={isCreating}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                >
                  {isCreating ? 'Creating...' : 'Yes, Create All Accounts'}
                </button>
                <button
                  onClick={() => setShowConfirmation(false)}
                  disabled={isCreating}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Button */}
      {!showConfirmation && (
        <div className="flex justify-end">
          <button
            onClick={() => setShowConfirmation(true)}
            disabled={isCreating || isLoadingEmployees || employees.length === 0}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Create Accounts for All Employees
          </button>
        </div>
      )}
    </div>
  );
};

export default CreateAllAccounts;
