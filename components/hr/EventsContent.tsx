'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import Link from 'next/link';

interface Event {
  id: string;
  title: string;
  type: 'birthday' | 'work_anniversary' | 'company_event' | 'holiday';
  date: string;
  employee_name?: string;
  description?: string;
}

const EventsContent: React.FC = () => {
  const { companies, user } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Mock data for now - in a real app, this would come from an API
  const mockEvents: Event[] = [
    {
      id: '1',
      title: '<PERSON>\'s Birthday',
      type: 'birthday',
      date: '2025-05-20',
      employee_name: '<PERSON>'
    },
    {
      id: '2',
      title: '<PERSON>\'s Work Anniversary',
      type: 'work_anniversary',
      date: '2025-05-25',
      employee_name: '<PERSON>',
      description: '2 years'
    },
    {
      id: '3',
      title: 'Company Picnic',
      type: 'company_event',
      date: '2025-06-15',
      description: 'Annual company picnic at Central Park'
    },
    {
      id: '4',
      title: 'Memorial Day',
      type: 'holiday',
      date: '2025-05-27',
      description: 'Public Holiday'
    },
    {
      id: '5',
      title: 'Robert <PERSON>\'s Birthday',
      type: 'birthday',
      date: '2025-06-10',
      employee_name: 'Robert Wilson'
    },
    {
      id: '6',
      title: 'Emily Davis\'s Work Anniversary',
      type: 'work_anniversary',
      date: '2025-06-05',
      employee_name: 'Emily Davis',
      description: '1 year'
    },
    {
      id: '7',
      title: 'Team Building Workshop',
      type: 'company_event',
      date: '2025-06-20',
      description: 'Team building activities and workshops'
    },
    {
      id: '8',
      title: 'Independence Day',
      type: 'holiday',
      date: '2025-07-04',
      description: 'Public Holiday'
    }
  ];

  useEffect(() => {
    // In a real app, this would fetch events from an API
    // For now, we'll use mock data
    setEvents(mockEvents);
    setIsLoading(false);
  }, []);

  // Function to get event type badge color
  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'birthday':
        return 'bg-blue-100 text-blue-800';
      case 'work_anniversary':
        return 'bg-purple-100 text-purple-800';
      case 'company_event':
        return 'bg-green-100 text-green-800';
      case 'holiday':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Function to get event type display name
  const getEventTypeDisplayName = (type: string) => {
    switch (type) {
      case 'birthday':
        return 'Birthday';
      case 'work_anniversary':
        return 'Work Anniversary';
      case 'company_event':
        return 'Company Event';
      case 'holiday':
        return 'Holiday';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Events Calendar</h1>
        <div>
          <button className="btn-primary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Event
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Events</span>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DashboardCard title="Upcoming Events">
          {isLoading ? (
            <div className="py-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="mt-2 text-secondary">Loading events...</p>
            </div>
          ) : events.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-secondary">No upcoming events found.</p>
            </div>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {events
                .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
                .map((event) => (
                  <div key={event.id} className="border-b border-gray-100 pb-4 last:border-0 last:pb-0">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-sm font-medium text-secondary-dark">{event.title}</h3>
                        <p className="text-xs text-secondary mt-1">{formatDate(event.date)}</p>
                        {event.description && (
                          <p className="text-xs text-secondary mt-1">{event.description}</p>
                        )}
                        {event.employee_name && (
                          <p className="text-xs text-secondary mt-1">Employee: {event.employee_name}</p>
                        )}
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${getEventTypeColor(event.type)}`}>
                        {getEventTypeDisplayName(event.type)}
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </DashboardCard>

        <DashboardCard title="Event Categories">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                <span className="text-sm font-medium text-secondary-dark">Birthdays</span>
              </div>
              <span className="text-sm text-secondary-dark">
                {events.filter(e => e.type === 'birthday').length}
              </span>
            </div>

            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                <span className="text-sm font-medium text-secondary-dark">Work Anniversaries</span>
              </div>
              <span className="text-sm text-secondary-dark">
                {events.filter(e => e.type === 'work_anniversary').length}
              </span>
            </div>

            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                <span className="text-sm font-medium text-secondary-dark">Company Events</span>
              </div>
              <span className="text-sm text-secondary-dark">
                {events.filter(e => e.type === 'company_event').length}
              </span>
            </div>

            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                <span className="text-sm font-medium text-secondary-dark">Holidays</span>
              </div>
              <span className="text-sm text-secondary-dark">
                {events.filter(e => e.type === 'holiday').length}
              </span>
            </div>
          </div>
        </DashboardCard>
      </div>

      <DashboardCard title="Monthly Calendar">
        <div className="py-8 text-center">
          <p className="text-secondary">Calendar view will be implemented in a future update.</p>
        </div>
      </DashboardCard>
    </div>
  );
};

export default EventsContent;
