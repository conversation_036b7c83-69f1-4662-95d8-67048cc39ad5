'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  id_number?: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
}

interface SingleAccountResult {
  employee_info: {
    department: string;
    employee_id: string;
    name: string;
  };
  generated_password: string;
  login_username: string;
  message: string;
  success: boolean;
  user: {
    created_at: string;
    email: string;
    employee: {
      department_id: string;
      first_name: string;
      full_name: string;
      id: string;
      last_name: string;
    };
    employee_id: string;
    first_name: string;
    full_name: string;
    id: string;
    is_active: boolean;
    last_name: string;
    phone_number: string | null;
    role: string;
    updated_at: string | null;
    username: string;
  };
}

interface CreateSingleAccountProps {
  employees: Employee[];
  isLoadingEmployees: boolean;
  onResult: (result: SingleAccountResult) => void;
}

const CreateSingleAccount: React.FC<CreateSingleAccountProps> = ({
  employees,
  isLoadingEmployees,
  onResult
}) => {
  const { companies } = useAuth();
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [selectedRole, setSelectedRole] = useState('employee');
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState('');

  const handleCreateAccount = async () => {
    try {
      if (!selectedEmployee) {
        setError('Please select an employee');
        return;
      }

      if (!companies || companies.length === 0) {
        setError('No company available');
        return;
      }

      setIsCreating(true);
      setError('');

      const { apiPost } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const requestData = {
        company_id: companies[0].company_id,
        employee_id: selectedEmployee,
        role: selectedRole
      };

      console.log('Creating account with data:', requestData);

      const response = await apiPost<SingleAccountResult>(
        'api/company-users',
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Account creation response:', response);

      if (response.success) {
        onResult(response);
        setSelectedEmployee('');
        setSelectedRole('employee');
      } else {
        setError(response.message || 'Failed to create account');
      }
    } catch (error: any) {
      console.error('Error creating account:', error);

      // Handle specific error cases
      if (error.message.includes('409') || error.message.toLowerCase().includes('conflict') || error.message.toLowerCase().includes('already exists')) {
        const selectedEmployeeData = employees.find(emp => emp.employee_id === selectedEmployee);
        setError(`Account already exists for ${selectedEmployeeData?.full_name || 'this employee'}. They can already log in to the system.`);
      } else {
        setError(error.message || 'Failed to create account');
      }
    } finally {
      setIsCreating(false);
    }
  };

  const selectedEmployeeData = employees.find(emp => emp.employee_id === selectedEmployee);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-secondary-dark mb-4">
          Create Account for Single Employee
        </h3>
        <p className="text-sm text-gray-600 mb-6">
          Select an employee and create a login account for them. They will receive their login credentials via email.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Employee Selection */}
        <div>
          <label className="block text-sm font-medium text-secondary-dark mb-2">
            Select Employee *
          </label>
          <select
            value={selectedEmployee}
            onChange={(e) => setSelectedEmployee(e.target.value)}
            disabled={isLoadingEmployees || isCreating}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <option value="">
              {isLoadingEmployees ? 'Loading employees...' : 'Select an employee'}
            </option>
            {employees.map((employee) => (
              <option key={employee.employee_id} value={employee.employee_id}>
                {employee.full_name} - {employee.position || 'No Position'} ({employee.status})
              </option>
            ))}
          </select>
        </div>

        {/* Role Selection */}
        <div>
          <label className="block text-sm font-medium text-secondary-dark mb-2">
            Account Role *
          </label>
          <select
            value={selectedRole}
            onChange={(e) => setSelectedRole(e.target.value)}
            disabled={isCreating}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <option value="employee">Employee</option>
            <option value="manager">Manager</option>
            <option value="hr">HR</option>
            <option value="admin">Admin</option>
          </select>
        </div>
      </div>

      {/* Selected Employee Preview */}
      {selectedEmployeeData && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Selected Employee</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-blue-700 font-medium">Name:</span> {selectedEmployeeData.full_name}
            </div>
            <div>
              <span className="text-blue-700 font-medium">Position:</span> {selectedEmployeeData.position || 'N/A'}
            </div>
            <div>
              <span className="text-blue-700 font-medium">Email:</span> {selectedEmployeeData.email || 'N/A'}
            </div>
            <div>
              <span className="text-blue-700 font-medium">Status:</span> {selectedEmployeeData.status}
            </div>
          </div>
        </div>
      )}

      {/* Action Button */}
      <div className="flex justify-end">
        <button
          onClick={handleCreateAccount}
          disabled={!selectedEmployee || isCreating || isLoadingEmployees}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isCreating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Creating Account...
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Create Account
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default CreateSingleAccount;
