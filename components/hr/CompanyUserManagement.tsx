'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import CompanyUserList from './CompanyUserList';
import CompanyUserDetails from './CompanyUserDetails';
import CompanyUserEdit from './CompanyUserEdit';

// Types for company user data
interface CompanyUser {
  created_at: string;
  email: string | null;
  employee: {
    department_id: string | null;
    first_name: string;
    full_name: string;
    id: string;
    last_name: string;
  };
  employee_id: string;
  first_name: string;
  full_name: string;
  id: string;
  is_active: boolean;
  last_name: string;
  login_username: string;
  phone_number: string | null;
  role: string;
  updated_at: string | null;
  username: string;
}

interface CompanyUsersResponse {
  pagination: {
    page: number;
    pages: number;
    per_page: number;
    total: number;
  };
  success: boolean;
  users: CompanyUser[];
}

interface SingleUserResponse {
  success: boolean;
  user: CompanyUser;
}

const CompanyUserManagement: React.FC = () => {
  const { companies } = useAuth();
  const [users, setUsers] = useState<CompanyUser[]>([]);
  const [selectedUser, setSelectedUser] = useState<CompanyUser | null>(null);
  const [activeView, setActiveView] = useState<'list' | 'details' | 'edit'>('list');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalUsers, setTotalUsers] = useState(0);

  // Fetch company users
  const fetchCompanyUsers = useCallback(async (page: number = 1, perPage: number = 10) => {
    try {
      if (!companies || companies.length === 0) {
        console.log('No companies available');
        return;
      }

      setIsLoading(true);
      setError('');

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        console.log('No access token available');
        setError('Authentication required');
        return;
      }

      console.log(`Fetching company users for company: ${companyId}, page: ${page}, per_page: ${perPage}`);

      const response = await apiGet<CompanyUsersResponse>(
        `api/company-users?company_id=${companyId}&page=${page}&per_page=${perPage}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      console.log('Company users response:', response);

      if (response.success && response.users) {
        setUsers(response.users);

        if (response.pagination) {
          setCurrentPage(response.pagination.page);
          setTotalPages(response.pagination.pages);
          setItemsPerPage(response.pagination.per_page);
          setTotalUsers(response.pagination.total);
        }
      } else {
        console.log('No users found in response');
        setError('No users found for this company');
      }
    } catch (error: any) {
      console.error('Error fetching company users:', error);
      setError(`Failed to fetch company users: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [companies]);

  // Fetch single user by ID
  const fetchUserById = useCallback(async (userId: string) => {
    try {
      if (!companies || companies.length === 0) return null;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) return null;

      const response = await apiGet<SingleUserResponse>(
        `api/company-users/${userId}?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.success && response.user) {
        return response.user;
      }

      return null;
    } catch (error: any) {
      console.error('Error fetching user by ID:', error);
      return null;
    }
  }, [companies]);

  // Fetch user by employee ID
  const fetchUserByEmployeeId = useCallback(async (employeeId: string) => {
    try {
      if (!companies || companies.length === 0) return null;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) return null;

      const response = await apiGet<SingleUserResponse>(
        `api/company-users/employee/${employeeId}?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.success && response.user) {
        return response.user;
      }

      return null;
    } catch (error: any) {
      console.error('Error fetching user by employee ID:', error);
      return null;
    }
  }, [companies]);

  // Update user
  const updateUser = useCallback(async (userId: string, updateData: any) => {
    try {
      if (!companies || companies.length === 0) {
        throw new Error('No company available');
      }

      const companyId = companies[0].company_id;
      const { apiPatch } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const requestData = {
        company_id: companyId,
        ...updateData
      };

      console.log('Updating user with data:', requestData);

      const response = await apiPatch<SingleUserResponse>(
        `api/company-users/${userId}`,
        requestData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('User update response:', response);

      if (response.success && response.user) {
        // Update the user in the local state
        setUsers(prevUsers =>
          prevUsers.map(user =>
            user.id === userId ? response.user : user
          )
        );

        // Update selected user if it's the one being edited
        if (selectedUser && selectedUser.id === userId) {
          setSelectedUser(response.user);
        }

        return response.user;
      } else {
        throw new Error('Failed to update user');
      }
    } catch (error: any) {
      console.error('Error updating user:', error);
      throw error;
    }
  }, [companies, selectedUser]);

  // Delete user
  const deleteUser = useCallback(async (userId: string) => {
    try {
      if (!companies || companies.length === 0) {
        throw new Error('No company available');
      }

      const companyId = companies[0].company_id;
      const { apiDelete } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      console.log(`Deleting user: ${userId}`);

      const response = await apiDelete<{success: boolean, message: string}>(
        `api/company-users/${userId}?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      console.log('User delete response:', response);

      if (response.success) {
        // Remove the user from the local state
        setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));

        // If the deleted user was selected, clear selection and go back to list
        if (selectedUser && selectedUser.id === userId) {
          setSelectedUser(null);
          setActiveView('list');
        }

        return true;
      } else {
        throw new Error(response.message || 'Failed to delete user');
      }
    } catch (error: any) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }, [companies, selectedUser]);

  // Load users on component mount
  useEffect(() => {
    fetchCompanyUsers(currentPage, itemsPerPage);
  }, [fetchCompanyUsers, currentPage, itemsPerPage]);

  // Handle view user details
  const handleViewUser = async (user: CompanyUser) => {
    setSelectedUser(user);
    setActiveView('details');
  };

  // Handle edit user
  const handleEditUser = async (user: CompanyUser) => {
    setSelectedUser(user);
    setActiveView('edit');
  };

  // Handle delete user
  const handleDeleteUser = async (user: CompanyUser) => {
    try {
      await deleteUser(user.id);
      // Refresh the list after successful deletion
      fetchCompanyUsers(currentPage, itemsPerPage);
    } catch (error: any) {
      setError(`Failed to delete user: ${error.message}`);
    }
  };

  // Handle back to list
  const handleBackToList = () => {
    setSelectedUser(null);
    setActiveView('list');
    // Refresh the list
    fetchCompanyUsers(currentPage, itemsPerPage);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">
            Company User Management
          </h1>
          <p className="text-gray-600 mt-1">
            View and manage employee login accounts
          </p>
        </div>
        <div className="flex space-x-3">
          {activeView !== 'list' && (
            <button
              onClick={handleBackToList}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to List
            </button>
          )}
          <Link
            href="/dashboard/hr/employees"
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Employees
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Content based on active view */}
      {activeView === 'list' && (
        <CompanyUserList
          users={users}
          isLoading={isLoading}
          currentPage={currentPage}
          totalPages={totalPages}
          totalUsers={totalUsers}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
          onViewUser={handleViewUser}
          onEditUser={handleEditUser}
          onDeleteUser={handleDeleteUser}
        />
      )}

      {activeView === 'details' && selectedUser && (
        <CompanyUserDetails
          user={selectedUser}
          onEdit={() => setActiveView('edit')}
          onBack={handleBackToList}
        />
      )}

      {activeView === 'edit' && selectedUser && (
        <CompanyUserEdit
          user={selectedUser}
          onUpdate={updateUser}
          onBack={handleBackToList}
          onCancel={() => setActiveView('details')}
        />
      )}
    </div>
  );
};

export default CompanyUserManagement;
