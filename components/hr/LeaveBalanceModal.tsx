'use client';

import React from 'react';

interface Employee {
  employee_id: string;
  full_name: string;
  email: string;
  position: string;
  department_name?: string;
}

interface LeaveType {
  leave_type_id: string;
  name: string;
  code: string;
  description: string;
  is_paid: boolean;
  requires_approval: boolean;
  requires_documentation: boolean;
  created_at: string;
  updated_at: string;
}

interface LeaveBalance {
  balance_id: string;
  employee_id: string;
  employee_name: string;
  leave_type_id: string;
  leave_type_name: string;
  leave_type_code: string;
  total_days: number;
  used_days: number;
  available_days: number;
  pending_days: number;
  carried_over_days: number;
  year: number;
  created_at: string;
  updated_at: string;
}

interface LeaveBalanceFormData {
  employee_id: string;
  leave_type_id: string;
  year: number;
  total_days: number;
  carried_over_days: number;
}

interface LeaveBalanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (e: React.FormEvent) => void;
  formData: LeaveBalanceFormData;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  employees: Employee[];
  leaveTypes: LeaveType[];
  editingBalance: LeaveBalance | null;
  submitting: boolean;
  error: string;
}

const LeaveBalanceModal: React.FC<LeaveBalanceModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  formData,
  onInputChange,
  employees,
  leaveTypes,
  editingBalance,
  submitting,
  error
}) => {
  if (!isOpen) return null;

  // Generate year options (current year ± 5 years)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  // Calculate available days
  const availableDays = formData.total_days + formData.carried_over_days;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <div className="relative w-full max-w-md bg-white rounded-2xl border border-gray-200 overflow-hidden">
        <div className="p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">
            {editingBalance ? 'Edit Leave Balance' : 'Create New Leave Balance'}
          </h3>

          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          )}

          <form onSubmit={onSubmit} className="space-y-4">
            {/* Employee Selection */}
            <div>
              <label htmlFor="employee_id" className="block text-sm font-medium text-gray-700 mb-1">
                Employee *
              </label>
              <select
                id="employee_id"
                name="employee_id"
                value={formData.employee_id}
                onChange={onInputChange}
                required
                disabled={!!editingBalance}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:cursor-not-allowed transition-colors"
              >
                <option value="">Select Employee</option>
                {employees.map(employee => (
                  <option key={employee.employee_id} value={employee.employee_id}>
                    {employee.full_name} - {employee.position}
                  </option>
                ))}
              </select>
            </div>

            {/* Leave Type Selection */}
            <div>
              <label htmlFor="leave_type_id" className="block text-sm font-medium text-gray-700 mb-1">
                Leave Type *
              </label>
              <select
                id="leave_type_id"
                name="leave_type_id"
                value={formData.leave_type_id}
                onChange={onInputChange}
                required
                disabled={!!editingBalance}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:cursor-not-allowed transition-colors"
              >
                <option value="">Select Leave Type</option>
                {leaveTypes.map(leaveType => (
                  <option key={leaveType.leave_type_id} value={leaveType.leave_type_id}>
                    {leaveType.name} ({leaveType.code})
                  </option>
                ))}
              </select>
            </div>

            {/* Year Selection */}
            <div>
              <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
                Year *
              </label>
              <select
                id="year"
                name="year"
                value={formData.year}
                onChange={onInputChange}
                required
                disabled={!!editingBalance}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:cursor-not-allowed transition-colors"
              >
                {yearOptions.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* Total Days */}
            <div>
              <label htmlFor="total_days" className="block text-sm font-medium text-gray-700 mb-1">
                Total Days *
              </label>
              <input
                type="number"
                id="total_days"
                name="total_days"
                value={formData.total_days}
                onChange={onInputChange}
                required
                min="0"
                max="365"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="e.g., 25"
              />
            </div>

            {/* Carried Over Days */}
            <div>
              <label htmlFor="carried_over_days" className="block text-sm font-medium text-gray-700 mb-1">
                Carried Over Days
              </label>
              <input
                type="number"
                id="carried_over_days"
                name="carried_over_days"
                value={formData.carried_over_days}
                onChange={onInputChange}
                min="0"
                max="365"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="e.g., 5"
              />
              <p className="text-xs text-gray-500 mt-1">
                Days carried over from previous year
              </p>
            </div>

            {/* Calculated Available Days */}
            {(formData.total_days > 0 || formData.carried_over_days > 0) && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center">
                  <svg className="h-5 w-5 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <p className="text-sm font-medium text-blue-800">
                      Total Available Days: {availableDays}
                    </p>
                    <p className="text-xs text-blue-600">
                      {formData.total_days} allocated + {formData.carried_over_days} carried over
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-100">
              <button
                type="button"
                onClick={onClose}
                disabled={submitting}
                className="px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={submitting || !formData.employee_id || !formData.leave_type_id || formData.total_days < 0}
                className="px-6 py-3 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {submitting ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {editingBalance ? 'Updating...' : 'Creating...'}
                  </div>
                ) : (
                  editingBalance ? 'Update Balance' : 'Create Balance'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LeaveBalanceModal;
