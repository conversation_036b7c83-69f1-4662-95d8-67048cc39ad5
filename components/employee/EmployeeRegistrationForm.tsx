'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getEmployeeTypesForCompany, areEmployeeTypesAvailable } from '@/lib/employee';
import { EmployeeType } from '@/types/payroll';
import DepartmentModal from '@/components/department/DepartmentModal';

// Define department interface
interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

// Define the employee registration response interface
interface EmployeeRegistrationResponse {
  extend: {
    employee: {
      created_at: string;
      department_id: string | null;
      email: string | null;
      employee_id: string;
      first_name: string;
      full_name: string;
      hire_date: string | null;
      id_number: string | null;
      last_name: string;
      phone_number: string | null;
      position: string | null;
      status: string;
      updated_at: string;
    };
  };
  msg: string;
}

interface EmployeeRegistrationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const EmployeeRegistrationForm: React.FC<EmployeeRegistrationFormProps> = ({
  onSuccess,
  onCancel
}) => {
  const { companies } = useAuth();
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    position: '',
    hire_date: '',
    id_number: '',
    department_id: '',
    employee_type_id: ''
  });
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employeeTypes, setEmployeeTypes] = useState<EmployeeType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [isLoadingEmployeeTypes, setIsLoadingEmployeeTypes] = useState(false);
  const [employeeTypesAvailable, setEmployeeTypesAvailable] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [isDepartmentModalOpen, setIsDepartmentModalOpen] = useState(false);

  // Fetch departments when component mounts
  useEffect(() => {
    fetchDepartments();
    fetchEmployeeTypes();
  }, [companies]);

  // Function to fetch employee types
  const fetchEmployeeTypes = async () => {
    try {
      setIsLoadingEmployeeTypes(true);

      // Check if employee types are available for this company
      const available = await areEmployeeTypesAvailable();
      setEmployeeTypesAvailable(available);

      if (available) {
        const types = await getEmployeeTypesForCompany();
        setEmployeeTypes(types);
        console.log(`Employee types loaded: ${types.length} types available`);
      }
    } catch (error) {
      console.error('Error fetching employee types:', error);
      // Don't show error for employee types as they're optional
      setEmployeeTypesAvailable(false);
      setEmployeeTypes([]);
    } finally {
      setIsLoadingEmployeeTypes(false);
    }
  };

  // Function to fetch departments
  const fetchDepartments = async () => {
    try {
      setIsLoadingDepartments(true);

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        console.error('No company found');
        setIsLoadingDepartments(false);
        return;
      }

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        console.error('Authentication required');
        setIsLoadingDepartments(false);
        return;
      }

      const response = await apiGet<{departments: Department[], success: boolean}>(`api/departments?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.departments) {
        setDepartments(response.departments);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Open department modal
  const openDepartmentModal = () => setIsDepartmentModalOpen(true);
  const closeDepartmentModal = () => setIsDepartmentModalOpen(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    // Basic validation
    if (!formData.first_name.trim()) {
      setError('First name is required');
      setIsLoading(false);
      return;
    }

    if (!formData.last_name.trim()) {
      setError('Last name is required');
      setIsLoading(false);
      return;
    }

    try {
      // Get the company ID from the first company in the list
      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        setError('No company found. Please register a company first.');
        setIsLoading(false);
        return;
      }

      // Prepare the data
      const employeeData: {
        company_id: string;
        first_name: string;
        last_name: string;
        email?: string;
        phone_number?: string;
        position?: string;
        hire_date?: string;
        id_number?: string;
        department_id?: string;
        employee_type_id?: string;
      } = {
        company_id: companyId,
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim()
      };

      // Add optional fields if they're not empty
      if (formData.email.trim()) employeeData.email = formData.email.trim();
      if (formData.phone_number.trim()) employeeData.phone_number = formData.phone_number.trim();
      if (formData.position.trim()) employeeData.position = formData.position.trim();
      if (formData.hire_date.trim()) employeeData.hire_date = formData.hire_date.trim();
      if (formData.id_number.trim()) employeeData.id_number = formData.id_number.trim();
      if (formData.department_id) employeeData.department_id = formData.department_id;
      if (formData.employee_type_id) employeeData.employee_type_id = formData.employee_type_id;

      // Log the data being sent
      console.log('Submitting employee data:', employeeData);

      // Import apiPost dynamically to avoid circular dependencies
      const { apiPost } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');

      // Get the access token
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      // Make the API call to register the employee
      const response = await apiPost<EmployeeRegistrationResponse>('api/employees', employeeData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });



      // Handle success
      setSuccessMessage(
        response.msg || 'Employee registered successfully!'
      );

      // Reset the form
      setFormData({
        first_name: '',
        last_name: '',
        email: '',
        phone_number: '',
        position: '',
        hire_date: '',
        id_number: '',
        department_id: '',
        employee_type_id: ''
      });

      // Call the onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (error: any) {
      console.error('Error registering employee:', error);

      // Use the error message from the API (which now includes proper error extraction)
      setError(error.message || 'Failed to register employee. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-xl border border-gray-200">
      {/* Department Modal */}
      <DepartmentModal
        isOpen={isDepartmentModalOpen}
        onClose={closeDepartmentModal}
        onSuccess={() => {
          closeDepartmentModal();
          fetchDepartments();
        }}
      />

      <h2 className="text-xl font-semibold text-secondary-dark mb-4">Register New Employee</h2>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-4">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm mb-4">
          {successMessage}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="first_name" className="block text-sm font-medium text-secondary-dark mb-1">
              First Name *
            </label>
            <input
              id="first_name"
              name="first_name"
              type="text"
              required
              value={formData.first_name}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter first name"
            />
          </div>

          <div>
            <label htmlFor="last_name" className="block text-sm font-medium text-secondary-dark mb-1">
              Last Name *
            </label>
            <input
              id="last_name"
              name="last_name"
              type="text"
              required
              value={formData.last_name}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter last name"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-secondary-dark mb-1">
              Email (Optional)
            </label>
            <input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter email address"
            />
          </div>

          <div>
            <label htmlFor="phone_number" className="block text-sm font-medium text-secondary-dark mb-1">
              Phone Number (Optional)
            </label>
            <input
              id="phone_number"
              name="phone_number"
              type="tel"
              value={formData.phone_number}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter phone number"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="position" className="block text-sm font-medium text-secondary-dark mb-1">
              Position (Optional)
            </label>
            <input
              id="position"
              name="position"
              type="text"
              value={formData.position}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter position"
            />
          </div>

          <div>
            <label htmlFor="hire_date" className="block text-sm font-medium text-secondary-dark mb-1">
              Hire Date (Optional)
            </label>
            <input
              id="hire_date"
              name="hire_date"
              type="date"
              value={formData.hire_date}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="flex justify-between items-center">
              <label htmlFor="department_id" className="block text-sm font-medium text-secondary-dark mb-1">
                Department (Optional)
              </label>
              <button
                type="button"
                onClick={openDepartmentModal}
                className="text-xs text-primary hover:text-primary-dark mb-1"
              >
                + Create New
              </button>
            </div>
            <select
              id="department_id"
              name="department_id"
              value={formData.department_id}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">Select Department</option>
              {isLoadingDepartments ? (
                <option disabled>Loading departments...</option>
              ) : departments.length === 0 ? (
                <option disabled>No departments available</option>
              ) : (
                departments.map(dept => (
                  <option key={dept.department_id} value={dept.department_id}>
                    {dept.name}
                  </option>
                ))
              )}
            </select>
          </div>

          {/* Employee Type Field - Always show since it's optional */}
          <div>
            <label htmlFor="employee_type_id" className="block text-sm font-medium text-secondary-dark mb-1">
              Employee Type (Optional)
            </label>
            <select
              id="employee_type_id"
              name="employee_type_id"
              value={formData.employee_type_id}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={isLoadingEmployeeTypes}
            >
              <option value="">Select Employee Type</option>
              {isLoadingEmployeeTypes ? (
                <option disabled>Loading employee types...</option>
              ) : employeeTypes.length === 0 ? (
                <option disabled>No employee types available for this country</option>
              ) : (
                employeeTypes.map(type => (
                  <option key={type.employee_type_id} value={type.employee_type_id}>
                    {type.name}
                  </option>
                ))
              )}
            </select>
            {!employeeTypesAvailable && employeeTypes.length === 0 && !isLoadingEmployeeTypes && (
              <p className="mt-1 text-sm text-gray-500">
                Employee types are not configured for your country or are not available for your subscription plan.
              </p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="id_number" className="block text-sm font-medium text-secondary-dark mb-1">
              ID Number (Optional)
            </label>
            <input
              id="id_number"
              name="id_number"
              type="text"
              value={formData.id_number}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter ID number"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-outline py-2 px-4"
              disabled={isLoading}
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary py-2 px-6 flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Registering...
              </>
            ) : (
              'Register Employee'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EmployeeRegistrationForm;
