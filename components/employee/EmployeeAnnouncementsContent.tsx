'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Announcement } from '@/types/announcement';
import { getEmployeeAnnouncements, getEmployeeAnnouncementById, acknowledgeAnnouncement, getUnreadAnnouncementsCount } from '@/lib/announcements';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import AnnouncementCard from '@/components/announcements/AnnouncementCard';

const EmployeeAnnouncementsContent = () => {
  const { companies } = useAuth();
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Filter states
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [unreadCount, setUnreadCount] = useState(0);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | null>(null);
  const [showAnnouncementModal, setShowAnnouncementModal] = useState(false);

  // Fetch announcements
  const fetchAnnouncements = async () => {
    try {
      setIsLoading(true);
      setError('');

      const options = {
        page: pagination.page,
        limit: pagination.limit,
        unread_only: showUnreadOnly,
        priority: priorityFilter !== 'all' ? priorityFilter : undefined
      };

      const response = await getEmployeeAnnouncements(options);

      // Filter announcements based on type filter (client-side for now)
      let filteredAnnouncements = response.announcements;

      if (typeFilter !== 'all') {
        filteredAnnouncements = filteredAnnouncements.filter(a => a.announcement_type === typeFilter);
      }

      // Sort by pinned status, then priority, then creation date
      filteredAnnouncements.sort((a, b) => {
        // First sort by pinned status
        if (a.is_pinned && !b.is_pinned) return -1;
        if (!a.is_pinned && b.is_pinned) return 1;

        // Then by priority
        const priorityOrder = { 'URGENT': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
        const aPriority = priorityOrder[a.priority] || 0;
        const bPriority = priorityOrder[b.priority] || 0;
        if (aPriority !== bPriority) return bPriority - aPriority;

        // Finally by creation date (newest first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });

      setAnnouncements(filteredAnnouncements);
      setPagination(response.pagination);
      setUnreadCount(response.unread_count);
    } catch (error: any) {
      console.error('Error fetching announcements:', error);
      setError(error.message || 'Failed to fetch announcements');
      setAnnouncements([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch announcements when component mounts or filters change
  useEffect(() => {
    fetchAnnouncements();
  }, [pagination.page, pagination.limit, priorityFilter, typeFilter, showUnreadOnly]);

  // Calculate stats - when filtering for unread only, show different stats
  const totalAnnouncements = showUnreadOnly ? unreadCount : announcements.length;
  const urgentAnnouncements = announcements.filter(a => a.priority === 'URGENT').length;
  const pinnedAnnouncements = announcements.filter(a => a.is_pinned).length;
  const requiresAcknowledgment = announcements.filter(a => a.requires_acknowledgment && (!showUnreadOnly || !a.is_acknowledged)).length;

  const stats = [
    {
      title: showUnreadOnly ? 'Unread Announcements' : 'Total Announcements',
      value: totalAnnouncements.toString(),
      change: '',
      changeType: 'neutral' as const
    },
    { title: 'Unread', value: unreadCount.toString(), change: '', changeType: 'negative' as const },
    { title: 'Urgent', value: urgentAnnouncements.toString(), change: '', changeType: 'negative' as const },
    { title: 'Requires Action', value: requiresAcknowledgment.toString(), change: '', changeType: 'neutral' as const },
  ];

  const handleAnnouncementClick = async (announcement: Announcement) => {
    try {
      // Get full announcement details and mark as read
      const fullAnnouncement = await getEmployeeAnnouncementById(announcement.announcement_id);
      setSelectedAnnouncement(fullAnnouncement);
      setShowAnnouncementModal(true);

      // Refresh the list to update read status
      fetchAnnouncements();
    } catch (error) {
      console.error('Error fetching announcement details:', error);
    }
  };

  const handleAcknowledgeAnnouncement = async (announcementId: string) => {
    try {
      const result = await acknowledgeAnnouncement(announcementId);
      console.log('Acknowledgment result:', result);

      // Update the selected announcement if it's the one being acknowledged
      if (selectedAnnouncement?.announcement_id === announcementId) {
        setSelectedAnnouncement({
          ...selectedAnnouncement,
          is_acknowledged: true,
          acknowledged_at: new Date().toISOString()
        });
      }

      // Update the announcements list to reflect the acknowledgment
      setAnnouncements(prev => prev.map(ann =>
        ann.announcement_id === announcementId
          ? { ...ann, is_acknowledged: true, acknowledged_at: new Date().toISOString() }
          : ann
      ));

      // Refresh the list to get updated counts
      fetchAnnouncements();

      // Show success message
      alert('Announcement acknowledged successfully!');
    } catch (error: any) {
      console.error('Error acknowledging announcement:', error);
      setError(error.message || 'Failed to acknowledge announcement');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Company Announcements</h1>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-gray-600">
        <Link href="/dashboard/employee" className="hover:text-blue-600">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-gray-900">Announcements</span>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            loading={isLoading}
          />
        ))}
      </div>

      {/* Filters */}
      <DashboardCard title="Filters">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="priority-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Priority
            </label>
            <select
              id="priority-filter"
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All</option>
              <option value="URGENT">Urgent</option>
              <option value="HIGH">High</option>
              <option value="MEDIUM">Medium</option>
              <option value="LOW">Low</option>
            </select>
          </div>

          <div>
            <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              id="type-filter"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All</option>
              <option value="GENERAL">General</option>
              <option value="POLICY">Policy</option>
              <option value="EVENT">Event</option>
              <option value="URGENT">Urgent</option>
              <option value="MAINTENANCE">Maintenance</option>
              <option value="CELEBRATION">Celebration</option>
              <option value="TRAINING">Training</option>
              <option value="MEETING">Meeting</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Show Only
            </label>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="unread-only"
                checked={showUnreadOnly}
                onChange={(e) => setShowUnreadOnly(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="unread-only" className="ml-2 text-sm text-gray-700">
                Unread announcements only
              </label>
            </div>
          </div>
        </div>
      </DashboardCard>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Announcements List */}
      <DashboardCard title="Announcements" loading={isLoading}>
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Loading announcements...</p>
          </div>
        ) : announcements.length === 0 ? (
          <div className="py-8 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
            </svg>
            <p className="text-gray-600">No announcements available</p>
            <p className="text-sm text-gray-500 mt-1">Check back later for company updates</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {announcements.map((announcement) => (
              <AnnouncementCard
                key={announcement.announcement_id}
                announcement={announcement}
                isEmployee={true}
                onClick={handleAnnouncementClick}
              />
            ))}
          </div>
        )}
      </DashboardCard>

      {/* Information Card */}
      <DashboardCard title="Need Help?">
        <div className="text-sm text-gray-600">
          <p className="mb-2">
            📢 <strong>Announcements</strong> are important company communications that may require your attention.
          </p>
          <p className="mb-2">
            ⚠️ <strong>Urgent announcements</strong> require immediate attention and may need acknowledgment.
          </p>
          <p className="mb-2">
            📌 <strong>Pinned announcements</strong> are highlighted for easy access.
          </p>
          <p>
            ✅ Some announcements may require you to acknowledge that you've read them. Look for the "Action Required" badge.
          </p>
        </div>
      </DashboardCard>

      {/* Announcement Detail Modal */}
      {showAnnouncementModal && selectedAnnouncement && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Header */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {selectedAnnouncement.title}
                  </h3>
                  <div className="flex items-center gap-2 mb-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      selectedAnnouncement.priority === 'URGENT' ? 'bg-red-100 text-red-800' :
                      selectedAnnouncement.priority === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                      selectedAnnouncement.priority === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {selectedAnnouncement.priority}
                    </span>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                      {selectedAnnouncement.announcement_type}
                    </span>
                    {selectedAnnouncement.is_pinned && (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                        📌 Pinned
                      </span>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => setShowAnnouncementModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Content */}
              <div className="mb-6">
                <div className="prose max-w-none">
                  <div className="text-gray-700 whitespace-pre-wrap">
                    {selectedAnnouncement.content || 'No content available'}
                  </div>
                </div>
              </div>

              {/* Acknowledgment Section */}
              {selectedAnnouncement.requires_acknowledgment && (
                <div className={`border rounded-md p-4 mb-4 ${
                  selectedAnnouncement.is_acknowledged
                    ? 'bg-green-50 border-green-200'
                    : 'bg-yellow-50 border-yellow-200'
                }`}>
                  <div className="flex items-center">
                    <svg className={`h-5 w-5 mr-2 ${
                      selectedAnnouncement.is_acknowledged
                        ? 'text-green-400'
                        : 'text-yellow-400'
                    }`} fill="currentColor" viewBox="0 0 20 20">
                      {selectedAnnouncement.is_acknowledged ? (
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      ) : (
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      )}
                    </svg>
                    <div className="flex-1">
                      <p className={`text-sm ${
                        selectedAnnouncement.is_acknowledged
                          ? 'text-green-800'
                          : 'text-yellow-800'
                      }`}>
                        {selectedAnnouncement.is_acknowledged
                          ? `Acknowledged on ${new Date(selectedAnnouncement.acknowledged_at || '').toLocaleDateString()}`
                          : 'This announcement requires acknowledgment.'
                        }
                      </p>
                    </div>
                    {!selectedAnnouncement.is_acknowledged && (
                      <button
                        onClick={() => handleAcknowledgeAnnouncement(selectedAnnouncement.announcement_id)}
                        className="ml-4 px-4 py-2 bg-yellow-600 text-white text-sm font-medium rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500"
                      >
                        Acknowledge
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* Footer */}
              <div className="text-xs text-gray-500 border-t pt-4">
                <div>Published: {new Date(selectedAnnouncement.publish_date || selectedAnnouncement.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</div>
                {selectedAnnouncement.expiry_date && (
                  <div>Expires: {new Date(selectedAnnouncement.expiry_date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeAnnouncementsContent;
