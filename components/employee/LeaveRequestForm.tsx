'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { apiPost, apiGet } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';

interface LeaveRequestData {
  leave_type: string;
  start_date: string;
  end_date: string;
  reason: string;
  emergency_contact?: string;
  emergency_phone?: string;
}

interface LeaveRequestPayload {
  company_id: string;
  employee_id: string;
  leave_type_id: string;
  start_date: string;
  end_date: string;
  reason: string;
  emergency_contact?: string;
  total_days: number;
}

interface LeaveRequestResponse {
  code: number;
  msg: string;
  extend?: {
    request_id: string;
    status: string;
  };
}

interface LeaveType {
  leave_type_id: string;
  name: string;
  code: string;
  description: string;
  is_paid: boolean;
  requires_approval: boolean;
  requires_documentation: boolean;
  created_at: string;
  updated_at: string;
}

interface LeaveTypesResponse {
  code: number;
  extend: {
    leave_types: LeaveType[];
  };
  msg: string;
}

interface LeaveBalance {
  balance_id: string;
  employee_id: string;
  leave_type_id: string;
  leave_type_name: string;
  total_days: number;
  used_days: number;
  available_days: number;
  pending_days: number;
  carried_over_days: number;
  year: number;
  created_at: string;
  updated_at: string;
}

interface LeaveBalancesResponse {
  code: number;
  extend: {
    employee: {
      employee_id: string;
      full_name: string;
      first_name: string;
      last_name: string;
      email: string;
      phone_number: string;
      position: string;
      department_id: string;
      hire_date: string;
      status: string;
      created_at: string;
      updated_at: string;
    };
    leave_balances: LeaveBalance[];
  };
  msg: string;
}

interface ExtendedUser {
  employee_id: string;
  employee_info: {
    employee_id: string;
    full_name: string;
  };
  id: string;
  name: string;
  role: string;
  username: string;
}

const LeaveRequestForm = () => {
  const router = useRouter();
  const { user, companies } = useAuth();
  const extendedUser = user as ExtendedUser;
  const [loading, setLoading] = useState(false);
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [loadingTypes, setLoadingTypes] = useState(true);
  const [loadingBalances, setLoadingBalances] = useState(true);
  const [selectedLeaveType, setSelectedLeaveType] = useState<LeaveType | null>(null);
  const [selectedBalance, setSelectedBalance] = useState<LeaveBalance | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState<LeaveRequestData>({
    leave_type: '',
    start_date: '',
    end_date: '',
    reason: '',
    emergency_contact: '',
    emergency_phone: ''
  });

  // Get company ID using the same robust method
  const getCompanyId = () => {
    // First try to get from companies array
    if (companies && companies.length > 0) {
      return companies[0].company_id;
    }

    // If companies array is empty, try to get from stored auth data
    const authData = localStorage.getItem('kazisync_auth');
    if (authData) {
      try {
        const parsedData = JSON.parse(authData);
        // Check if company object exists (from login response)
        if (parsedData.company && parsedData.company.company_id) {
          return parsedData.company.company_id;
        }
        // Also check if company_id is directly in the token payload
        else if (parsedData.company_id) {
          return parsedData.company_id;
        }
      } catch (error) {
        // Silently handle parsing errors
      }
    }
    return null;
  };

  // Fetch leave types
  const fetchLeaveTypes = async () => {
    try {
      const companyId = getCompanyId();
      const token = getAccessToken();

      if (!companyId || !token) {
        setLoadingTypes(false);
        return;
      }

      const response = await apiGet<LeaveTypesResponse>(
        `api/leave/types?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.leave_types) {
        setLeaveTypes(response.extend.leave_types);
      }
    } catch (error: any) {
      console.error('Error fetching leave types:', error);
    } finally {
      setLoadingTypes(false);
    }
  };

  // Fetch leave balances
  const fetchLeaveBalances = async () => {
    try {
      const companyId = getCompanyId();
      const token = getAccessToken();
      const employeeId = extendedUser?.employee_info?.employee_id;

      if (!companyId || !token || !employeeId) {
        setLoadingBalances(false);
        return;
      }

      const response = await apiGet<LeaveBalancesResponse>(
        `api/leave/balances?company_id=${companyId}&employee_id=${employeeId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.leave_balances) {
        setLeaveBalances(response.extend.leave_balances);
      }
    } catch (error: any) {
      console.error('Error fetching leave balances:', error);
    } finally {
      setLoadingBalances(false);
    }
  };

  useEffect(() => {
    fetchLeaveTypes();
    fetchLeaveBalances();
  }, [companies, extendedUser?.employee_info?.employee_id]);

  // Click outside handler for dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'leave_type') {
      const selectedType = leaveTypes.find(type => type.leave_type_id === value);
      const selectedBal = leaveBalances.find(balance => balance.leave_type_id === value);
      setSelectedLeaveType(selectedType || null);
      setSelectedBalance(selectedBal || null);
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLeaveTypeSelect = (leaveTypeId: string) => {
    const selectedType = leaveTypes.find(type => type.leave_type_id === leaveTypeId);
    const selectedBal = leaveBalances.find(balance => balance.leave_type_id === leaveTypeId);

    setSelectedLeaveType(selectedType || null);
    setSelectedBalance(selectedBal || null);
    setFormData(prev => ({
      ...prev,
      leave_type: leaveTypeId
    }));
    setDropdownOpen(false);
  };

  const calculateDays = () => {
    if (formData.start_date && formData.end_date) {
      const start = new Date(formData.start_date);
      const end = new Date(formData.end_date);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      return diffDays;
    }
    return 0;
  };

  const getValidationMessage = () => {
    const requestedDays = calculateDays();
    if (selectedBalance && requestedDays > 0) {
      if (requestedDays > selectedBalance.available_days) {
        return {
          type: 'error',
          message: `You only have ${selectedBalance.available_days} days available for ${selectedBalance.leave_type_name}. You're requesting ${requestedDays} days.`
        };
      } else if (requestedDays > selectedBalance.available_days * 0.8) {
        return {
          type: 'warning',
          message: `You're requesting ${requestedDays} out of ${selectedBalance.available_days} available days for ${selectedBalance.leave_type_name}.`
        };
      }
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!extendedUser?.employee_info?.employee_id) {
      setSubmitError('Employee information not available');
      return;
    }

    // Validate leave balance
    const validation = getValidationMessage();
    if (validation && validation.type === 'error') {
      setSubmitError(validation.message);
      return;
    }

    // Clear previous states
    setSubmitError(null);
    setSubmitSuccess(false);
    setLoading(true);

    try {
      const token = getAccessToken();
      const companyId = getCompanyId();
      const employeeId = extendedUser.employee_info.employee_id;

      if (!token || !companyId) {
        throw new Error('Authentication or company information not available');
      }

      const requestPayload: LeaveRequestPayload = {
        company_id: companyId,
        employee_id: employeeId,
        leave_type_id: formData.leave_type,
        start_date: formData.start_date,
        end_date: formData.end_date,
        reason: formData.reason,
        emergency_contact: formData.emergency_contact || undefined,
        total_days: calculateDays()
      };

      const response = await apiPost<LeaveRequestResponse>(
        'api/leave/requests',
        requestPayload,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.code === 100 || response.code === 200) {
        setSubmitSuccess(true);
        // Auto-redirect after 3 seconds
        setTimeout(() => {
          router.push('/dashboard/employee/leave');
        }, 3000);
      } else {
        throw new Error(response.msg || 'Failed to submit leave request');
      }
    } catch (error: any) {
      console.error('Error submitting leave request:', error);
      setSubmitError(error.message || 'Failed to submit leave request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <Link href="/dashboard/employee" className="hover:text-blue-600">Dashboard</Link>
            <span>/</span>
            <Link href="/dashboard/employee/leave" className="hover:text-blue-600">My Leave</Link>
            <span>/</span>
            <span className="text-gray-900">Request Leave</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Request Leave</h1>
        </div>
      </div>

      {/* Success Message */}
      {submitSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 animate-fade-in">
          <div className="flex items-center justify-center mb-4">
            <div className="relative">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-green-600 animate-bounce"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center animate-ping">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-green-900 mb-2">Leave Request Submitted Successfully!</h3>
            <p className="text-green-800 mb-4">
              Your leave request has been submitted and is now pending approval. You will be notified once it's reviewed.
            </p>
            <div className="flex items-center justify-center space-x-2 text-sm text-green-700">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span>Redirecting to leave dashboard in 3 seconds...</span>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {submitError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Submitting Leave Request</h3>
              <p className="mt-1 text-sm text-red-700">{submitError}</p>
              <div className="mt-3">
                <button
                  onClick={() => setSubmitError(null)}
                  className="text-sm font-medium text-red-800 hover:text-red-900 underline"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Leave Balances Summary */}
      {!submitSuccess && !loadingBalances && leaveBalances.length > 0 && (
        <DashboardCard title="Your Leave Balances">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {leaveBalances.map((balance) => (
              <div key={balance.balance_id} className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-900 mb-2">{balance.leave_type_name}</h3>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Available</span>
                    <span className="font-medium text-green-600">{balance.available_days} days</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Used</span>
                    <span className="font-medium text-red-600">{balance.used_days} days</span>
                  </div>
                  {balance.pending_days > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Pending</span>
                      <span className="font-medium text-yellow-600">{balance.pending_days} days</span>
                    </div>
                  )}
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${Math.min((balance.used_days / balance.total_days) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>
      )}

      {!submitSuccess && (
        <DashboardCard title="Leave Request Form">
        {(loadingTypes || loadingBalances) && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="text-sm text-blue-800">Loading leave types and balances...</span>
            </div>
          </div>
        )}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Leave Type */}
            <div className="md:col-span-2">
              <label htmlFor="leave_type" className="block text-sm font-medium text-gray-700 mb-2">
                Leave Type *
              </label>

              {/* Custom Enhanced Dropdown */}
              <div className="relative" ref={dropdownRef}>
                <button
                  type="button"
                  onClick={() => !loadingTypes && !loadingBalances && setDropdownOpen(!dropdownOpen)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      !loadingTypes && !loadingBalances && setDropdownOpen(!dropdownOpen);
                    } else if (e.key === 'Escape') {
                      setDropdownOpen(false);
                    }
                  }}
                  disabled={loadingTypes || loadingBalances}
                  className="w-full px-4 py-3 text-left bg-white border border-gray-300 rounded-lg shadow-sm hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      {loadingTypes || loadingBalances ? (
                        <div className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span className="text-gray-500">Loading leave types...</span>
                        </div>
                      ) : selectedLeaveType ? (
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">{selectedLeaveType.name}</div>
                            <div className="text-sm text-gray-500">
                              {selectedLeaveType.code} • {selectedBalance ? `${selectedBalance.available_days} days available` : 'No balance info'}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                              selectedLeaveType.is_paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {selectedLeaveType.is_paid ? 'Paid' : 'Unpaid'}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-500">Select a leave type</span>
                      )}
                    </div>
                    <svg className={`ml-2 h-5 w-5 text-gray-400 transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </button>

                {/* Dropdown Menu */}
                {dropdownOpen && !loadingTypes && !loadingBalances && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg max-h-80 overflow-auto">
                    {leaveTypes.length === 0 ? (
                      <div className="px-4 py-3 text-sm text-gray-500">No leave types available</div>
                    ) : (
                      leaveTypes.map((type) => {
                        const balance = leaveBalances.find(b => b.leave_type_id === type.leave_type_id);
                        const isSelected = formData.leave_type === type.leave_type_id;

                        return (
                          <button
                            key={type.leave_type_id}
                            type="button"
                            onClick={() => handleLeaveTypeSelect(type.leave_type_id)}
                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors ${
                              isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="font-medium text-gray-900">{type.name}</span>
                                  <span className="text-xs font-semibold px-2 py-1 bg-gray-100 text-gray-700 rounded-full">
                                    {type.code}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-600 mb-2 line-clamp-2">{type.description}</p>

                                {/* Balance Information */}
                                {balance && (
                                  <div className="flex items-center justify-between text-xs">
                                    <div className="flex items-center space-x-3">
                                      <span className="text-green-600 font-medium">
                                        {balance.available_days} available
                                      </span>
                                      <span className="text-gray-500">
                                        {balance.used_days} used
                                      </span>
                                      {balance.pending_days > 0 && (
                                        <span className="text-yellow-600">
                                          {balance.pending_days} pending
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                )}

                                {/* Leave Type Badges */}
                                <div className="flex flex-wrap gap-1 mt-2">
                                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                                    type.is_paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {type.is_paid ? 'Paid' : 'Unpaid'}
                                  </span>
                                  {type.requires_approval && (
                                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                      Approval Required
                                    </span>
                                  )}
                                  {type.requires_documentation && (
                                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                      Documentation Required
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </button>
                        );
                      })
                    )}
                  </div>
                )}
              </div>

              {/* Leave Type Information and Balance */}
              {selectedLeaveType && (
                <div className="mt-3 space-y-3">
                  {/* Leave Type Details */}
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">{selectedLeaveType.name}</h4>
                    <p className="text-sm text-blue-800 mb-2">{selectedLeaveType.description}</p>
                    <div className="flex flex-wrap gap-2">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        selectedLeaveType.is_paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {selectedLeaveType.is_paid ? 'Paid' : 'Unpaid'}
                      </span>
                      {selectedLeaveType.requires_approval && (
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Requires Approval
                        </span>
                      )}
                      {selectedLeaveType.requires_documentation && (
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                          Documentation Required
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Leave Balance Information */}
                  {selectedBalance && (
                    <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                      <h4 className="text-sm font-medium text-green-900 mb-2">Your Leave Balance</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                        <div>
                          <span className="text-gray-600">Total:</span>
                          <span className="ml-1 font-medium">{selectedBalance.total_days} days</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Used:</span>
                          <span className="ml-1 font-medium text-red-600">{selectedBalance.used_days} days</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Available:</span>
                          <span className="ml-1 font-medium text-green-600">{selectedBalance.available_days} days</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Pending:</span>
                          <span className="ml-1 font-medium text-yellow-600">{selectedBalance.pending_days} days</span>
                        </div>
                      </div>
                      {selectedBalance.carried_over_days > 0 && (
                        <div className="mt-2 text-sm text-gray-600">
                          <span>Carried over from previous year: {selectedBalance.carried_over_days} days</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Days Requested (calculated) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Days Requested
              </label>
              <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700">
                {calculateDays()} days
              </div>

              {/* Validation Message */}
              {(() => {
                const validation = getValidationMessage();
                if (validation) {
                  return (
                    <div className={`mt-2 p-2 rounded-md text-sm ${
                      validation.type === 'error'
                        ? 'bg-red-50 border border-red-200 text-red-800'
                        : 'bg-yellow-50 border border-yellow-200 text-yellow-800'
                    }`}>
                      <div className="flex items-start">
                        <svg className={`w-4 h-4 mr-2 mt-0.5 flex-shrink-0 ${
                          validation.type === 'error' ? 'text-red-500' : 'text-yellow-500'
                        }`} fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <span>{validation.message}</span>
                      </div>
                    </div>
                  );
                }
                return null;
              })()}
            </div>

            {/* Start Date */}
            <div>
              <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-2">
                Start Date *
              </label>
              <input
                type="date"
                id="start_date"
                name="start_date"
                value={formData.start_date}
                onChange={handleInputChange}
                required
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* End Date */}
            <div>
              <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-2">
                End Date *
              </label>
              <input
                type="date"
                id="end_date"
                name="end_date"
                value={formData.end_date}
                onChange={handleInputChange}
                required
                min={formData.start_date || new Date().toISOString().split('T')[0]}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Emergency Contact */}
            <div>
              <label htmlFor="emergency_contact" className="block text-sm font-medium text-gray-700 mb-2">
                Emergency Contact Name
              </label>
              <input
                type="text"
                id="emergency_contact"
                name="emergency_contact"
                value={formData.emergency_contact}
                onChange={handleInputChange}
                placeholder="Contact person during leave"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Emergency Phone */}
            <div>
              <label htmlFor="emergency_phone" className="block text-sm font-medium text-gray-700 mb-2">
                Emergency Contact Phone
              </label>
              <input
                type="tel"
                id="emergency_phone"
                name="emergency_phone"
                value={formData.emergency_phone}
                onChange={handleInputChange}
                placeholder="Phone number"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Reason */}
          <div>
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
              Reason for Leave *
            </label>
            <textarea
              id="reason"
              name="reason"
              value={formData.reason}
              onChange={handleInputChange}
              required
              rows={4}
              placeholder="Please provide a detailed reason for your leave request..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || (getValidationMessage()?.type === 'error')}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Submitting...' : 'Submit Request'}
            </button>
          </div>
        </form>
      </DashboardCard>
      )}
    </div>
  );
};

export default LeaveRequestForm;
