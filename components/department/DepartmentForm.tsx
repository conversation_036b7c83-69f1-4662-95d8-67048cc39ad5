'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

interface DepartmentFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  department?: Department | null;
  isEditing?: boolean;
}

const DepartmentForm: React.FC<DepartmentFormProps> = ({
  onSuccess,
  onCancel,
  department = null,
  isEditing = false
}) => {
  const { companies } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Initialize form data if editing an existing department
  useEffect(() => {
    if (isEditing && department) {
      setFormData({
        name: department.name,
        description: department.description || ''
      });
    }
  }, [isEditing, department]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    // Basic validation
    if (!formData.name.trim()) {
      setError('Department name is required');
      setIsLoading(false);
      return;
    }

    try {
      // Get the company ID from the first company in the list
      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;
      
      if (!companyId) {
        setError('No company found. Please register a company first.');
        setIsLoading(false);
        return;
      }

      // Prepare the data
      const departmentData = {
        company_id: companyId,
        name: formData.name.trim(),
        description: formData.description.trim()
      };
      
      // Log the data being sent


      // Import API functions dynamically to avoid circular dependencies
      const { apiPost, apiPut } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      
      // Get the access token
      const token = getAccessToken();
      
      if (!token) {
        throw new Error('Authentication required');
      }
      
      let response;
      
      if (isEditing && department) {
        // Update existing department
        response = await apiPut(`api/departments/${department.department_id}`, departmentData, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        setSuccessMessage('Department updated successfully!');
      } else {
        // Create new department
        response = await apiPost('api/departments', departmentData, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        setSuccessMessage('Department created successfully!');
      }
      
      console.log('Department operation response:', response);
      
      // Reset the form if not editing
      if (!isEditing) {
        setFormData({
          name: '',
          description: ''
        });
      }
      
      // Call the onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (error: any) {
      console.error('Error with department operation:', error);

      // Use the error message from the API (which now includes proper error extraction)
      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} department. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold text-secondary-dark mb-4">
        {isEditing ? 'Edit Department' : 'Create New Department'}
      </h2>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-4">
          {error}
        </div>
      )}
      
      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm mb-4">
          {successMessage}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-secondary-dark mb-1">
            Department Name *
          </label>
          <input
            id="name"
            name="name"
            type="text"
            required
            value={formData.name}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter department name"
          />
        </div>
        
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-secondary-dark mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter department description"
            rows={4}
          />
        </div>
        
        <div className="flex justify-end space-x-3 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn-outline py-2 px-4"
              disabled={isLoading}
            >
              Cancel
            </button>
          )}
          
          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary py-2 px-6 flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {isEditing ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              isEditing ? 'Update Department' : 'Create Department'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default DepartmentForm;
