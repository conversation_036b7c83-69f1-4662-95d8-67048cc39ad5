'use client';

import React, { useState } from 'react';
import { Shift } from '@/types/shift';
import EmployeeShiftAssignmentModal from './EmployeeShiftAssignmentModal';

interface ShiftAssignButtonProps {
  shift: Shift;
  onAssignmentSuccess?: () => void;
}

const ShiftAssignButton: React.FC<ShiftAssignButtonProps> = ({ 
  shift,
  onAssignmentSuccess 
}) => {
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  
  // Open assignment modal
  const openAssignModal = () => {
    setIsAssignModalOpen(true);
  };
  
  // Close assignment modal
  const closeAssignModal = () => {
    setIsAssignModalOpen(false);
  };
  
  // Handle successful assignment
  const handleAssignmentSuccess = () => {
    if (onAssignmentSuccess) {
      onAssignmentSuccess();
    }
    closeAssignModal();
  };
  
  return (
    <>
      <EmployeeShiftAssignmentModal
        isOpen={isAssignModalOpen}
        onClose={closeAssignModal}
        onSuccess={handleAssignmentSuccess}
        shift={shift}
        employee={null}
      />
      
      <button 
        onClick={openAssignModal}
        className="text-green-600 hover:text-green-800 text-sm"
      >
        Assign
      </button>
    </>
  );
};

export default ShiftAssignButton;
