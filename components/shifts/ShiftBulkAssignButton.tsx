'use client';

import React, { useState } from 'react';
import { Shift } from '@/types/shift';
import BulkEmployeeShiftAssignmentModal from './BulkEmployeeShiftAssignmentModal';

interface ShiftBulkAssignButtonProps {
  shift: Shift;
  onAssignmentSuccess?: () => void;
}

const ShiftBulkAssignButton: React.FC<ShiftBulkAssignButtonProps> = ({ 
  shift,
  onAssignmentSuccess 
}) => {
  const [isBulkAssignModalOpen, setIsBulkAssignModalOpen] = useState(false);
  
  // Open bulk assignment modal
  const openBulkAssignModal = () => {
    setIsBulkAssignModalOpen(true);
  };
  
  // Close bulk assignment modal
  const closeBulkAssignModal = () => {
    setIsBulkAssignModalOpen(false);
  };
  
  // Handle successful assignment
  const handleAssignmentSuccess = () => {
    if (onAssignmentSuccess) {
      onAssignmentSuccess();
    }
    closeBulkAssignModal();
  };
  
  return (
    <>
      <BulkEmployeeShiftAssignmentModal
        isOpen={isBulkAssignModalOpen}
        onClose={closeBulkAssignModal}
        onSuccess={handleAssignmentSuccess}
        shift={shift}
      />
      
      <button
        onClick={openBulkAssignModal}
        className="text-purple-600 hover:text-white hover:bg-purple-600 text-sm px-2 py-1 rounded transition-all duration-200 border border-purple-600 hover:border-purple-700"
        title="Bulk assign employees to this shift"
      >
        Bulk Assign
      </button>
    </>
  );
};

export default ShiftBulkAssignButton;
