'use client';

import React, { useState, useEffect } from 'react';
import DashboardCard from '@/components/ui/DashboardCard';
import { getSchedulingTemplateExamples, formatDaysOfWeek, formatTimeRange, getWeekendRequirementText, getCoveragePlanText } from '@/lib/advanced-shift-api';
import { TemplateExamplesResponse, TemplateExample } from '@/types/advanced-shift';

interface TemplateExamplesProps {
  onUseTemplate?: (example: TemplateExample, exampleKey: string) => void;
}

const TemplateExamples: React.FC<TemplateExamplesProps> = ({ onUseTemplate }) => {
  const [examples, setExamples] = useState<TemplateExamplesResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [expandedExample, setExpandedExample] = useState<string | null>(null);

  useEffect(() => {
    fetchTemplateExamples();
  }, []);

  const fetchTemplateExamples = async () => {
    try {
      setIsLoading(true);
      setError('');
      const response = await getSchedulingTemplateExamples();
      setExamples(response);
    } catch (error: any) {
      setError(error.message || 'Failed to fetch template examples');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleExpanded = (exampleKey: string) => {
    setExpandedExample(expandedExample === exampleKey ? null : exampleKey);
  };

  const handleUseTemplate = (example: TemplateExample, exampleKey: string) => {
    if (onUseTemplate) {
      onUseTemplate(example, exampleKey);
    }
  };

  const getExampleIcon = (key: string) => {
    switch (key) {
      case 'hospital_icu':
        return '🏥';
      case 'manufacturing_3_shift':
        return '🏭';
      case 'office_customer_service':
        return '🏢';
      default:
        return '📋';
    }
  };

  const getExampleColor = (key: string) => {
    switch (key) {
      case 'hospital_icu':
        return 'bg-red-50 border-red-200';
      case 'manufacturing_3_shift':
        return 'bg-blue-50 border-blue-200';
      case 'office_customer_service':
        return 'bg-green-50 border-green-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <DashboardCard title="Template Examples">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-secondary">Loading template examples...</span>
        </div>
      </DashboardCard>
    );
  }

  if (error) {
    return (
      <DashboardCard title="Template Examples">
        <div className="text-center py-8">
          <div className="text-red-600 mb-4">⚠️ {error}</div>
          <button
            onClick={fetchTemplateExamples}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            Retry
          </button>
        </div>
      </DashboardCard>
    );
  }

  if (!examples) {
    return (
      <DashboardCard title="Template Examples">
        <div className="text-center py-8 text-secondary">
          No template examples available
        </div>
      </DashboardCard>
    );
  }

  return (
    <DashboardCard title="Pre-built Template Examples">
      <div className="p-6">
        <div className="mb-6">
          <p className="text-secondary text-sm">
            {examples.usage.description}
          </p>
        </div>

        <div className="space-y-4">
          {Object.entries(examples.examples).map(([key, example]) => (
            <div
              key={key}
              className={`border rounded-lg overflow-hidden transition-all duration-200 ${getExampleColor(key)}`}
            >
              <div
                className="p-4 cursor-pointer hover:bg-opacity-80 transition-colors"
                onClick={() => toggleExpanded(key)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getExampleIcon(key)}</span>
                    <div>
                      <h3 className="font-semibold text-secondary-dark">{example.name}</h3>
                      <p className="text-sm text-secondary">
                        {example.pattern_type} • {example.business_requirements.shifts.length} shift{example.business_requirements.shifts.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {onUseTemplate && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleUseTemplate(example, key);
                        }}
                        className="px-3 py-1 bg-primary text-white text-sm rounded hover:bg-primary-dark transition-colors"
                      >
                        Use Template
                      </button>
                    )}
                    <span className="text-secondary">
                      {expandedExample === key ? '▼' : '▶'}
                    </span>
                  </div>
                </div>
              </div>

              {expandedExample === key && (
                <div className="border-t bg-white bg-opacity-50 p-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Schedule Pattern */}
                    <div>
                      <h4 className="font-medium text-secondary-dark mb-2">Schedule Pattern</h4>
                      <div className="text-sm space-y-1">
                        <p><span className="font-medium">Type:</span> {example.business_requirements.schedule_pattern.type}</p>
                        <p><span className="font-medium">Cycle Length:</span> {example.business_requirements.schedule_pattern.cycle_length} days</p>
                        {example.business_requirements.schedule_pattern.description && (
                          <p><span className="font-medium">Description:</span> {example.business_requirements.schedule_pattern.description}</p>
                        )}
                      </div>
                    </div>

                    {/* Work Rules */}
                    <div>
                      <h4 className="font-medium text-secondary-dark mb-2">Work Rules</h4>
                      <div className="text-sm space-y-1">
                        <p><span className="font-medium">Max Consecutive Days:</span> {example.business_requirements.work_rules.max_consecutive_days}</p>
                        <p><span className="font-medium">Max Hours/Week:</span> {example.business_requirements.work_rules.max_hours_per_week}</p>
                        <p><span className="font-medium">Min Rest Days:</span> {example.business_requirements.work_rules.min_rest_days}</p>
                        <p><span className="font-medium">Overtime:</span> {example.business_requirements.work_rules.overtime_allowed ? 'Allowed' : 'Not Allowed'}</p>
                        <p><span className="font-medium">Weekend:</span> {getWeekendRequirementText(example.business_requirements.work_rules.weekend_requirements)}</p>
                      </div>
                    </div>
                  </div>

                  {/* Shifts */}
                  <div className="mt-6">
                    <h4 className="font-medium text-secondary-dark mb-3">Shifts Configuration</h4>
                    <div className="space-y-3">
                      {example.business_requirements.shifts.map((shift, index) => (
                        <div key={index} className="bg-white rounded-lg p-3 border">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <p className="text-sm font-medium text-secondary-dark">Working Days</p>
                              <p className="text-sm text-secondary">{formatDaysOfWeek(shift.days_of_week)}</p>
                            </div>
                            {shift.time_range && (
                              <div>
                                <p className="text-sm font-medium text-secondary-dark">Time Range</p>
                                <p className="text-sm text-secondary">{formatTimeRange(shift.time_range.start_time, shift.time_range.end_time)}</p>
                              </div>
                            )}
                            <div>
                              <p className="text-sm font-medium text-secondary-dark">Staffing</p>
                              <p className="text-sm text-secondary">
                                Min: {shift.staffing.minimum_staff}, 
                                Preferred: {shift.staffing.preferred_staff}, 
                                Max: {shift.staffing.maximum_staff}
                              </p>
                            </div>
                          </div>
                          {shift.roles_required && shift.roles_required.length > 0 && (
                            <div className="mt-2">
                              <p className="text-sm font-medium text-secondary-dark">Role Requirements</p>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {shift.roles_required.map((role, roleIndex) => (
                                  <span key={roleIndex} className="text-xs bg-gray-100 px-2 py-1 rounded">
                                    {role.role}: {role.minimum}-{role.preferred}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Coverage Rules (if available) */}
                  {example.business_requirements.coverage_rules && (
                    <div className="mt-6">
                      <h4 className="font-medium text-secondary-dark mb-2">Coverage Rules</h4>
                      <div className="text-sm space-y-1">
                        <p><span className="font-medium">Allow Understaffing:</span> {example.business_requirements.coverage_rules.allow_understaffing ? 'Yes' : 'No'}</p>
                        <p><span className="font-medium">Emergency Plan:</span> {getCoveragePlanText(example.business_requirements.coverage_rules.emergency_coverage_plan)}</p>
                        <p><span className="font-medium">Min Coverage:</span> {example.business_requirements.coverage_rules.minimum_coverage_percentage}%</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Usage Instructions */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">How to Use These Templates</h4>
          <ol className="text-sm text-blue-800 space-y-1">
            {examples.usage.instructions.map((instruction, index) => (
              <li key={index}>{instruction}</li>
            ))}
          </ol>
        </div>
      </div>
    </DashboardCard>
  );
};

export default TemplateExamples;
