'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import { 
  analyzeTemplate, 
  formatDaysOfWeek, 
  formatTimeRange, 
  getWeekendRequirementText,
  getCoveragePlanText 
} from '@/lib/advanced-shift-api';
import { TemplateAnalysis, TemplateWithSummary } from '@/types/advanced-shift';

interface TemplateAnalysisProps {
  template: TemplateWithSummary;
  onClose?: () => void;
  onGenerateSchedule?: (template: TemplateWithSummary) => void;
}

const TemplateAnalysisComponent: React.FC<TemplateAnalysisProps> = ({
  template,
  onClose,
  onGenerateSchedule
}) => {
  const { companies } = useAuth();
  const [analysis, setAnalysis] = useState<TemplateAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState<'overview' | 'shifts' | 'rules' | 'coverage'>('overview');

  useEffect(() => {
    if (companies.length > 0 && template) {
      fetchAnalysis();
    }
  }, [companies, template]);

  const fetchAnalysis = async () => {
    try {
      setIsLoading(true);
      setError('');
      const companyId = companies[0].company_id;
      const response = await analyzeTemplate(template.template_id, companyId);
      setAnalysis(response.analysis);
    } catch (error: any) {
      setError(error.message || 'Failed to analyze template');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <DashboardCard title={`Analyzing: ${template.name}`}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-secondary">Analyzing template...</span>
        </div>
      </DashboardCard>
    );
  }

  if (error) {
    return (
      <DashboardCard title={`Analysis Error: ${template.name}`}>
        <div className="text-center py-8">
          <div className="text-red-600 mb-4">⚠️ {error}</div>
          <div className="space-x-2">
            <button
              onClick={fetchAnalysis}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              Retry Analysis
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-secondary rounded-lg hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
            )}
          </div>
        </div>
      </DashboardCard>
    );
  }

  if (!analysis) {
    return (
      <DashboardCard title={`Analysis: ${template.name}`}>
        <div className="text-center py-8 text-secondary">
          No analysis data available
        </div>
      </DashboardCard>
    );
  }

  return (
    <DashboardCard title={`Template Analysis: ${analysis.template_info.name}`}>
      <div className="p-6">
        {/* Header with validation status */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              analysis.validation_status[0] 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {analysis.validation_status[0] ? '✓ Valid' : '✗ Invalid'}
            </div>
            <span className="text-sm text-secondary">{analysis.validation_status[1]}</span>
          </div>
          <div className="flex space-x-2">
            {onGenerateSchedule && analysis.validation_status[0] && (
              <button
                onClick={() => onGenerateSchedule(template)}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Generate Schedule
              </button>
            )}
            {onClose && (
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-secondary rounded-lg hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
            )}
          </div>
        </div>

        {/* Template Info */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-secondary-dark">Created</p>
              <p className="text-sm text-secondary">{formatDate(analysis.template_info.created_at)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">Pattern Type</p>
              <p className="text-sm text-secondary capitalize">{analysis.template_info.pattern_type}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">Coverage Type</p>
              <p className="text-sm text-secondary">{analysis.is_24_7_coverage ? '24/7 Coverage' : 'Standard Coverage'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">Weekly Hours</p>
              <p className="text-sm text-secondary">{analysis.weekly_coverage_hours}h</p>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'shifts', label: 'Shifts Details' },
              { key: 'rules', label: 'Work Rules' },
              { key: 'coverage', label: 'Coverage Rules' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.key
                    ? 'border-primary text-primary'
                    : 'border-transparent text-secondary hover:text-secondary-dark hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Staffing Summary */}
            <div>
              <h3 className="font-medium text-secondary-dark mb-4">Staffing Summary</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-secondary-dark mb-3">Weekly Totals</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Minimum Staff Hours:</span>
                      <span className="font-medium">{analysis.staffing_summary.weekly_totals.min_staff_hours}h</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Preferred Staff Hours:</span>
                      <span className="font-medium">{analysis.staffing_summary.weekly_totals.preferred_staff_hours}h</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Maximum Staff Hours:</span>
                      <span className="font-medium">{analysis.staffing_summary.weekly_totals.max_staff_hours}h</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Total Shifts:</span>
                      <span className="font-medium">{analysis.staffing_summary.total_shifts}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-secondary-dark mb-3">Daily Staffing Requirements</h4>
                  <div className="grid grid-cols-7 gap-1">
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => {
                      const dayNum = (index + 1).toString();
                      const dayData = analysis.staffing_summary.daily_totals[dayNum];
                      return (
                        <div key={day} className="text-center p-2 bg-white rounded border">
                          <div className="text-xs font-medium text-secondary-dark mb-1">{day}</div>
                          <div className="text-xs space-y-1">
                            <div>Min: {dayData.min_staff}</div>
                            <div>Pref: {dayData.preferred_staff}</div>
                            <div>Max: {dayData.max_staff}</div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>

            {/* Shift Details Summary */}
            <div>
              <h3 className="font-medium text-secondary-dark mb-4">Shift Details</h3>
              <div className="space-y-3">
                {analysis.staffing_summary.shift_details.map((shift, index) => (
                  <div key={index} className="bg-white border rounded-lg p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm font-medium text-secondary-dark">Shift ID</p>
                        <p className="text-sm text-secondary font-mono">{shift.shift_id}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-secondary-dark">Days Count</p>
                        <p className="text-sm text-secondary">{shift.days_count} days per week</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-secondary-dark">Staffing Range</p>
                        <p className="text-sm text-secondary">
                          {shift.staffing.minimum_staff} - {shift.staffing.maximum_staff} staff
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'shifts' && (
          <div className="space-y-4">
            <h3 className="font-medium text-secondary-dark mb-4">Detailed Shift Configuration</h3>
            {analysis.business_requirements.shifts.map((shift, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-secondary-dark mb-3">Shift Information</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Name:</span>
                        <span className="font-medium">{shift.shift_details.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Time Range:</span>
                        <span className="font-medium">
                          {formatTimeRange(shift.shift_details.start_time, shift.shift_details.end_time)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Working Days:</span>
                        <span className="font-medium">{formatDaysOfWeek(shift.days_of_week)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Night Shift:</span>
                        <span className="font-medium">{shift.shift_details.is_night_shift ? 'Yes' : 'No'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Break Duration:</span>
                        <span className="font-medium">{shift.break_duration || 0} minutes</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Overtime Eligible:</span>
                        <span className="font-medium">{shift.overtime_eligible ? 'Yes' : 'No'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-secondary-dark mb-3">Staffing Requirements</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Minimum Staff:</span>
                        <span className="font-medium">{shift.staffing.minimum_staff}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Preferred Staff:</span>
                        <span className="font-medium">{shift.staffing.preferred_staff}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Maximum Staff:</span>
                        <span className="font-medium">{shift.staffing.maximum_staff}</span>
                      </div>
                    </div>

                    {shift.roles_required && shift.roles_required.length > 0 && (
                      <div className="mt-4">
                        <h5 className="font-medium text-secondary-dark mb-2">Role Requirements</h5>
                        <div className="space-y-1">
                          {shift.roles_required.map((role, roleIndex) => (
                            <div key={roleIndex} className="flex justify-between text-sm">
                              <span>{role.role}:</span>
                              <span className="font-medium">{role.minimum} - {role.preferred}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {shift.shift_details.description && (
                  <div className="mt-4 pt-4 border-t">
                    <p className="text-sm text-secondary">{shift.shift_details.description}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {activeTab === 'rules' && (
          <div className="space-y-6">
            <div>
              <h3 className="font-medium text-secondary-dark mb-4">Work Rules Configuration</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Max Consecutive Days:</span>
                    <span className="text-sm font-medium">{analysis.work_rules_summary.max_consecutive_days}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Min Rest Days:</span>
                    <span className="text-sm font-medium">{analysis.work_rules_summary.min_rest_days}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Max Hours per Week:</span>
                    <span className="text-sm font-medium">{analysis.work_rules_summary.max_hours_per_week}h</span>
                  </div>
                  {analysis.work_rules_summary.max_hours_per_day && (
                    <div className="flex justify-between">
                      <span className="text-sm">Max Hours per Day:</span>
                      <span className="text-sm font-medium">{analysis.work_rules_summary.max_hours_per_day}h</span>
                    </div>
                  )}
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Overtime Allowed:</span>
                    <span className="text-sm font-medium">{analysis.work_rules_summary.overtime_allowed ? 'Yes' : 'No'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Weekend Requirements:</span>
                    <span className="text-sm font-medium">{getWeekendRequirementText(analysis.work_rules_summary.weekend_requirements)}</span>
                  </div>
                  {analysis.business_requirements.work_rules.minimum_turnaround_hours && (
                    <div className="flex justify-between">
                      <span className="text-sm">Min Turnaround Hours:</span>
                      <span className="text-sm font-medium">{analysis.business_requirements.work_rules.minimum_turnaround_hours}h</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Employee Preferences */}
            {analysis.business_requirements.employee_preferences && (
              <div>
                <h3 className="font-medium text-secondary-dark mb-4">Employee Preferences</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Consider Employee Requests:</span>
                    <span className="text-sm font-medium">
                      {analysis.business_requirements.employee_preferences.consider_employee_requests ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Fair Rotation:</span>
                    <span className="text-sm font-medium">
                      {analysis.business_requirements.employee_preferences.fair_rotation ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Seniority Priority:</span>
                    <span className="text-sm font-medium">
                      {analysis.business_requirements.employee_preferences.seniority_priority ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'coverage' && (
          <div className="space-y-6">
            {analysis.business_requirements.coverage_rules && (
              <div>
                <h3 className="font-medium text-secondary-dark mb-4">Coverage Rules</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm">Allow Understaffing:</span>
                    <span className="text-sm font-medium">
                      {analysis.business_requirements.coverage_rules.allow_understaffing ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Emergency Coverage Plan:</span>
                    <span className="text-sm font-medium">
                      {getCoveragePlanText(analysis.business_requirements.coverage_rules.emergency_coverage_plan)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Minimum Coverage Percentage:</span>
                    <span className="text-sm font-medium">
                      {analysis.business_requirements.coverage_rules.minimum_coverage_percentage}%
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Schedule Pattern */}
            <div>
              <h3 className="font-medium text-secondary-dark mb-4">Schedule Pattern</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Pattern Type:</span>
                  <span className="text-sm font-medium capitalize">{analysis.business_requirements.schedule_pattern.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Cycle Length:</span>
                  <span className="text-sm font-medium">{analysis.business_requirements.schedule_pattern.cycle_length} days</span>
                </div>
                {analysis.business_requirements.schedule_pattern.description && (
                  <div>
                    <span className="text-sm">Description:</span>
                    <p className="text-sm font-medium mt-1">{analysis.business_requirements.schedule_pattern.description}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardCard>
  );
};

export default TemplateAnalysisComponent;
