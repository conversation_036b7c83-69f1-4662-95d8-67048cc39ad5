'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import { 
  getAvailableShifts, 
  validateBusinessRequirements, 
  createSchedulingTemplate,
  formatDaysOfWeek,
  formatTimeRange
} from '@/lib/advanced-shift-api';
import { 
  CreateTemplateRequest, 
  BusinessRequirements, 
  ShiftRequirement, 
  WorkRules,
  AvailableShift,
  TemplateExample
} from '@/types/advanced-shift';

interface TemplateBuilderProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  prefilledData?: TemplateExample | null;
}

const TemplateBuilder: React.FC<TemplateBuilderProps> = ({ 
  onSuccess, 
  onCancel, 
  prefilledData 
}) => {
  const { companies } = useAuth();
  const [availableShifts, setAvailableShifts] = useState<AvailableShift[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);

  // Form state
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [patternType, setPatternType] = useState<'weekly' | 'monthly' | 'custom'>('weekly');
  const [cycleLength, setCycleLength] = useState(7);
  const [patternDescription, setPatternDescription] = useState('');

  // Work Rules state
  const [maxConsecutiveDays, setMaxConsecutiveDays] = useState(5);
  const [minRestDays, setMinRestDays] = useState(2);
  const [maxHoursPerWeek, setMaxHoursPerWeek] = useState(40);
  const [maxHoursPerDay, setMaxHoursPerDay] = useState(8);
  const [overtimeAllowed, setOvertimeAllowed] = useState(true);
  const [weekendRequirements, setWeekendRequirements] = useState<'flexible' | 'both_off' | 'at_least_one_off' | 'both_required'>('flexible');
  const [minimumTurnaroundHours, setMinimumTurnaroundHours] = useState(8);

  // Coverage Rules state
  const [allowUnderstaffing, setAllowUnderstaffing] = useState(false);
  const [emergencyCoveragePlan, setEmergencyCoveragePlan] = useState('call_in_overtime');
  const [minimumCoveragePercentage, setMinimumCoveragePercentage] = useState(90);

  // Employee Preferences state
  const [considerEmployeeRequests, setConsiderEmployeeRequests] = useState(true);
  const [fairRotation, setFairRotation] = useState(true);
  const [seniorityPriority, setSeniorityPriority] = useState(false);

  // Shifts state
  const [shiftRequirements, setShiftRequirements] = useState<ShiftRequirement[]>([]);

  useEffect(() => {
    if (companies.length > 0) {
      fetchAvailableShifts();
    }
  }, [companies]);

  useEffect(() => {
    if (prefilledData) {
      populateFromExample(prefilledData);
    }
  }, [prefilledData]);

  const fetchAvailableShifts = async () => {
    try {
      const companyId = companies[0].company_id;
      const response = await getAvailableShifts(companyId);
      setAvailableShifts(response.shifts);
    } catch (error: any) {
      setError('Failed to fetch available shifts: ' + error.message);
    }
  };

  const populateFromExample = (example: TemplateExample) => {
    setTemplateName(example.name);
    setPatternType(example.pattern_type as 'weekly' | 'monthly' | 'custom');
    setCycleLength(example.business_requirements.schedule_pattern.cycle_length);
    setPatternDescription(example.business_requirements.schedule_pattern.description || '');

    // Work rules
    const workRules = example.business_requirements.work_rules;
    setMaxConsecutiveDays(workRules.max_consecutive_days);
    setMinRestDays(workRules.min_rest_days);
    setMaxHoursPerWeek(workRules.max_hours_per_week);
    setMaxHoursPerDay(workRules.max_hours_per_day || 8);
    setOvertimeAllowed(workRules.overtime_allowed);
    setWeekendRequirements(workRules.weekend_requirements as any);
    setMinimumTurnaroundHours(workRules.minimum_turnaround_hours || 8);

    // Coverage rules
    if (example.business_requirements.coverage_rules) {
      const coverageRules = example.business_requirements.coverage_rules;
      setAllowUnderstaffing(coverageRules.allow_understaffing);
      setEmergencyCoveragePlan(coverageRules.emergency_coverage_plan);
      setMinimumCoveragePercentage(coverageRules.minimum_coverage_percentage);
    }

    // Employee preferences
    if (example.business_requirements.employee_preferences) {
      const empPrefs = example.business_requirements.employee_preferences;
      setConsiderEmployeeRequests(empPrefs.consider_employee_requests);
      setFairRotation(empPrefs.fair_rotation);
      setSeniorityPriority(empPrefs.seniority_priority);
    }

    // Shifts (will need to be mapped to actual shift IDs)
    setShiftRequirements(example.business_requirements.shifts.map(shift => ({
      ...shift,
      shift_id: '' // Will need to be selected by user
    })));
  };

  const addShiftRequirement = () => {
    const newShift: ShiftRequirement = {
      shift_id: '',
      days_of_week: [1, 2, 3, 4, 5], // Monday to Friday
      staffing: {
        minimum_staff: 1,
        preferred_staff: 2,
        maximum_staff: 3
      },
      break_duration: 60,
      overtime_eligible: true
    };
    setShiftRequirements([...shiftRequirements, newShift]);
  };

  const updateShiftRequirement = (index: number, updates: Partial<ShiftRequirement>) => {
    const updated = [...shiftRequirements];
    updated[index] = { ...updated[index], ...updates };
    setShiftRequirements(updated);
  };

  const removeShiftRequirement = (index: number) => {
    setShiftRequirements(shiftRequirements.filter((_, i) => i !== index));
  };

  const validateTemplate = async () => {
    if (!templateName.trim()) {
      setError('Template name is required');
      return false;
    }

    if (shiftRequirements.length === 0) {
      setError('At least one shift requirement is needed');
      return false;
    }

    // Check if all shifts have valid shift_id
    const invalidShifts = shiftRequirements.filter(shift => !shift.shift_id);
    if (invalidShifts.length > 0) {
      setError('All shift requirements must have a selected shift');
      return false;
    }

    try {
      const businessRequirements: BusinessRequirements = {
        schedule_pattern: {
          type: patternType,
          cycle_length: cycleLength,
          description: patternDescription
        },
        shifts: shiftRequirements,
        work_rules: {
          max_consecutive_days: maxConsecutiveDays,
          min_rest_days: minRestDays,
          max_hours_per_week: maxHoursPerWeek,
          max_hours_per_day: maxHoursPerDay,
          overtime_allowed: overtimeAllowed,
          weekend_requirements: weekendRequirements,
          minimum_turnaround_hours: minimumTurnaroundHours
        },
        coverage_rules: {
          allow_understaffing: allowUnderstaffing,
          emergency_coverage_plan: emergencyCoveragePlan,
          minimum_coverage_percentage: minimumCoveragePercentage
        },
        employee_preferences: {
          consider_employee_requests: considerEmployeeRequests,
          fair_rotation: fairRotation,
          seniority_priority: seniorityPriority
        }
      };

      const companyId = companies[0].company_id;
      const validation = await validateBusinessRequirements({
        company_id: companyId,
        business_requirements: businessRequirements
      });

      setValidationErrors(validation.errors || []);
      setValidationWarnings(validation.warnings || []);

      return validation.valid;
    } catch (error: any) {
      setError('Validation failed: ' + error.message);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const isValid = await validateTemplate();
    if (!isValid) return;

    try {
      setIsLoading(true);
      setError('');

      const businessRequirements: BusinessRequirements = {
        schedule_pattern: {
          type: patternType,
          cycle_length: cycleLength,
          description: patternDescription
        },
        shifts: shiftRequirements,
        work_rules: {
          max_consecutive_days: maxConsecutiveDays,
          min_rest_days: minRestDays,
          max_hours_per_week: maxHoursPerWeek,
          max_hours_per_day: maxHoursPerDay,
          overtime_allowed: overtimeAllowed,
          weekend_requirements: weekendRequirements,
          minimum_turnaround_hours: minimumTurnaroundHours
        },
        coverage_rules: {
          allow_understaffing: allowUnderstaffing,
          emergency_coverage_plan: emergencyCoveragePlan,
          minimum_coverage_percentage: minimumCoveragePercentage
        },
        employee_preferences: {
          consider_employee_requests: considerEmployeeRequests,
          fair_rotation: fairRotation,
          seniority_priority: seniorityPriority
        }
      };

      const templateData: CreateTemplateRequest = {
        company_id: companies[0].company_id,
        name: templateName,
        description: templateDescription,
        pattern_type: patternType,
        business_requirements: businessRequirements
      };

      await createSchedulingTemplate(templateData);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      setError(error.message || 'Failed to create template');
    } finally {
      setIsLoading(false);
    }
  };

  const getSelectedShiftInfo = (shiftId: string) => {
    return availableShifts.find(shift => shift.shift_id === shiftId);
  };

  return (
    <DashboardCard title={prefilledData ? `Create Template from ${prefilledData.name}` : "Create Scheduling Template"}>
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="text-red-800 text-sm">{error}</div>
          </div>
        )}

        {validationErrors.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-medium text-red-800 mb-2">Validation Errors:</h4>
            <ul className="text-red-700 text-sm space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {validationWarnings.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-800 mb-2">Warnings:</h4>
            <ul className="text-yellow-700 text-sm space-y-1">
              {validationWarnings.map((warning, index) => (
                <li key={index}>• {warning}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary-dark mb-2">
              Template Name *
            </label>
            <input
              type="text"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter template name"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-secondary-dark mb-2">
              Pattern Type
            </label>
            <select
              value={patternType}
              onChange={(e) => setPatternType(e.target.value as 'weekly' | 'monthly' | 'custom')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="custom">Custom</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-secondary-dark mb-2">
            Description
          </label>
          <textarea
            value={templateDescription}
            onChange={(e) => setTemplateDescription(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            rows={3}
            placeholder="Describe this template's purpose and use case"
          />
        </div>

        {/* Schedule Pattern */}
        <div className="border rounded-lg p-4">
          <h3 className="font-medium text-secondary-dark mb-4">Schedule Pattern</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Cycle Length (days)
              </label>
              <input
                type="number"
                value={cycleLength}
                onChange={(e) => setCycleLength(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                min="1"
                max="365"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Pattern Description
              </label>
              <input
                type="text"
                value={patternDescription}
                onChange={(e) => setPatternDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Optional description"
              />
            </div>
          </div>
        </div>

        {/* Work Rules */}
        <div className="border rounded-lg p-4">
          <h3 className="font-medium text-secondary-dark mb-4">Work Rules</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Max Consecutive Days
              </label>
              <input
                type="number"
                value={maxConsecutiveDays}
                onChange={(e) => setMaxConsecutiveDays(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                min="1"
                max="14"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Min Rest Days
              </label>
              <input
                type="number"
                value={minRestDays}
                onChange={(e) => setMinRestDays(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                min="0"
                max="7"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Max Hours/Week
              </label>
              <input
                type="number"
                value={maxHoursPerWeek}
                onChange={(e) => setMaxHoursPerWeek(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                min="1"
                max="168"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Max Hours/Day
              </label>
              <input
                type="number"
                value={maxHoursPerDay}
                onChange={(e) => setMaxHoursPerDay(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                min="1"
                max="24"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Weekend Requirements
              </label>
              <select
                value={weekendRequirements}
                onChange={(e) => setWeekendRequirements(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="flexible">Flexible</option>
                <option value="both_off">Both Days Off</option>
                <option value="at_least_one_off">At Least One Day Off</option>
                <option value="both_required">Both Days Required</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Min Turnaround Hours
              </label>
              <input
                type="number"
                value={minimumTurnaroundHours}
                onChange={(e) => setMinimumTurnaroundHours(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                min="0"
                max="24"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={overtimeAllowed}
                onChange={(e) => setOvertimeAllowed(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-secondary-dark">Allow Overtime</span>
            </label>
          </div>
        </div>

        {/* Coverage Rules */}
        <div className="border rounded-lg p-4">
          <h3 className="font-medium text-secondary-dark mb-4">Coverage Rules</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Emergency Coverage Plan
              </label>
              <select
                value={emergencyCoveragePlan}
                onChange={(e) => setEmergencyCoveragePlan(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="call_in_overtime">Call-in Overtime</option>
                <option value="mandatory_overtime">Mandatory Overtime</option>
                <option value="temporary_staff">Temporary Staff</option>
                <option value="reduce_service">Reduce Service</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Minimum Coverage Percentage
              </label>
              <input
                type="number"
                value={minimumCoveragePercentage}
                onChange={(e) => setMinimumCoveragePercentage(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                min="0"
                max="100"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={allowUnderstaffing}
                onChange={(e) => setAllowUnderstaffing(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-secondary-dark">Allow Understaffing</span>
            </label>
          </div>
        </div>

        {/* Employee Preferences */}
        <div className="border rounded-lg p-4">
          <h3 className="font-medium text-secondary-dark mb-4">Employee Preferences</h3>
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={considerEmployeeRequests}
                onChange={(e) => setConsiderEmployeeRequests(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-secondary-dark">Consider Employee Requests</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={fairRotation}
                onChange={(e) => setFairRotation(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-secondary-dark">Fair Rotation</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={seniorityPriority}
                onChange={(e) => setSeniorityPriority(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-secondary-dark">Seniority Priority</span>
            </label>
          </div>
        </div>

        {/* Shift Requirements */}
        <div className="border rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-medium text-secondary-dark">Shift Requirements</h3>
            <button
              type="button"
              onClick={addShiftRequirement}
              className="px-3 py-1 bg-primary text-white text-sm rounded hover:bg-primary-dark transition-colors"
            >
              Add Shift
            </button>
          </div>

          {shiftRequirements.length === 0 ? (
            <div className="text-center py-8 text-secondary">
              <p>No shift requirements added yet.</p>
              <button
                type="button"
                onClick={addShiftRequirement}
                className="mt-2 text-primary hover:text-primary-dark"
              >
                Add your first shift requirement
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {shiftRequirements.map((shift, index) => (
                <div key={index} className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="font-medium text-secondary-dark">Shift {index + 1}</h4>
                    <button
                      type="button"
                      onClick={() => removeShiftRequirement(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-2">
                        Select Shift *
                      </label>
                      <select
                        value={shift.shift_id}
                        onChange={(e) => updateShiftRequirement(index, { shift_id: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                      >
                        <option value="">Select a shift...</option>
                        {availableShifts.map((availableShift) => (
                          <option key={availableShift.shift_id} value={availableShift.shift_id}>
                            {availableShift.name} ({formatTimeRange(availableShift.start_time, availableShift.end_time)})
                          </option>
                        ))}
                      </select>
                      {shift.shift_id && (
                        <div className="mt-2 text-xs text-secondary">
                          {(() => {
                            const shiftInfo = getSelectedShiftInfo(shift.shift_id);
                            return shiftInfo ? (
                              <div>
                                <p>Working Days: {formatDaysOfWeek(shiftInfo.working_days.split(',').map(Number))}</p>
                                <p>Duration: {formatTimeRange(shiftInfo.start_time, shiftInfo.end_time)}</p>
                              </div>
                            ) : null;
                          })()}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-2">
                        Working Days
                      </label>
                      <div className="grid grid-cols-7 gap-1">
                        {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, dayIndex) => (
                          <label key={dayIndex} className="flex flex-col items-center">
                            <input
                              type="checkbox"
                              checked={shift.days_of_week.includes(dayIndex + 1)}
                              onChange={(e) => {
                                const dayNum = dayIndex + 1;
                                const newDays = e.target.checked
                                  ? [...shift.days_of_week, dayNum].sort()
                                  : shift.days_of_week.filter(d => d !== dayNum);
                                updateShiftRequirement(index, { days_of_week: newDays });
                              }}
                              className="mb-1"
                            />
                            <span className="text-xs">{day}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-2">
                        Minimum Staff
                      </label>
                      <input
                        type="number"
                        value={shift.staffing.minimum_staff}
                        onChange={(e) => updateShiftRequirement(index, {
                          staffing: { ...shift.staffing, minimum_staff: parseInt(e.target.value) }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        min="1"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-2">
                        Preferred Staff
                      </label>
                      <input
                        type="number"
                        value={shift.staffing.preferred_staff}
                        onChange={(e) => updateShiftRequirement(index, {
                          staffing: { ...shift.staffing, preferred_staff: parseInt(e.target.value) }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        min="1"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-2">
                        Maximum Staff
                      </label>
                      <input
                        type="number"
                        value={shift.staffing.maximum_staff}
                        onChange={(e) => updateShiftRequirement(index, {
                          staffing: { ...shift.staffing, maximum_staff: parseInt(e.target.value) }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        min="1"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-2">
                        Break Duration (minutes)
                      </label>
                      <input
                        type="number"
                        value={shift.break_duration || 0}
                        onChange={(e) => updateShiftRequirement(index, { break_duration: parseInt(e.target.value) })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        min="0"
                      />
                    </div>
                    <div className="flex items-end">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={shift.overtime_eligible || false}
                          onChange={(e) => updateShiftRequirement(index, { overtime_eligible: e.target.checked })}
                          className="mr-2"
                        />
                        <span className="text-sm text-secondary-dark">Overtime Eligible</span>
                      </label>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 text-secondary rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            type="button"
            onClick={validateTemplate}
            className="px-6 py-2 border border-primary text-primary rounded-lg hover:bg-primary-light transition-colors"
            disabled={isLoading}
          >
            Validate
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
          >
            {isLoading ? 'Creating...' : 'Create Template'}
          </button>
        </div>
      </form>
    </DashboardCard>
  );
};

export default TemplateBuilder;
