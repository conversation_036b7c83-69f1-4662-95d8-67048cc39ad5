'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import { generateSchedule } from '@/lib/advanced-shift-api';
import { TemplateWithSummary, GeneratedSchedule } from '@/types/advanced-shift';

interface ScheduleGenerationProps {
  template: TemplateWithSummary;
  onSuccess?: (schedule: GeneratedSchedule) => void;
  onCancel?: () => void;
}

const ScheduleGeneration: React.FC<ScheduleGenerationProps> = ({
  template,
  onSuccess,
  onCancel
}) => {
  const { companies } = useAuth();
  const [scheduleName, setScheduleName] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [generatedSchedule, setGeneratedSchedule] = useState<GeneratedSchedule | null>(null);

  // Set default dates (next Monday to 8 weeks later)
  React.useEffect(() => {
    const today = new Date();
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);
    
    const endDateDefault = new Date(nextMonday);
    endDateDefault.setDate(nextMonday.getDate() + (8 * 7) - 1); // 8 weeks later, end on Sunday
    
    setStartDate(nextMonday.toISOString().split('T')[0]);
    setEndDate(endDateDefault.toISOString().split('T')[0]);
    
    // Generate default schedule name
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'];
    const startMonth = monthNames[nextMonday.getMonth()];
    const endMonth = monthNames[endDateDefault.getMonth()];
    const year = nextMonday.getFullYear();
    
    if (startMonth === endMonth) {
      setScheduleName(`${startMonth} ${year} Schedule`);
    } else {
      setScheduleName(`${startMonth}-${endMonth} ${year} Schedule`);
    }
  }, []);

  const validateDates = () => {
    if (!startDate || !endDate) {
      setError('Both start and end dates are required');
      return false;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (start < today) {
      setError('Start date cannot be in the past');
      return false;
    }

    if (end <= start) {
      setError('End date must be after start date');
      return false;
    }

    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 7) {
      setError('Schedule must be at least 7 days long');
      return false;
    }

    if (diffDays > 365) {
      setError('Schedule cannot be longer than 365 days');
      return false;
    }

    return true;
  };

  const handleGenerate = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!scheduleName.trim()) {
      setError('Schedule name is required');
      return;
    }

    if (!validateDates()) {
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const companyId = companies[0].company_id;
      const response = await generateSchedule({
        company_id: companyId,
        template_id: template.template_id,
        start_date: startDate,
        end_date: endDate,
        schedule_name: scheduleName.trim()
      });

      setGeneratedSchedule(response.schedule);
      
      if (onSuccess) {
        onSuccess(response.schedule);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to generate schedule');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateScheduleDuration = () => {
    if (!startDate || !endDate) return '';
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include end date
    const weeks = Math.floor(diffDays / 7);
    const remainingDays = diffDays % 7;
    
    if (weeks === 0) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
    } else if (remainingDays === 0) {
      return `${weeks} week${weeks !== 1 ? 's' : ''}`;
    } else {
      return `${weeks} week${weeks !== 1 ? 's' : ''} and ${remainingDays} day${remainingDays !== 1 ? 's' : ''}`;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (generatedSchedule) {
    return (
      <DashboardCard title="Schedule Generated Successfully">
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">✓</span>
            </div>
            <h3 className="text-lg font-semibold text-secondary-dark mb-2">
              Schedule Created Successfully!
            </h3>
            <p className="text-secondary">
              Your schedule has been generated and is ready for employee assignment.
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-secondary-dark mb-3">Schedule Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-secondary-dark">Schedule Name</p>
                <p className="text-sm text-secondary">{generatedSchedule.schedule_name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-dark">Schedule ID</p>
                <p className="text-sm text-secondary font-mono">{generatedSchedule.schedule_id}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-dark">Start Date</p>
                <p className="text-sm text-secondary">{formatDate(generatedSchedule.start_date)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-dark">End Date</p>
                <p className="text-sm text-secondary">{formatDate(generatedSchedule.end_date)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-dark">Status</p>
                <span className="inline-flex px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                  {generatedSchedule.status}
                </span>
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-dark">Created</p>
                <p className="text-sm text-secondary">{formatDate(generatedSchedule.created_at)}</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-blue-900 mb-2">Next Steps</h4>
            <ol className="text-sm text-blue-800 space-y-1">
              <li>1. Review the generated schedule details</li>
              <li>2. Assign employees or departments to the schedule</li>
              <li>3. Activate the schedule when ready</li>
              <li>4. Monitor and adjust as needed</li>
            </ol>
          </div>

          <div className="flex justify-center space-x-4">
            <button
              onClick={() => {
                setGeneratedSchedule(null);
                setScheduleName('');
                setError('');
              }}
              className="px-6 py-2 border border-gray-300 text-secondary rounded-lg hover:bg-gray-50 transition-colors"
            >
              Generate Another
            </button>
            {onCancel && (
              <button
                onClick={onCancel}
                className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Continue to Assignment
              </button>
            )}
          </div>
        </div>
      </DashboardCard>
    );
  }

  return (
    <DashboardCard title={`Generate Schedule from: ${template.name}`}>
      <form onSubmit={handleGenerate} className="p-6">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="text-red-800 text-sm">{error}</div>
          </div>
        )}

        {/* Template Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-secondary-dark mb-3">Template Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-secondary-dark">Pattern Type</p>
              <p className="text-sm text-secondary capitalize">{template.pattern_type}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">Total Shifts</p>
              <p className="text-sm text-secondary">{template.staffing_summary.total_shifts}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">Weekly Coverage</p>
              <p className="text-sm text-secondary">{template.weekly_coverage_hours}h</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">24/7 Coverage</p>
              <p className="text-sm text-secondary">{template.is_24_7_coverage ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </div>

        {/* Schedule Configuration */}
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-secondary-dark mb-2">
              Schedule Name *
            </label>
            <input
              type="text"
              value={scheduleName}
              onChange={(e) => setScheduleName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter schedule name"
              required
            />
            <p className="text-xs text-secondary mt-1">
              Choose a descriptive name for this schedule period
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Start Date *
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                End Date *
              </label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                required
              />
            </div>
          </div>

          {startDate && endDate && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <span className="text-blue-800 text-sm font-medium">Schedule Duration:</span>
                <span className="text-blue-700 text-sm">{calculateScheduleDuration()}</span>
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-blue-800 text-sm font-medium">Date Range:</span>
                <span className="text-blue-700 text-sm">
                  {formatDate(startDate)} to {formatDate(endDate)}
                </span>
              </div>
            </div>
          )}

          {/* Estimated Staffing Requirements */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-secondary-dark mb-3">Estimated Weekly Staffing Requirements</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {template.staffing_summary.weekly_totals.min_staff_hours}h
                </p>
                <p className="text-sm text-secondary">Minimum Hours</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  {template.staffing_summary.weekly_totals.preferred_staff_hours}h
                </p>
                <p className="text-sm text-secondary">Preferred Hours</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">
                  {template.staffing_summary.weekly_totals.max_staff_hours}h
                </p>
                <p className="text-sm text-secondary">Maximum Hours</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t mt-6">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 text-secondary rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isLoading}
            className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
          >
            {isLoading ? 'Generating Schedule...' : 'Generate Schedule'}
          </button>
        </div>
      </form>
    </DashboardCard>
  );
};

export default ScheduleGeneration;
