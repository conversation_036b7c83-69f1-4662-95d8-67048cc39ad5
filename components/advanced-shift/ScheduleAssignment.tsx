'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import { assignSchedule } from '@/lib/advanced-shift-api';
import { apiGet } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import { GeneratedSchedule, AssignScheduleResponse } from '@/types/advanced-shift';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  position: string | null;
  department_id: string | null;
  status: string;
}

interface Department {
  department_id: string;
  name: string;
  description: string | null;
  employee_count?: number;
}

interface ScheduleAssignmentProps {
  schedule: GeneratedSchedule;
  onSuccess?: (result: AssignScheduleResponse) => void;
  onCancel?: () => void;
}

const ScheduleAssignment: React.FC<ScheduleAssignmentProps> = ({
  schedule,
  onSuccess,
  onCancel
}) => {
  const { companies } = useAuth();
  const [assignmentType, setAssignmentType] = useState<'employee' | 'department'>('employee');
  const [assignmentStrategy, setAssignmentStrategy] = useState<'automatic' | 'manual'>('automatic');
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState('');
  const [assignmentResult, setAssignmentResult] = useState<AssignScheduleResponse | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (companies.length > 0) {
      fetchData();
    }
  }, [companies]);

  const fetchData = async () => {
    try {
      setIsLoadingData(true);
      const token = getAccessToken();
      const companyId = companies[0].company_id;

      // Fetch employees and departments in parallel
      const [employeesResponse, departmentsResponse] = await Promise.all([
        apiGet<{ employees: Employee[] }>(`api/employees?company_id=${companyId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        apiGet<{ departments: Department[] }>(`api/departments?company_id=${companyId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ]);

      setEmployees(employeesResponse.employees || []);
      setDepartments(departmentsResponse.departments || []);
    } catch (error: any) {
      setError('Failed to load employees and departments: ' + error.message);
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleEmployeeToggle = (employeeId: string) => {
    setSelectedEmployees(prev => 
      prev.includes(employeeId)
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  const handleDepartmentToggle = (departmentId: string) => {
    setSelectedDepartments(prev => 
      prev.includes(departmentId)
        ? prev.filter(id => id !== departmentId)
        : [...prev, departmentId]
    );
  };

  const handleSelectAll = () => {
    if (assignmentType === 'employee') {
      const filteredEmployees = employees.filter(emp => 
        emp.status === 'active' && 
        (emp.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
         (emp.position && emp.position.toLowerCase().includes(searchTerm.toLowerCase())))
      );
      setSelectedEmployees(filteredEmployees.map(emp => emp.employee_id));
    } else {
      const filteredDepartments = departments.filter(dept =>
        dept.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSelectedDepartments(filteredDepartments.map(dept => dept.department_id));
    }
  };

  const handleDeselectAll = () => {
    if (assignmentType === 'employee') {
      setSelectedEmployees([]);
    } else {
      setSelectedDepartments([]);
    }
  };

  const handleAssign = async () => {
    const targetIds = assignmentType === 'employee' ? selectedEmployees : selectedDepartments;
    
    if (targetIds.length === 0) {
      setError(`Please select at least one ${assignmentType}`);
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const companyId = companies[0].company_id;
      const result = await assignSchedule({
        company_id: companyId,
        schedule_id: schedule.schedule_id,
        assignment_type: assignmentType,
        target_ids: targetIds,
        assignment_strategy: assignmentStrategy
      });

      setAssignmentResult(result);
      
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to assign schedule');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const filteredEmployees = employees.filter(emp => 
    emp.status === 'active' && 
    (emp.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
     (emp.position && emp.position.toLowerCase().includes(searchTerm.toLowerCase())))
  );

  const filteredDepartments = departments.filter(dept =>
    dept.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (assignmentResult) {
    return (
      <DashboardCard title="Schedule Assignment Complete">
        <div className="p-6">
          <div className="text-center mb-6">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
              assignmentResult.summary.failed_assignments === 0 
                ? 'bg-green-100' 
                : 'bg-yellow-100'
            }`}>
              <span className="text-2xl">
                {assignmentResult.summary.failed_assignments === 0 ? '✓' : '⚠'}
              </span>
            </div>
            <h3 className="text-lg font-semibold text-secondary-dark mb-2">
              Assignment {assignmentResult.summary.failed_assignments === 0 ? 'Completed' : 'Partially Completed'}
            </h3>
            <p className="text-secondary">
              {assignmentResult.message}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-secondary-dark mb-3">Assignment Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <p className="text-sm font-medium text-secondary-dark">Total Assignments</p>
                <p className="text-2xl font-bold text-blue-600">{assignmentResult.summary.total_assignments}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-dark">Successful</p>
                <p className="text-2xl font-bold text-green-600">{assignmentResult.assignments_created}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-dark">Failed</p>
                <p className="text-2xl font-bold text-red-600">{assignmentResult.summary.failed_assignments}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-secondary-dark">Strategy</p>
                <p className="text-sm text-secondary capitalize">{assignmentResult.summary.assignment_strategy}</p>
              </div>
            </div>
          </div>

          {assignmentResult.assignments.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium text-secondary-dark mb-3">Successful Assignments</h4>
              <div className="max-h-60 overflow-y-auto">
                <div className="space-y-2">
                  {assignmentResult.assignments.map((assignment, index) => (
                    <div key={index} className="bg-white border rounded-lg p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-sm font-medium text-secondary-dark">
                            Employee ID: {assignment.employee_id}
                          </p>
                          <p className="text-xs text-secondary">
                            Assignment ID: {assignment.assignment_id}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-xs text-secondary">
                            {formatDate(assignment.start_date)} - {formatDate(assignment.end_date)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {assignmentResult.errors.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium text-red-800 mb-3">Assignment Errors</h4>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <ul className="text-sm text-red-700 space-y-1">
                  {assignmentResult.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          <div className="flex justify-center space-x-4">
            <button
              onClick={() => {
                setAssignmentResult(null);
                setSelectedEmployees([]);
                setSelectedDepartments([]);
                setError('');
              }}
              className="px-6 py-2 border border-gray-300 text-secondary rounded-lg hover:bg-gray-50 transition-colors"
            >
              Assign More
            </button>
            {onCancel && (
              <button
                onClick={onCancel}
                className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Done
              </button>
            )}
          </div>
        </div>
      </DashboardCard>
    );
  }

  return (
    <DashboardCard title={`Assign Schedule: ${schedule.schedule_name}`}>
      <div className="p-6">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="text-red-800 text-sm">{error}</div>
          </div>
        )}

        {/* Schedule Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-secondary-dark mb-3">Schedule Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-secondary-dark">Schedule Name</p>
              <p className="text-sm text-secondary">{schedule.schedule_name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">Start Date</p>
              <p className="text-sm text-secondary">{formatDate(schedule.start_date)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">End Date</p>
              <p className="text-sm text-secondary">{formatDate(schedule.end_date)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-secondary-dark">Status</p>
              <span className="inline-flex px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                {schedule.status}
              </span>
            </div>
          </div>
        </div>

        {/* Assignment Configuration */}
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Assignment Type
              </label>
              <select
                value={assignmentType}
                onChange={(e) => {
                  setAssignmentType(e.target.value as 'employee' | 'department');
                  setSelectedEmployees([]);
                  setSelectedDepartments([]);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="employee">Individual Employees</option>
                <option value="department">Entire Departments</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Assignment Strategy
              </label>
              <select
                value={assignmentStrategy}
                onChange={(e) => setAssignmentStrategy(e.target.value as 'automatic' | 'manual')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="automatic">Automatic Assignment</option>
                <option value="manual">Manual Assignment</option>
              </select>
            </div>
          </div>

          {/* Search and Selection */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <label className="block text-sm font-medium text-secondary-dark">
                Select {assignmentType === 'employee' ? 'Employees' : 'Departments'}
              </label>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={handleSelectAll}
                  className="text-sm text-primary hover:text-primary-dark"
                >
                  Select All
                </button>
                <button
                  type="button"
                  onClick={handleDeselectAll}
                  className="text-sm text-secondary hover:text-secondary-dark"
                >
                  Deselect All
                </button>
              </div>
            </div>

            <div className="mb-4">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder={`Search ${assignmentType === 'employee' ? 'employees' : 'departments'}...`}
              />
            </div>

            {isLoadingData ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                <span className="ml-2 text-secondary">Loading...</span>
              </div>
            ) : (
              <div className="border rounded-lg max-h-80 overflow-y-auto">
                {assignmentType === 'employee' ? (
                  <div className="divide-y">
                    {filteredEmployees.length === 0 ? (
                      <div className="p-4 text-center text-secondary">
                        No active employees found
                      </div>
                    ) : (
                      filteredEmployees.map((employee) => (
                        <label key={employee.employee_id} className="flex items-center p-3 hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedEmployees.includes(employee.employee_id)}
                            onChange={() => handleEmployeeToggle(employee.employee_id)}
                            className="mr-3"
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-secondary-dark">{employee.full_name}</p>
                            <p className="text-xs text-secondary">
                              {employee.position || 'No position'} • {employee.email || 'No email'}
                            </p>
                          </div>
                        </label>
                      ))
                    )}
                  </div>
                ) : (
                  <div className="divide-y">
                    {filteredDepartments.length === 0 ? (
                      <div className="p-4 text-center text-secondary">
                        No departments found
                      </div>
                    ) : (
                      filteredDepartments.map((department) => (
                        <label key={department.department_id} className="flex items-center p-3 hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedDepartments.includes(department.department_id)}
                            onChange={() => handleDepartmentToggle(department.department_id)}
                            className="mr-3"
                          />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-secondary-dark">{department.name}</p>
                            <p className="text-xs text-secondary">
                              {department.description || 'No description'}
                            </p>
                          </div>
                        </label>
                      ))
                    )}
                  </div>
                )}
              </div>
            )}

            <div className="mt-2 text-sm text-secondary">
              {assignmentType === 'employee' 
                ? `${selectedEmployees.length} employee${selectedEmployees.length !== 1 ? 's' : ''} selected`
                : `${selectedDepartments.length} department${selectedDepartments.length !== 1 ? 's' : ''} selected`
              }
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t mt-6">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 text-secondary rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            onClick={handleAssign}
            disabled={isLoading || (assignmentType === 'employee' ? selectedEmployees.length === 0 : selectedDepartments.length === 0)}
            className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
          >
            {isLoading ? 'Assigning...' : 'Assign Schedule'}
          </button>
        </div>
      </div>
    </DashboardCard>
  );
};

export default ScheduleAssignment;
