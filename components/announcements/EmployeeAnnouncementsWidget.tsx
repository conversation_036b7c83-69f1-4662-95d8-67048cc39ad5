'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Announcement, getPriorityColor, getPriorityIcon } from '@/types/announcement';
import { getEmployeeAnnouncements, getUnreadAnnouncementsCount } from '@/lib/announcements';
import DashboardCard from '@/components/ui/DashboardCard';

interface EmployeeAnnouncementsWidgetProps {
  limit?: number;
  showViewAll?: boolean;
}

const EmployeeAnnouncementsWidget: React.FC<EmployeeAnnouncementsWidgetProps> = ({
  limit = 5,
  showViewAll = true
}) => {
  const { companies, user } = useAuth();
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [unreadCount, setUnreadCount] = useState(0);

  // Fetch announcements for employee
  const fetchAnnouncements = async () => {
    try {
      setIsLoading(true);
      setError('');

      const options = {
        page: 1,
        limit: limit,
        priority: 'HIGH' // Show high priority announcements in widget
      };

      const response = await getEmployeeAnnouncements(options);

      // Sort by priority and creation date
      const sortedAnnouncements = response.announcements
        .sort((a, b) => {
          // First sort by pinned status
          if (a.is_pinned && !b.is_pinned) return -1;
          if (!a.is_pinned && b.is_pinned) return 1;

          // Then by priority
          const priorityOrder = { 'URGENT': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
          const aPriority = priorityOrder[a.priority] || 0;
          const bPriority = priorityOrder[b.priority] || 0;
          if (aPriority !== bPriority) return bPriority - aPriority;

          // Finally by creation date (newest first)
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });

      setAnnouncements(sortedAnnouncements);
      setUnreadCount(response.unread_count);
    } catch (error: any) {
      console.error('Error fetching announcements:', error);
      setError('Failed to load announcements');
      setAnnouncements([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, [limit]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  const truncateText = (text: string | undefined | null, maxLength: number) => {
    if (!text || typeof text !== 'string') return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const cardTitle = (
    <div className="flex items-center justify-between">
      <span>Company Announcements</span>
      {unreadCount > 0 && (
        <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
          {unreadCount} unread
        </span>
      )}
    </div>
  );

  return (
    <DashboardCard title={cardTitle} loading={isLoading}>
      {error ? (
        <div className="py-8 text-center">
          <p className="text-red-600 text-sm">{error}</p>
          <button
            onClick={fetchAnnouncements}
            className="mt-2 text-blue-600 hover:text-blue-800 text-sm"
          >
            Try again
          </button>
        </div>
      ) : announcements.length === 0 ? (
        <div className="py-8 text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
          </svg>
          <p className="mt-2 text-gray-600">No announcements available</p>
        </div>
      ) : (
        <div className="space-y-4">
          {announcements.map((announcement) => (
            <div
              key={announcement.announcement_id}
              className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    {announcement.is_pinned && (
                      <svg className="h-4 w-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"/>
                      </svg>
                    )}
                    <h4 className="text-sm font-semibold text-gray-900 line-clamp-1">
                      {announcement.title}
                    </h4>
                  </div>
                  
                  {announcement.summary ? (
                    <p className="text-xs text-gray-600 line-clamp-2">
                      {truncateText(announcement.summary, 100)}
                    </p>
                  ) : announcement.content ? (
                    <p className="text-xs text-gray-600 line-clamp-2">
                      {truncateText(announcement.content, 100)}
                    </p>
                  ) : (
                    <p className="text-xs text-gray-500 italic line-clamp-2">
                      No content available
                    </p>
                  )}
                </div>

                {/* Priority Badge */}
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(announcement.priority)} ml-2`}>
                  <span className="mr-1">{getPriorityIcon(announcement.priority)}</span>
                  {announcement.priority}
                </span>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-100">
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500">
                    {formatDate(announcement.created_at)}
                  </span>
                  
                  {announcement.category && (
                    <>
                      <span className="text-xs text-gray-400">•</span>
                      <span className="text-xs text-gray-500">
                        {announcement.category}
                      </span>
                    </>
                  )}
                </div>

                {announcement.requires_acknowledgment && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Action Required
                  </span>
                )}
              </div>
            </div>
          ))}

          {/* View All Link */}
          {showViewAll && announcements.length > 0 && (
            <div className="text-center pt-4 border-t border-gray-100">
              <Link
                href="/dashboard/employee/announcements"
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                View all announcements →
              </Link>
            </div>
          )}
        </div>
      )}
    </DashboardCard>
  );
};

export default EmployeeAnnouncementsWidget;
