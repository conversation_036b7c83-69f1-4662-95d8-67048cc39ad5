'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Announcement,
  CreateAnnouncementRequest,
  UpdateAnnouncementRequest,
  AnnouncementType,
  Priority,
  TargetAudience
} from '@/types/announcement';
import { createAnnouncement, updateAnnouncement } from '@/lib/announcements';

// Define interfaces for departments and employees
interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
  id_number?: string | null;
}

interface AnnouncementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  announcement?: Announcement;
  isEditing?: boolean;
}

const AnnouncementModal: React.FC<AnnouncementModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  announcement,
  isEditing = false
}) => {
  const { companies } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // State for departments and employees
  const [departments, setDepartments] = useState<Department[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);

  const [formData, setFormData] = useState({
    title: '',
    content: '',
    summary: '',
    announcement_type: 'GENERAL' as AnnouncementType,
    priority: 'MEDIUM' as Priority,
    category: '',
    tags: '',
    target_audience: 'ALL' as TargetAudience,
    publish_date: '',
    expiry_date: '',
    is_pinned: false,
    allows_comments: false,
    requires_acknowledgment: false,
    department_ids: [] as string[],
    employee_ids: [] as string[],
    role_targets: [] as string[]
  });

  // Fetch departments
  const fetchDepartments = async () => {
    try {
      setIsLoadingDepartments(true);
      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        setIsLoadingDepartments(false);
        return;
      }

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        setIsLoadingDepartments(false);
        return;
      }

      const response = await apiGet<{departments: Department[], success: boolean}>(`api/departments?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.departments) {
        setDepartments(response.departments);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      setIsLoadingEmployees(true);
      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        setIsLoadingEmployees(false);
        return;
      }

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        setIsLoadingEmployees(false);
        return;
      }

      const response = await apiGet<{extend: {employees: Employee[]}}>(`api/employees?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.extend && response.extend.employees) {
        setEmployees(response.extend.employees);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  // Reset form when modal opens/closes or announcement changes
  useEffect(() => {
    if (isOpen) {
      // Fetch departments and employees when modal opens
      fetchDepartments();
      fetchEmployees();

      if (isEditing && announcement) {
        setFormData({
          title: announcement.title,
          content: announcement.content || '',
          summary: announcement.summary || '',
          announcement_type: announcement.announcement_type,
          priority: announcement.priority,
          category: announcement.category || '',
          tags: announcement.tags.join(', '),
          target_audience: announcement.target_audience,
          publish_date: announcement.publish_date ? announcement.publish_date.slice(0, 16) : '',
          expiry_date: announcement.expiry_date ? announcement.expiry_date.slice(0, 16) : '',
          is_pinned: announcement.is_pinned,
          allows_comments: announcement.allows_comments,
          requires_acknowledgment: announcement.requires_acknowledgment,
          department_ids: announcement.department_ids,
          employee_ids: announcement.employee_ids,
          role_targets: announcement.role_targets
        });
      } else {
        // Reset to default values for new announcement
        setFormData({
          title: '',
          content: '',
          summary: '',
          announcement_type: 'GENERAL',
          priority: 'MEDIUM',
          category: '',
          tags: '',
          target_audience: 'ALL',
          publish_date: '',
          expiry_date: '',
          is_pinned: false,
          allows_comments: false,
          requires_acknowledgment: false,
          department_ids: [],
          employee_ids: [],
          role_targets: []
        });
      }
      setError('');
    }
  }, [isOpen, isEditing, announcement]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Reset department/employee selections when target audience changes
      if (name === 'target_audience') {
        setFormData(prev => ({
          ...prev,
          department_ids: [],
          employee_ids: []
        }));
      }
    }
  };

  // Handle department selection
  const handleDepartmentToggle = (departmentId: string) => {
    setFormData(prev => ({
      ...prev,
      department_ids: prev.department_ids.includes(departmentId)
        ? prev.department_ids.filter(id => id !== departmentId)
        : [...prev.department_ids, departmentId]
    }));
  };

  // Handle employee selection
  const handleEmployeeToggle = (employeeId: string) => {
    setFormData(prev => ({
      ...prev,
      employee_ids: prev.employee_ids.includes(employeeId)
        ? prev.employee_ids.filter(id => id !== employeeId)
        : [...prev.employee_ids, employeeId]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Basic validation
    if (!formData.title.trim()) {
      setError('Title is required');
      setIsLoading(false);
      return;
    }

    if (!formData.content.trim()) {
      setError('Content is required');
      setIsLoading(false);
      return;
    }

    // Validate target audience specific requirements
    if (formData.target_audience === 'DEPARTMENT_SPECIFIC' && formData.department_ids.length === 0) {
      setError('Please select at least one department');
      setIsLoading(false);
      return;
    }

    if (formData.target_audience === 'CUSTOM' && formData.employee_ids.length === 0) {
      setError('Please select at least one employee');
      setIsLoading(false);
      return;
    }

    try {
      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        setError('No company found. Please register a company first.');
        setIsLoading(false);
        return;
      }

      // Prepare the data
      const tagsArray = formData.tags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      // Create base data object
      const baseData = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        company_id: companyId,
        summary: formData.summary.trim() || undefined,
        announcement_type: formData.announcement_type,
        priority: formData.priority,
        category: formData.category.trim() || undefined,
        tags: tagsArray,
        target_audience: formData.target_audience,
        publish_date: formData.publish_date || undefined,
        expiry_date: formData.expiry_date || undefined,
        is_pinned: formData.is_pinned,
        allows_comments: formData.allows_comments,
        requires_acknowledgment: formData.requires_acknowledgment
      };

      // Add target-specific fields based on target audience
      let requestData: any = { ...baseData };

      if (formData.target_audience === 'DEPARTMENT_SPECIFIC') {
        requestData.department_ids = formData.department_ids;
      } else if (formData.target_audience === 'CUSTOM') {
        requestData.employee_ids = formData.employee_ids;
      }
      // For 'ALL' target audience, no additional fields needed

      if (isEditing && announcement) {
        // Update existing announcement
        await updateAnnouncement(announcement.announcement_id, requestData);
      } else {
        // Create new announcement
        await createAnnouncement(requestData);
      }

      // Close modal immediately without showing success message
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error with announcement operation:', error);
      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} announcement. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  {isEditing ? 'Edit Announcement' : 'Create New Announcement'}
                </h3>

                {error && (
                  <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="mt-4 space-y-6">
                  {/* Title */}
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      Title *
                    </label>
                    <input
                      id="title"
                      name="title"
                      type="text"
                      required
                      value={formData.title}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter announcement title"
                    />
                  </div>

                  {/* Summary */}
                  <div>
                    <label htmlFor="summary" className="block text-sm font-medium text-gray-700 mb-1">
                      Summary
                    </label>
                    <input
                      id="summary"
                      name="summary"
                      type="text"
                      value={formData.summary}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Brief summary of the announcement"
                    />
                  </div>

                  {/* Content */}
                  <div>
                    <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                      Content *
                    </label>
                    <textarea
                      id="content"
                      name="content"
                      required
                      rows={6}
                      value={formData.content}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter the full announcement content"
                    />
                  </div>

                  {/* Type and Priority */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="announcement_type" className="block text-sm font-medium text-gray-700 mb-1">
                        Type
                      </label>
                      <select
                        id="announcement_type"
                        name="announcement_type"
                        value={formData.announcement_type}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="GENERAL">General</option>
                        <option value="POLICY">Policy</option>
                        <option value="EVENT">Event</option>
                        <option value="URGENT">Urgent</option>
                        <option value="MAINTENANCE">Maintenance</option>
                        <option value="CELEBRATION">Celebration</option>
                        <option value="TRAINING">Training</option>
                        <option value="MEETING">Meeting</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                        Priority
                      </label>
                      <select
                        id="priority"
                        name="priority"
                        value={formData.priority}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="LOW">Low</option>
                        <option value="MEDIUM">Medium</option>
                        <option value="HIGH">High</option>
                        <option value="URGENT">Urgent</option>
                      </select>
                    </div>
                  </div>

                  {/* Category and Tags */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                        Category
                      </label>
                      <input
                        id="category"
                        name="category"
                        type="text"
                        value={formData.category}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., HR Policy, IT Update"
                      />
                    </div>

                    <div>
                      <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-1">
                        Tags
                      </label>
                      <input
                        id="tags"
                        name="tags"
                        type="text"
                        value={formData.tags}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Comma-separated tags"
                      />
                    </div>
                  </div>

                  {/* Target Audience */}
                  <div>
                    <label htmlFor="target_audience" className="block text-sm font-medium text-gray-700 mb-1">
                      Target Audience
                    </label>
                    <select
                      id="target_audience"
                      name="target_audience"
                      value={formData.target_audience}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="ALL">All Employees</option>
                      <option value="DEPARTMENT_SPECIFIC">Specific Departments</option>
                      <option value="CUSTOM">Specific Employees</option>
                    </select>
                  </div>

                  {/* Department Selection - Show when DEPARTMENT_SPECIFIC is selected */}
                  {formData.target_audience === 'DEPARTMENT_SPECIFIC' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Departments
                      </label>
                      <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                        {isLoadingDepartments ? (
                          <div className="text-sm text-gray-500 p-2">Loading departments...</div>
                        ) : departments.length === 0 ? (
                          <div className="text-sm text-gray-500 p-2">No departments found</div>
                        ) : (
                          departments.map((department) => (
                            <div key={department.department_id} className="flex items-center mb-2">
                              <input
                                type="checkbox"
                                id={`dept-${department.department_id}`}
                                checked={formData.department_ids.includes(department.department_id)}
                                onChange={() => handleDepartmentToggle(department.department_id)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label
                                htmlFor={`dept-${department.department_id}`}
                                className="ml-2 block text-sm text-gray-900 cursor-pointer"
                              >
                                {department.name}
                              </label>
                            </div>
                          ))
                        )}
                      </div>
                      {formData.department_ids.length > 0 && (
                        <div className="mt-2 text-sm text-gray-600">
                          {formData.department_ids.length} department(s) selected
                        </div>
                      )}
                    </div>
                  )}

                  {/* Employee Selection - Show when CUSTOM is selected */}
                  {formData.target_audience === 'CUSTOM' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Employees
                      </label>
                      <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
                        {isLoadingEmployees ? (
                          <div className="text-sm text-gray-500 p-2">Loading employees...</div>
                        ) : employees.length === 0 ? (
                          <div className="text-sm text-gray-500 p-2">No employees found</div>
                        ) : (
                          employees.map((employee) => (
                            <div key={employee.employee_id} className="flex items-center mb-2">
                              <input
                                type="checkbox"
                                id={`emp-${employee.employee_id}`}
                                checked={formData.employee_ids.includes(employee.employee_id)}
                                onChange={() => handleEmployeeToggle(employee.employee_id)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label
                                htmlFor={`emp-${employee.employee_id}`}
                                className="ml-2 block text-sm text-gray-900 cursor-pointer"
                              >
                                {employee.full_name}
                                {employee.position && (
                                  <span className="text-gray-500 text-xs ml-1">({employee.position})</span>
                                )}
                              </label>
                            </div>
                          ))
                        )}
                      </div>
                      {formData.employee_ids.length > 0 && (
                        <div className="mt-2 text-sm text-gray-600">
                          {formData.employee_ids.length} employee(s) selected
                        </div>
                      )}
                    </div>
                  )}

                  {/* Publish and Expiry Dates */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="publish_date" className="block text-sm font-medium text-gray-700 mb-1">
                        Publish Date
                      </label>
                      <input
                        id="publish_date"
                        name="publish_date"
                        type="datetime-local"
                        value={formData.publish_date}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    <div>
                      <label htmlFor="expiry_date" className="block text-sm font-medium text-gray-700 mb-1">
                        Expiry Date
                      </label>
                      <input
                        id="expiry_date"
                        name="expiry_date"
                        type="datetime-local"
                        value={formData.expiry_date}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  {/* Checkboxes */}
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        id="is_pinned"
                        name="is_pinned"
                        type="checkbox"
                        checked={formData.is_pinned}
                        onChange={handleChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is_pinned" className="ml-2 block text-sm text-gray-900">
                        Pin this announcement
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        id="allows_comments"
                        name="allows_comments"
                        type="checkbox"
                        checked={formData.allows_comments}
                        onChange={handleChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="allows_comments" className="ml-2 block text-sm text-gray-900">
                        Allow comments
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        id="requires_acknowledgment"
                        name="requires_acknowledgment"
                        type="checkbox"
                        checked={formData.requires_acknowledgment}
                        onChange={handleChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="requires_acknowledgment" className="ml-2 block text-sm text-gray-900">
                        Require acknowledgment
                      </label>
                    </div>
                  </div>

                  <div className="pt-4 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                      disabled={isLoading}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center"
                    >
                      {isLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {isEditing ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        isEditing ? 'Update Announcement' : 'Create Announcement'
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementModal;
