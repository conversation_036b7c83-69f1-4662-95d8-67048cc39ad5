'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Announcement, 
  AnnouncementAnalyticsResponse, 
  AnnouncementReadResponse 
} from '@/types/announcement';
import { getAnnouncementAnalytics, getAnnouncementReads } from '@/lib/announcements';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardChart from '@/components/ui/DashboardChart';

interface AnnouncementAnalyticsProps {
  announcement: Announcement;
  isOpen: boolean;
  onClose: () => void;
}

const AnnouncementAnalytics: React.FC<AnnouncementAnalyticsProps> = ({
  announcement,
  isOpen,
  onClose
}) => {
  const { companies } = useAuth();
  const [analytics, setAnalytics] = useState<AnnouncementAnalyticsResponse | null>(null);
  const [readRecords, setReadRecords] = useState<AnnouncementReadResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch analytics data
  const fetchAnalytics = async () => {
    if (!companies || companies.length === 0) return;

    try {
      setIsLoading(true);
      setError('');

      const companyId = companies[0].company_id;
      
      // Fetch analytics and read records in parallel
      const [analyticsResponse, readsResponse] = await Promise.all([
        getAnnouncementAnalytics(announcement.announcement_id, companyId),
        getAnnouncementReads(announcement.announcement_id, companyId, { limit: 50 })
      ]);

      setAnalytics(analyticsResponse);
      setReadRecords(readsResponse);
    } catch (error: any) {
      console.error('Error fetching analytics:', error);
      setError(error.message || 'Failed to fetch analytics');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchAnalytics();
    }
  }, [isOpen, announcement.announcement_id, companies]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEngagementRate = () => {
    if (!analytics || !announcement.total_target_employees) return 0;
    return Math.round((analytics.analytics.unique_readers / announcement.total_target_employees) * 100);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="bg-white px-4 pt-5 pb-4 sm:p-6">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Analytics: {announcement.title}
                </h3>

                {error && (
                  <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {isLoading ? (
                  <div className="py-8 text-center">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="mt-2 text-gray-600">Loading analytics...</p>
                  </div>
                ) : analytics && readRecords ? (
                  <div className="space-y-6">
                    {/* Overview Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="bg-blue-50 rounded-lg p-4">
                        <div className="text-2xl font-bold text-blue-600">
                          {analytics.analytics.total_reads}
                        </div>
                        <div className="text-sm text-blue-800">Total Reads</div>
                      </div>
                      
                      <div className="bg-green-50 rounded-lg p-4">
                        <div className="text-2xl font-bold text-green-600">
                          {analytics.analytics.unique_readers}
                        </div>
                        <div className="text-sm text-green-800">Unique Readers</div>
                      </div>
                      
                      <div className="bg-purple-50 rounded-lg p-4">
                        <div className="text-2xl font-bold text-purple-600">
                          {announcement.acknowledgment_count}
                        </div>
                        <div className="text-sm text-purple-800">Acknowledgments</div>
                      </div>
                      
                      <div className="bg-orange-50 rounded-lg p-4">
                        <div className="text-2xl font-bold text-orange-600">
                          {getEngagementRate()}%
                        </div>
                        <div className="text-sm text-orange-800">Engagement Rate</div>
                      </div>
                    </div>

                    {/* Charts */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Daily Reads Chart */}
                      <DashboardCard title="Daily Reads Trend">
                        <div className="h-64">
                          {analytics.analytics.daily_reads.length > 0 ? (
                            <DashboardChart
                              type="line"
                              labels={analytics.analytics.daily_reads.map(d => 
                                new Date(d.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                              )}
                              datasets={[
                                {
                                  label: 'Daily Reads',
                                  data: analytics.analytics.daily_reads.map(d => d.reads),
                                  borderColor: 'rgb(59, 130, 246)',
                                  backgroundColor: 'rgba(59, 130, 246, 0.1)'
                                }
                              ]}
                            />
                          ) : (
                            <div className="flex items-center justify-center h-full text-gray-500">
                              No read data available
                            </div>
                          )}
                        </div>
                      </DashboardCard>

                      {/* Device Breakdown */}
                      <DashboardCard title="Device Breakdown">
                        <div className="h-64">
                          {readRecords.statistics.device_breakdown.length > 0 ? (
                            <DashboardChart
                              type="doughnut"
                              labels={readRecords.statistics.device_breakdown.map(d => d.device_type || 'Unknown')}
                              datasets={[
                                {
                                  data: readRecords.statistics.device_breakdown.map(d => d.count),
                                  backgroundColor: [
                                    '#3B82F6',
                                    '#10B981',
                                    '#F59E0B',
                                    '#EF4444',
                                    '#8B5CF6'
                                  ]
                                }
                              ]}
                            />
                          ) : (
                            <div className="flex items-center justify-center h-full text-gray-500">
                              No device data available
                            </div>
                          )}
                        </div>
                      </DashboardCard>
                    </div>

                    {/* Read Records Table */}
                    <DashboardCard title="Read Records">
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Employee
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Department
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Read At
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Acknowledged
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Device
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {readRecords.reads.length > 0 ? (
                              readRecords.reads.map((record, index) => (
                                <tr key={index} className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {record.employee_name || 'Unknown'}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {record.department || 'N/A'}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {formatDate(record.read_at)}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    {record.acknowledged_at ? (
                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Yes
                                      </span>
                                    ) : (
                                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        No
                                      </span>
                                    )}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {record.device_type || 'Unknown'}
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                                  No read records found
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </DashboardCard>

                    {/* Statistics Summary */}
                    <DashboardCard title="Summary Statistics">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="text-lg font-semibold text-gray-900">
                            {readRecords.statistics.acknowledgment_rate}%
                          </div>
                          <div className="text-sm text-gray-500">Acknowledgment Rate</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-lg font-semibold text-gray-900">
                            {readRecords.statistics.average_time_spent ? 
                              `${Math.round(readRecords.statistics.average_time_spent)}s` : 
                              'N/A'
                            }
                          </div>
                          <div className="text-sm text-gray-500">Avg. Time Spent</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-lg font-semibold text-gray-900">
                            {announcement.total_target_employees}
                          </div>
                          <div className="text-sm text-gray-500">Target Employees</div>
                        </div>
                      </div>
                    </DashboardCard>
                  </div>
                ) : (
                  <div className="py-8 text-center text-gray-500">
                    No analytics data available
                  </div>
                )}

                <div className="mt-6 flex justify-end">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementAnalytics;
