'use client';

import React, { useState, useMemo } from 'react';
import { formatDate } from '@/utils/dateFormatters';

interface DayStatistics {
  date: string;
  day_of_week: string;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

interface MonthStatistics {
  month: string;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

type StatisticsData = DayStatistics | MonthStatistics;

interface DetailedTableProps {
  data: StatisticsData[];
  period: 'weekly' | 'monthly' | 'annual' | 'custom';
  departmentId?: string;
}

const DetailedTable: React.FC<DetailedTableProps> = ({ data, period, departmentId = 'all' }) => {
  const [sortField, setSortField] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Sort and filter data
  const processedData = useMemo(() => {
    let result = [...data];

    // Sort data
    result.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      if (sortField === 'date') {
        if ('date' in a && 'date' in b) {
          aValue = new Date(a.date).getTime();
          bValue = new Date(b.date).getTime();
        } else if ('month' in a && 'month' in b) {
          // For annual data, sort by month
          const months = ['January', 'February', 'March', 'April', 'May', 'June', 
                         'July', 'August', 'September', 'October', 'November', 'December'];
          aValue = months.indexOf(a.month);
          bValue = months.indexOf(b.month);
        }
      } else {
        aValue = (a as any)[sortField];
        bValue = (b as any)[sortField];
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return result;
  }, [data, sortField, sortDirection]);

  // Get column header based on period
  const getDateColumnHeader = () => {
    switch (period) {
      case 'weekly':
      case 'monthly':
      case 'custom':
        return 'Date';
      case 'annual':
        return 'Month';
      default:
        return 'Date';
    }
  };

  // Format date/month based on period
  const formatDateColumn = (item: StatisticsData) => {
    if ('month' in item) {
      return item.month;
    } else {
      return formatDate(item.date, period);
    }
  };

  // Get day of week (only for daily data)
  const getDayOfWeek = (item: StatisticsData) => {
    if ('day_of_week' in item) {
      return item.day_of_week;
    }
    return '';
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField === field) {
      return sortDirection === 'asc' ? '↑' : '↓';
    }
    return '';
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('date')}
            >
              {getDateColumnHeader()} {renderSortIndicator('date')}
            </th>
            {(period === 'weekly' || period === 'monthly' || period === 'custom') && (
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Day
              </th>
            )}
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('present_count')}
            >
              Present {renderSortIndicator('present_count')}
            </th>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('absent_count')}
            >
              Absent {renderSortIndicator('absent_count')}
            </th>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('late_count')}
            >
              Late {renderSortIndicator('late_count')}
            </th>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('attendance_percentage')}
            >
              Attendance % {renderSortIndicator('attendance_percentage')}
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {processedData.length > 0 ? (
            processedData.map((item, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {formatDateColumn(item)}
                </td>
                {(period === 'weekly' || period === 'monthly' || period === 'custom') && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {getDayOfWeek(item)}
                  </td>
                )}
                <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                  {item.present_count}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                  {item.absent_count}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600 font-medium">
                  {item.late_count}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <span className="text-sm text-gray-900 mr-2">
                      {item.attendance_percentage.toFixed(1)}%
                    </span>
                    <div className="w-24 bg-gray-200 rounded-full h-2.5">
                      <div 
                        className={`h-2.5 rounded-full ${
                          item.attendance_percentage < 5 ? 'bg-red-600' : 'bg-primary'
                        }`}
                        style={{ width: `${Math.min(100, item.attendance_percentage)}%` }}
                      ></div>
                    </div>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td 
                colSpan={(period === 'weekly' || period === 'monthly' || period === 'custom') ? 6 : 5} 
                className="px-6 py-4 text-center text-sm text-gray-500"
              >
                No data available for the selected period
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default DetailedTable;
