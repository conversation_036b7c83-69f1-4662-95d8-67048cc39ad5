'use client';

import React, { useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { formatDate } from '@/utils/dateFormatters';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface DayStatistics {
  date: string;
  day_of_week: string;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

interface MonthStatistics {
  month: string;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  attendance_percentage: number;
}

type StatisticsData = DayStatistics | MonthStatistics;

interface TrendChartProps {
  data: StatisticsData[];
  period: 'weekly' | 'monthly' | 'annual' | 'custom';
}

const TrendChart: React.FC<TrendChartProps> = ({ data, period }) => {
  // Format labels based on period
  const formatLabel = (item: StatisticsData) => {
    if ('month' in item) {
      return item.month;
    } else {
      return formatDate(item.date, period);
    }
  };

  // Prepare chart data
  const chartData = useMemo(() => {
    const labels = data.map(item => formatLabel(item));
    const attendanceData = data.map(item => item.attendance_percentage);
    
    // Find outliers (0% attendance days)
    const outliers = data.map((item, index) => 
      item.attendance_percentage === 0 ? index : -1
    ).filter(index => index !== -1);
    
    return {
      labels,
      datasets: [
        {
          label: 'Attendance %',
          data: attendanceData,
          borderColor: 'rgba(59, 130, 246, 1)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true,
          tension: 0.4,
          pointBackgroundColor: (context: any) => {
            // Highlight outliers in red
            const index = context.dataIndex;
            return outliers.includes(index) ? 'rgba(239, 68, 68, 1)' : 'rgba(59, 130, 246, 1)';
          },
          pointRadius: (context: any) => {
            // Make outliers larger
            const index = context.dataIndex;
            return outliers.includes(index) ? 6 : 3;
          },
          pointHoverRadius: 8,
          pointBorderWidth: 2,
          pointBorderColor: 'white',
        }
      ]
    };
  }, [data, period]);

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const index = context.dataIndex;
            const item = data[index];
            
            let tooltipLines = [`Attendance: ${item.attendance_percentage.toFixed(1)}%`];
            
            if ('present_count' in item) {
              tooltipLines.push(`Present: ${item.present_count}`);
              tooltipLines.push(`Absent: ${item.absent_count}`);
              
              if ('late_count' in item) {
                tooltipLines.push(`Late: ${item.late_count}`);
              }
              
              if ('on_leave_count' in item) {
                tooltipLines.push(`On Leave: ${item.on_leave_count}`);
              }
            }
            
            return tooltipLines;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Attendance %'
        },
        ticks: {
          callback: (value: any) => `${value}%`
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      }
    }
  };

  return (
    <div className="h-80">
      {data.length > 0 ? (
        <Line data={chartData} options={options} />
      ) : (
        <div className="h-full flex items-center justify-center">
          <p className="text-gray-500">No data available for the selected period</p>
        </div>
      )}
    </div>
  );
};

export default TrendChart;
