'use client';

import React, { useState } from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  department_id: string | null;
  department_name: string | null;
  status: string;
  attendance: {
    status: string;
    attendance_id?: string;
    check_in_time?: string | null;
    check_out_time?: string | null;
    total_hours?: number | null;
    reason?: string;
  };
}

interface AttendanceEmployeeListProps {
  presentEmployees: Employee[];
  absentEmployees: Employee[];
  onLeaveEmployees: Employee[];
  date: string;
}

const AttendanceEmployeeList: React.FC<AttendanceEmployeeListProps> = ({
  presentEmployees,
  absentEmployees,
  onLeaveEmployees,
  date
}) => {
  const [activeTab, setActiveTab] = useState<'present' | 'absent' | 'leave'>('present');
  
  // Format time for display (HH:MM:SS -> HH:MM AM/PM)
  const formatTime = (timeString: string | null | undefined) => {
    if (!timeString) return 'N/A';
    
    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      return timeString;
    }
  };
  
  return (
    <DashboardCard title={`Employee Attendance - ${date}`}>
      <div className="mb-4">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('present')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'present'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-secondary hover:text-secondary-dark hover:border-gray-300'
              }`}
            >
              Present ({presentEmployees.length})
            </button>
            <button
              onClick={() => setActiveTab('absent')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'absent'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-secondary hover:text-secondary-dark hover:border-gray-300'
              }`}
            >
              Absent ({absentEmployees.length})
            </button>
            <button
              onClick={() => setActiveTab('leave')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'leave'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-secondary hover:text-secondary-dark hover:border-gray-300'
              }`}
            >
              On Leave ({onLeaveEmployees.length})
            </button>
          </nav>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        {activeTab === 'present' && (
          <>
            {presentEmployees.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-secondary">No employees present on this date.</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Check In
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Check Out
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Hours
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {presentEmployees.map((employee) => (
                    <tr key={employee.employee_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
                            <span className="text-sm font-medium">
                              {employee.first_name.charAt(0)}
                              {employee.last_name.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-secondary-dark">
                              {employee.full_name}
                            </div>
                            <div className="text-xs text-secondary">
                              {employee.position || 'No position'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.department_name || 'No Department'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {formatTime(employee.attendance.check_in_time)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {formatTime(employee.attendance.check_out_time)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.attendance.total_hours !== null && employee.attendance.total_hours !== undefined
                            ? `${employee.attendance.total_hours.toFixed(2)} hrs`
                            : 'N/A'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </>
        )}
        
        {activeTab === 'absent' && (
          <>
            {absentEmployees.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-secondary">No employees absent on this date.</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Reason
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {absentEmployees.map((employee) => (
                    <tr key={employee.employee_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-red-100 text-red-600 flex items-center justify-center">
                            <span className="text-sm font-medium">
                              {employee.first_name.charAt(0)}
                              {employee.last_name.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-secondary-dark">
                              {employee.full_name}
                            </div>
                            <div className="text-xs text-secondary">
                              {employee.email || 'No email'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.department_name || 'No Department'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.position || 'No position'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-red-600">
                          {employee.attendance.reason || 'No reason provided'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </>
        )}
        
        {activeTab === 'leave' && (
          <>
            {onLeaveEmployees.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-secondary">No employees on leave on this date.</p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Reason
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {onLeaveEmployees.map((employee) => (
                    <tr key={employee.employee_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center">
                            <span className="text-sm font-medium">
                              {employee.first_name.charAt(0)}
                              {employee.last_name.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-secondary-dark">
                              {employee.full_name}
                            </div>
                            <div className="text-xs text-secondary">
                              {employee.email || 'No email'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.department_name || 'No Department'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.position || 'No position'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-yellow-600">
                          {employee.attendance.reason || 'No reason provided'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </>
        )}
      </div>
    </DashboardCard>
  );
};

export default AttendanceEmployeeList;
