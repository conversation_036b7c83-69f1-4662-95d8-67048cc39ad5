'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

interface DepartmentStatistics {
  department_id: string;
  department_name: string;
  employee_count: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  avg_attendance_percentage: number;
}

interface DepartmentStatisticsTableProps {
  departmentStatistics: DepartmentStatistics[];
  isLoading: boolean;
}

const DepartmentStatisticsTable: React.FC<DepartmentStatisticsTableProps> = ({
  departmentStatistics,
  isLoading
}) => {
  return (
    <DashboardCard title="Department Statistics">
      {isLoading ? (
        <div className="py-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="mt-2 text-secondary">Loading department statistics...</p>
        </div>
      ) : departmentStatistics.length === 0 ? (
        <div className="py-8 text-center">
          <p className="text-secondary">No department statistics available.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Department
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Employees
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Present
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Absent
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Late
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  On Leave
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Attendance %
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {departmentStatistics.map((dept, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-secondary-dark">
                      {dept.department_name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary-dark">
                      {dept.employee_count}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-green-600 font-medium">
                      {dept.present_count}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-red-600 font-medium">
                      {dept.absent_count}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-yellow-600 font-medium">
                      {dept.late_count}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-purple-600 font-medium">
                      {dept.on_leave_count}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm text-secondary-dark mr-2">
                        {dept.avg_attendance_percentage.toFixed(2)}%
                      </div>
                      <div className="w-24 bg-gray-200 rounded-full h-2.5">
                        <div 
                          className="bg-primary h-2.5 rounded-full" 
                          style={{ width: `${Math.min(100, dept.avg_attendance_percentage)}%` }}
                        ></div>
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </DashboardCard>
  );
};

export default DepartmentStatisticsTable;
