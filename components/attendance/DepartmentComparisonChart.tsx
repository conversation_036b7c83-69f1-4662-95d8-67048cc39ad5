'use client';

import React, { useMemo } from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface DepartmentStatistics {
  department_id: string;
  department_name: string;
  employee_count: number;
  present_count: number;
  absent_count: number;
  late_count: number;
  on_leave_count: number;
  avg_attendance_percentage: number;
}

interface DepartmentComparisonChartProps {
  departments: DepartmentStatistics[];
}

const DepartmentComparisonChart: React.FC<DepartmentComparisonChartProps> = ({ departments }) => {
  // Sort departments by attendance percentage
  const sortedDepartments = useMemo(() => {
    return [...departments].sort((a, b) => b.avg_attendance_percentage - a.avg_attendance_percentage);
  }, [departments]);

  // Prepare chart data
  const chartData = useMemo(() => {
    const labels = sortedDepartments.map(dept => dept.department_name);
    const attendanceData = sortedDepartments.map(dept => dept.avg_attendance_percentage);
    
    // Determine bar colors based on performance threshold
    const barColors = sortedDepartments.map(dept => 
      dept.avg_attendance_percentage < 5 ? 'rgba(239, 68, 68, 0.8)' : 'rgba(59, 130, 246, 0.8)'
    );
    
    const borderColors = sortedDepartments.map(dept => 
      dept.avg_attendance_percentage < 5 ? 'rgba(239, 68, 68, 1)' : 'rgba(59, 130, 246, 1)'
    );
    
    return {
      labels,
      datasets: [
        {
          label: 'Attendance %',
          data: attendanceData,
          backgroundColor: barColors,
          borderColor: borderColors,
          borderWidth: 1,
          borderRadius: 4,
          maxBarThickness: 50,
        }
      ]
    };
  }, [sortedDepartments]);

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const index = context.dataIndex;
            const dept = sortedDepartments[index];
            
            return [
              `Attendance: ${dept.avg_attendance_percentage.toFixed(1)}%`,
              `Employees: ${dept.employee_count}`,
              `Present: ${dept.present_count}`,
              `Absent: ${dept.absent_count}`
            ];
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Attendance %'
        },
        ticks: {
          callback: (value: any) => `${value}%`
        }
      },
      y: {
        grid: {
          display: false
        }
      }
    }
  };

  return (
    <div className="h-80">
      {departments.length > 0 ? (
        <Bar data={chartData} options={options} />
      ) : (
        <div className="h-full flex items-center justify-center">
          <p className="text-gray-500">No department data available</p>
        </div>
      )}
    </div>
  );
};

export default DepartmentComparisonChart;
