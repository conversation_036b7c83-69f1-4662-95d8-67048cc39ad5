'use client';

import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface PeriodSelectorProps {
  selectedPeriod: 'weekly' | 'monthly' | 'annual' | 'custom';
  onPeriodChange: (period: 'weekly' | 'monthly' | 'annual' | 'custom') => void;
  startDate: Date | null;
  endDate: Date | null;
  onStartDateChange: (date: Date | null) => void;
  onEndDateChange: (date: Date | null) => void;
}

const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  selectedPeriod,
  onPeriodChange,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => onPeriodChange('weekly')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedPeriod === 'weekly'
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            aria-label="View weekly attendance"
          >
            Week
          </button>
          <button
            onClick={() => onPeriodChange('monthly')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedPeriod === 'monthly'
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            aria-label="View monthly attendance"
          >
            Month
          </button>
          <button
            onClick={() => onPeriodChange('annual')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedPeriod === 'annual'
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            aria-label="View annual attendance"
          >
            Year
          </button>
          <button
            onClick={() => onPeriodChange('custom')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedPeriod === 'custom'
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
            aria-label="View custom period attendance"
          >
            Custom
          </button>
        </div>
        
        {selectedPeriod === 'custom' && (
          <div className="flex flex-wrap gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-1">
                Start Date
              </label>
              <DatePicker
                selected={startDate}
                onChange={onStartDateChange}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                dateFormat="yyyy-MM-dd"
                maxDate={endDate || new Date()}
                aria-label="Select start date"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-1">
                End Date
              </label>
              <DatePicker
                selected={endDate}
                onChange={onEndDateChange}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                dateFormat="yyyy-MM-dd"
                minDate={startDate || undefined}
                maxDate={new Date()}
                aria-label="Select end date"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PeriodSelector;
