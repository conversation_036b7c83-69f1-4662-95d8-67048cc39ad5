'use client';

import React, { useState } from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

interface AIInsights {
  behavioral_patterns: {
    anomalies: string[];
    consistency_level: string;
    punctuality_trend: string;
    work_hours_pattern: string;
  };
  performance_summary: {
    areas_for_improvement: string[];
    overall_score: number;
    risk_level: string;
    strengths: string[];
  };
  predictive_indicators: {
    attendance_risk: string;
    burnout_risk: string;
    performance_trend: string;
  };
  recommendations: {
    immediate_actions: string[];
    long_term_goals: string[];
    manager_actions: string[];
  };
}

interface EmployeeAIInsightsProps {
  insights: AIInsights;
}

const EmployeeAIInsights: React.FC<EmployeeAIInsightsProps> = ({ insights }) => {
  const [activeTab, setActiveTab] = useState<'summary' | 'patterns' | 'predictions' | 'recommendations'>('summary');

  // Check if insights data exists
  if (!insights) {
    return (
      <DashboardCard title="AI-Powered Insights">
        <div className="py-8 text-center">
          <p className="text-gray-500">No AI insights available</p>
        </div>
      </DashboardCard>
    );
  }

  // Helper function to get risk level styling
  const getRiskLevelStyle = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case 'low':
        return 'text-green-700 bg-green-100 border-green-200';
      case 'medium':
        return 'text-yellow-700 bg-yellow-100 border-yellow-200';
      case 'high':
        return 'text-red-700 bg-red-100 border-red-200';
      default:
        return 'text-gray-700 bg-gray-100 border-gray-200';
    }
  };

  // Helper function to get consistency level styling
  const getConsistencyStyle = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high':
        return 'text-green-700 bg-green-100';
      case 'medium':
        return 'text-yellow-700 bg-yellow-100';
      case 'low':
        return 'text-red-700 bg-red-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  return (
    <DashboardCard title="AI-Powered Insights">
      <div className="space-y-6">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('summary')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'summary'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Performance Summary
            </button>
            <button
              onClick={() => setActiveTab('patterns')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'patterns'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Behavioral Patterns
            </button>
            <button
              onClick={() => setActiveTab('predictions')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'predictions'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Predictions
            </button>
            <button
              onClick={() => setActiveTab('recommendations')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'recommendations'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Recommendations
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'summary' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className={`p-4 rounded-lg border ${getRiskLevelStyle(insights.performance_summary.risk_level)}`}>
                  <h4 className="font-medium mb-2">Overall Performance</h4>
                  <div className="text-2xl font-bold mb-1">
                    {insights.performance_summary.overall_score.toFixed(1)}/100
                  </div>
                  <div className="text-sm capitalize">
                    Risk Level: {insights.performance_summary.risk_level}
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium text-green-700 mb-2">Strengths</h5>
                    <ul className="space-y-1">
                      {insights.performance_summary.strengths.map((strength, index) => (
                        <li key={index} className="text-sm text-gray-700 flex items-center">
                          <span className="text-green-500 mr-2">✓</span>
                          {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <div>
                <h5 className="font-medium text-red-700 mb-2">Areas for Improvement</h5>
                <ul className="space-y-1">
                  {insights.performance_summary.areas_for_improvement.map((area, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-center">
                      <span className="text-red-500 mr-2">!</span>
                      {area}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'patterns' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className={`p-4 rounded-lg ${getConsistencyStyle(insights.behavioral_patterns.consistency_level)}`}>
                  <h4 className="font-medium mb-1">Consistency Level</h4>
                  <div className="text-lg font-semibold capitalize">
                    {insights.behavioral_patterns.consistency_level}
                  </div>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium mb-1 text-blue-700">Punctuality Trend</h4>
                  <div className="text-lg font-semibold text-blue-800 capitalize">
                    {insights.behavioral_patterns.punctuality_trend}
                  </div>
                </div>
              </div>

              <div className="p-4 bg-purple-50 rounded-lg">
                <h4 className="font-medium mb-1 text-purple-700">Work Hours Pattern</h4>
                <div className="text-lg font-semibold text-purple-800 capitalize">
                  {insights.behavioral_patterns.work_hours_pattern}
                </div>
              </div>

              {insights.behavioral_patterns.anomalies.length > 0 && (
                <div>
                  <h5 className="font-medium text-orange-700 mb-2">Detected Anomalies</h5>
                  <ul className="space-y-1">
                    {insights.behavioral_patterns.anomalies.map((anomaly, index) => (
                      <li key={index} className="text-sm text-gray-700 flex items-center">
                        <span className="text-orange-500 mr-2">⚠</span>
                        {anomaly}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {activeTab === 'predictions' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className={`p-4 rounded-lg border ${getRiskLevelStyle(insights.predictive_indicators.attendance_risk)}`}>
                <h4 className="font-medium mb-2">Attendance Risk</h4>
                <div className="text-lg font-semibold capitalize">
                  {insights.predictive_indicators.attendance_risk}
                </div>
              </div>

              <div className={`p-4 rounded-lg border ${getRiskLevelStyle(insights.predictive_indicators.burnout_risk)}`}>
                <h4 className="font-medium mb-2">Burnout Risk</h4>
                <div className="text-lg font-semibold capitalize">
                  {insights.predictive_indicators.burnout_risk}
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium mb-2 text-blue-700">Performance Trend</h4>
                <div className="text-lg font-semibold text-blue-800 capitalize">
                  {insights.predictive_indicators.performance_trend}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'recommendations' && (
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-red-700 mb-3">Immediate Actions</h4>
                <ul className="space-y-2">
                  {insights.recommendations.immediate_actions.map((action, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="text-red-500 mr-2 mt-0.5">🔥</span>
                      {action}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-blue-700 mb-3">Manager Actions</h4>
                <ul className="space-y-2">
                  {insights.recommendations.manager_actions.map((action, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="text-blue-500 mr-2 mt-0.5">👥</span>
                      {action}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-green-700 mb-3">Long-term Goals</h4>
                <ul className="space-y-2">
                  {insights.recommendations.long_term_goals.map((goal, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="text-green-500 mr-2 mt-0.5">🎯</span>
                      {goal}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardCard>
  );
};

export default EmployeeAIInsights;
