'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

interface EmployeeStatsOverviewProps {
  data: {
    employee: {
      employee_id: string;
      first_name: string;
      last_name: string;
      full_name: string;
      email: string;
      phone_number: string;
      position: string;
      status: string;
      hire_date: string;
      id_number: string;
      tenure_days: number;
      department: {
        department_id: string;
        name: string;
      };
    };
    period: {
      end_date: string;
      period_description: string;
      reference_date: string;
      start_date: string;
      total_days: number;
      type: string;
      working_days: number;
    };
    statistics: {
      attendance: {
        absent_days: number;
        attendance_rate: number;
        consistency_score: number;
        late_days: number;
        on_leave_days: number;
        present_days: number;
        punctuality_rate: number;
      };
      time: {
        average_daily_hours: number;
        efficiency_rate: number;
        expected_hours: number;
        overtime_hours: number;
        productivity_score: number;
        total_hours: number;
        undertime_hours: number;
      };
      shift_compliance: {
        compliance_rate: number;
        compliant_days: number;
        early_arrivals: number;
        early_departures: number;
        has_shifts: boolean;
        late_arrivals: number;
        late_departures: number;
        shift_violations: string[];
        total_shift_days: number;
      };
    };
    ai_insights: {
      performance_summary: {
        areas_for_improvement: string[];
        overall_score: number;
        risk_level: string;
        strengths: string[];
      };
    };
  };
}

const EmployeeStatsOverview: React.FC<EmployeeStatsOverviewProps> = ({ data }) => {
  const { employee, period, statistics, ai_insights } = data;

  // Calculate tenure in years and months
  const tenureYears = Math.floor(employee.tenure_days / 365);
  const tenureMonths = Math.floor((employee.tenure_days % 365) / 30);

  // Get risk level color
  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case 'low':
        return 'text-green-600 bg-green-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'high':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Employee Info Header */}
      <DashboardCard title="Employee Overview">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-secondary-dark">{employee.full_name}</h3>
            <p className="text-sm text-gray-600">{employee.position}</p>
            <p className="text-xs text-gray-500">{employee.department.name}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600">Tenure</p>
            <p className="text-lg font-semibold text-secondary-dark">
              {tenureYears > 0 && `${tenureYears}y `}
              {tenureMonths > 0 && `${tenureMonths}m`}
              {tenureYears === 0 && tenureMonths === 0 && 'New'}
            </p>
            <p className="text-xs text-gray-500">Since {new Date(employee.hire_date).toLocaleDateString()}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600">Period</p>
            <p className="text-lg font-semibold text-secondary-dark">{period.period_description}</p>
            <p className="text-xs text-gray-500">{period.working_days} working days</p>
          </div>

          <div className={`p-4 rounded-lg ${getRiskLevelColor(ai_insights.performance_summary.risk_level)}`}>
            <p className="text-sm">Risk Level</p>
            <p className="text-lg font-semibold capitalize">{ai_insights.performance_summary.risk_level}</p>
            <p className="text-xs">Score: {ai_insights.performance_summary.overall_score.toFixed(1)}/100</p>
          </div>
        </div>
      </DashboardCard>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Attendance Rate */}
        <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 font-medium">Attendance Rate</p>
              <h3 className="text-2xl font-bold text-green-600 mt-1">
                {(statistics.attendance.attendance_rate).toFixed(1)}%
              </h3>
            </div>
            <div className="p-3 bg-green-50 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            {statistics.attendance.present_days} of {period.working_days} days
          </p>
        </div>

        {/* Punctuality Rate */}
        <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 font-medium">Punctuality</p>
              <h3 className="text-2xl font-bold text-blue-600 mt-1">
                {statistics.attendance.punctuality_rate.toFixed(1)}%
              </h3>
            </div>
            <div className="p-3 bg-blue-50 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            {statistics.attendance.late_days} late days
          </p>
        </div>

        {/* Hours Worked */}
        <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 font-medium">Hours Worked</p>
              <h3 className="text-2xl font-bold text-purple-600 mt-1">
                {statistics.time.total_hours.toFixed(1)}h
              </h3>
            </div>
            <div className="p-3 bg-purple-50 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Expected: {statistics.time.expected_hours.toFixed(1)}h
          </p>
        </div>

        {/* Productivity Score */}
        <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 font-medium">Productivity</p>
              <h3 className="text-2xl font-bold text-orange-600 mt-1">
                {statistics.time.productivity_score}/100
              </h3>
            </div>
            <div className="p-3 bg-orange-50 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Efficiency: {(statistics.time.efficiency_rate).toFixed(1)}%
          </p>
        </div>
      </div>

      {/* Detailed Breakdown */}
      <DashboardCard title="Attendance Breakdown">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{statistics.attendance.present_days}</div>
            <div className="text-sm text-gray-600">Present Days</div>
          </div>

          <div className="text-center p-4 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{statistics.attendance.absent_days}</div>
            <div className="text-sm text-gray-600">Absent Days</div>
          </div>

          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{statistics.attendance.late_days}</div>
            <div className="text-sm text-gray-600">Late Days</div>
          </div>

          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{statistics.attendance.on_leave_days}</div>
            <div className="text-sm text-gray-600">Leave Days</div>
          </div>
        </div>

        {statistics.shift_compliance.has_shifts && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Shift Compliance</h4>
            <div className="flex items-center space-x-4">
              <div className="text-lg font-semibold text-secondary-dark">
                {(statistics.shift_compliance.compliance_rate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">
                Compliance Rate
              </div>
            </div>
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default EmployeeStatsOverview;
