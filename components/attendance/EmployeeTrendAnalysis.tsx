'use client';

import React from 'react';
import DashboardCard from '@/components/ui/DashboardCard';

interface PeriodComparison {
  attendance_rate: number;
  end_date: string;
  period: string;
  present_days: number;
  start_date: string;
  working_days: number;
}

interface Trends {
  improvement_areas: string[];
  period_comparison: PeriodComparison[];
  recommendations: string[];
  trend_direction: string;
  trend_strength: number;
}

interface EmployeeTrendAnalysisProps {
  trends: Trends;
  period: 'weekly' | 'monthly' | 'annual';
}

const EmployeeTrendAnalysis: React.FC<EmployeeTrendAnalysisProps> = ({ trends, period }) => {
  // Check if trends and period_comparison exist
  if (!trends || !trends.period_comparison || !Array.isArray(trends.period_comparison)) {
    return (
      <DashboardCard title="Trend Analysis">
        <div className="py-8 text-center">
          <p className="text-gray-500">No trend data available</p>
        </div>
      </DashboardCard>
    );
  }

  // Get the best and worst performing periods
  const sortedPeriods = [...trends.period_comparison].sort((a, b) => b.attendance_rate - a.attendance_rate);
  const bestPeriod = sortedPeriods[0];
  const worstPeriod = sortedPeriods[sortedPeriods.length - 1];

  // Calculate average attendance rate
  const averageAttendance = trends.period_comparison.length > 0
    ? trends.period_comparison.reduce((sum, period) => sum + period.attendance_rate, 0) / trends.period_comparison.length
    : 0;

  // Get trend direction styling
  const getTrendStyle = () => {
    switch (trends.trend_direction.toLowerCase()) {
      case 'improving':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'declining':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'stable':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  // Format period name for display
  const formatPeriodName = (periodString: string) => {
    if (period === 'annual' && periodString.includes('2025')) {
      return periodString.split(' ')[0]; // Extract month name
    }
    return periodString;
  };

  return (
    <DashboardCard title="Trend Analysis">
      <div className="space-y-6">
        {/* Overall Trend Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className={`p-4 rounded-lg border ${getTrendStyle()}`}>
            <h4 className="font-medium mb-2">Trend Direction</h4>
            <div className="text-lg font-semibold capitalize">
              {trends.trend_direction}
            </div>
            <div className="text-sm mt-1">
              Strength: {(trends.trend_strength * 100).toFixed(1)}%
            </div>
          </div>

          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h4 className="font-medium mb-2">Average Attendance</h4>
            <div className="text-lg font-semibold">
              {averageAttendance.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600 mt-1">
              Across all periods
            </div>
          </div>

          <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
            <h4 className="font-medium mb-2 text-purple-700">Total Present Days</h4>
            <div className="text-lg font-semibold text-purple-800">
              {trends.period_comparison.reduce((sum, period) => sum + period.present_days, 0)}
            </div>
            <div className="text-sm text-purple-600 mt-1">
              Out of {trends.period_comparison.reduce((sum, period) => sum + period.working_days, 0)} working days
            </div>
          </div>
        </div>

        {/* Best and Worst Periods */}
        {bestPeriod && worstPeriod && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 className="font-medium text-green-700 mb-2">Best Period</h4>
              <div className="text-lg font-semibold text-green-800">
                {formatPeriodName(bestPeriod.period)}
              </div>
              <div className="text-sm text-green-600 mt-1">
                {bestPeriod.attendance_rate.toFixed(1)}% attendance ({bestPeriod.present_days}/{bestPeriod.working_days} days)
              </div>
            </div>

            <div className="p-4 bg-red-50 rounded-lg border border-red-200">
              <h4 className="font-medium text-red-700 mb-2">Needs Attention</h4>
              <div className="text-lg font-semibold text-red-800">
                {formatPeriodName(worstPeriod.period)}
              </div>
              <div className="text-sm text-red-600 mt-1">
                {worstPeriod.attendance_rate.toFixed(1)}% attendance ({worstPeriod.present_days}/{worstPeriod.working_days} days)
              </div>
            </div>
          </div>
        )}

        {/* Period-by-Period Breakdown */}
        <div>
          <h4 className="font-medium text-secondary-dark mb-4">Period Breakdown</h4>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Present Days
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Working Days
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Attendance Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Performance
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {trends.period_comparison.map((period, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatPeriodName(period.period)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {period.present_days}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {period.working_days}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {period.attendance_rate.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2.5 mr-2">
                          <div
                            className={`h-2.5 rounded-full ${
                              period.attendance_rate >= 80 ? 'bg-green-600' :
                              period.attendance_rate >= 60 ? 'bg-yellow-600' :
                              period.attendance_rate >= 40 ? 'bg-orange-600' : 'bg-red-600'
                            }`}
                            style={{ width: `${Math.min(100, period.attendance_rate)}%` }}
                          ></div>
                        </div>
                        <span className={`text-xs font-medium ${
                          period.attendance_rate >= 80 ? 'text-green-600' :
                          period.attendance_rate >= 60 ? 'text-yellow-600' :
                          period.attendance_rate >= 40 ? 'text-orange-600' : 'text-red-600'
                        }`}>
                          {period.attendance_rate >= 80 ? 'Excellent' :
                           period.attendance_rate >= 60 ? 'Good' :
                           period.attendance_rate >= 40 ? 'Fair' : 'Poor'}
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Improvement Areas */}
        {trends.improvement_areas.length > 0 && (
          <div>
            <h4 className="font-medium text-orange-700 mb-3">Areas for Improvement</h4>
            <ul className="space-y-2">
              {trends.improvement_areas.map((area, index) => (
                <li key={index} className="text-sm text-gray-700 flex items-center">
                  <span className="text-orange-500 mr-2">📈</span>
                  {area}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Trend Recommendations */}
        {trends.recommendations.length > 0 && (
          <div>
            <h4 className="font-medium text-blue-700 mb-3">Trend-Based Recommendations</h4>
            <ul className="space-y-2">
              {trends.recommendations.map((recommendation, index) => (
                <li key={index} className="text-sm text-gray-700 flex items-center">
                  <span className="text-blue-500 mr-2">💡</span>
                  {recommendation}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </DashboardCard>
  );
};

export default EmployeeTrendAnalysis;
