'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  id_number?: string | null;
  department_id: string | null;
  created_at?: string;
  updated_at?: string;
}

interface EmployeeSelectorProps {
  selectedEmployee: string;
  onEmployeeChange: (employeeId: string) => void;
  isLoading?: boolean;
  employeeData?: Employee | null; // Optional employee data to display instead of fetching
}

const EmployeeSelector: React.FC<EmployeeSelectorProps> = ({
  selectedEmployee,
  onEmployeeChange,
  isLoading = false,
  employeeData = null
}) => {
  const { companies } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedEmployeeData, setSelectedEmployeeData] = useState<Employee | null>(null);

  // Debounced search function
  const searchEmployees = useCallback(async (searchQuery: string) => {
    if (!companies || companies.length === 0) return;

    try {
      setIsSearching(true);
      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      let endpoint = `api/employees?company_id=${companyId}`;
      if (searchQuery.trim()) {
        endpoint += `&search=${encodeURIComponent(searchQuery.trim())}`;
      }

      const response = await apiGet<{extend: {employees: Employee[]}, msg: string}>(
        endpoint,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.employees) {
        setEmployees(response.extend.employees);
      }
    } catch (error) {
      console.error('Error searching employees:', error);
      setEmployees([]);
    } finally {
      setIsSearching(false);
    }
  }, [companies]);

  // Fetch selected employee details
  const fetchSelectedEmployee = useCallback(async () => {
    if (!selectedEmployee || !companies || companies.length === 0) {
      setSelectedEmployeeData(null);
      return;
    }

    try {
      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{extend: {employees: Employee[]}, msg: string}>(
        `api/employees?company_id=${companyId}&employee_id=${selectedEmployee}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.employees && response.extend.employees.length > 0) {
        setSelectedEmployeeData(response.extend.employees[0]);
      }
    } catch (error) {
      console.error('Error fetching selected employee:', error);
      setSelectedEmployeeData(null);
    }
  }, [selectedEmployee, companies]);

  // Load initial employees and selected employee
  useEffect(() => {
    if (isOpen && searchTerm === '') {
      searchEmployees('');
    }
  }, [isOpen, searchEmployees, searchTerm]);

  useEffect(() => {
    fetchSelectedEmployee();
  }, [fetchSelectedEmployee]);

  // Debounce search
  useEffect(() => {
    if (!isOpen) return;

    const timeoutId = setTimeout(() => {
      searchEmployees(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, searchEmployees, isOpen]);

  const handleEmployeeSelect = (employeeId: string) => {
    onEmployeeChange(employeeId);
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm">
      <label className="block text-sm font-medium text-secondary-dark mb-2">
        Select Employee
      </label>

      <div className="relative">
        <button
          onClick={() => !isLoading && setIsOpen(!isOpen)}
          disabled={isLoading}
          className="w-full px-4 py-3 text-left bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isLoading ? (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                <span className="text-gray-500">Loading employees...</span>
              </div>
            </div>
          ) : (employeeData || selectedEmployeeData) ? (
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-secondary-dark">
                  {(employeeData || selectedEmployeeData)?.full_name}
                </div>
                <div className="text-sm text-gray-500">
                  {(employeeData || selectedEmployeeData)?.position || 'No Position'}
                </div>
              </div>
              <svg
                className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <span className="text-gray-500">
                {employees.length === 0 ? 'No employees available' : 'Select an employee...'}
              </span>
              <svg
                className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          )}
        </button>

        {isOpen && !isLoading && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg max-h-60 overflow-auto">
            <div className="p-2">
              <input
                type="text"
                placeholder="Search employees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                autoFocus
              />
            </div>

            <div className="max-h-48 overflow-y-auto">
              {isSearching ? (
                <div className="px-4 py-3 text-sm text-gray-500 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                    Searching employees...
                  </div>
                </div>
              ) : employees.length > 0 ? (
                employees.map((employee) => (
                  <button
                    key={employee.employee_id}
                    onClick={() => handleEmployeeSelect(employee.employee_id)}
                    className={`w-full px-4 py-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 ${
                      selectedEmployee === employee.employee_id ? 'bg-primary-50 border-r-2 border-primary' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-primary">
                            {employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-secondary-dark truncate">
                          {employee.full_name}
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {employee.position || 'No Position'}
                        </div>
                        <div className="text-xs text-gray-400 truncate">
                          {employee.email || 'No Email'}
                        </div>
                      </div>
                      {employee.status === 'active' ? (
                        <div className="flex-shrink-0">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                          </span>
                        </div>
                      ) : (
                        <div className="flex-shrink-0">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {employee.status}
                          </span>
                        </div>
                      )}
                    </div>
                  </button>
                ))
              ) : (
                <div className="px-4 py-3 text-sm text-gray-500 text-center">
                  {searchTerm ? `No employees found matching "${searchTerm}"` : 'No employees available'}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Close dropdown when clicking outside */}
      {isOpen && !isLoading && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default EmployeeSelector;
