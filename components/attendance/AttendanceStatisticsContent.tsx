'use client';

import React from 'react';
import AttendanceStatistics from './AttendanceStatistics';
import Breadcrumb from '@/components/ui/Breadcrumb';
import Link from 'next/link';

const AttendanceStatisticsContent: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Attendance Statistics</h1>
        <div className="flex space-x-3">
          <Link
            href="/dashboard/hr/attendance"
            className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Back to Attendance</span>
          </Link>
          <Link
            href="/dashboard/hr/attendance/daily"
            className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-primary rounded-md shadow-sm hover:bg-primary-dark hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
            style={{ color: 'white' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <span className="text-white">Daily Report</span>
          </Link>
        </div>
      </div>

      {/* Breadcrumbs */}
      <Breadcrumb
        items={[
          { label: 'Dashboard', href: '/dashboard/hr' },
          { label: 'Attendance', href: '/dashboard/hr/attendance' },
          { label: 'Statistics', isActive: true }
        ]}
      />

      {/* The AttendanceStatistics component will handle all the data fetching and period selection */}
      <AttendanceStatistics />
    </div>
  );
};

export default AttendanceStatisticsContent;
