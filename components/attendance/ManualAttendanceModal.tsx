'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  department_id: string | null;
  department_name: string | null;
  status: string;
}

interface ManualAttendanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const ManualAttendanceModal: React.FC<ManualAttendanceModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { companies, user } = useAuth();
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form state
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [checkInTime, setCheckInTime] = useState('09:00');
  const [checkOutTime, setCheckOutTime] = useState('17:00');
  const [status, setStatus] = useState('present');
  const [notes, setNotes] = useState('');

  // Fetch employees when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchEmployees();
    }
  }, [isOpen]);

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      setIsLoading(true);

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiGet<{extend: {employees: Employee[]}}>(
        `api/employees?company_id=${companyId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.extend && response.extend.employees) {
        // Filter to only active employees
        const activeEmployees = response.extend.employees.filter(emp => emp.status === 'active');
        setEmployees(activeEmployees);

        // Select the first employee by default
        if (activeEmployees.length > 0) {
          setSelectedEmployee(activeEmployees[0].employee_id);
        }
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch employees');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);
      setError('');
      setSuccess('');

      if (!companies || companies.length === 0) {
        throw new Error('No company found');
      }

      if (!selectedEmployee) {
        throw new Error('Please select an employee');
      }

      if (!selectedDate) {
        throw new Error('Please select a date');
      }

      if (!checkInTime) {
        throw new Error('Please enter check-in time');
      }

      if (!checkOutTime) {
        throw new Error('Please enter check-out time');
      }

      const companyId = companies[0].company_id;
      const { apiPost } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      // Format date and times
      const formattedDate = selectedDate.toISOString().split('T')[0];
      const checkInDateTime = `${formattedDate} ${checkInTime}:00`;
      const checkOutDateTime = `${formattedDate} ${checkOutTime}:00`;

      const attendanceData = {
        company_id: companyId,
        employee_id: selectedEmployee,
        check_in_time: checkInDateTime,
        check_out_time: checkOutDateTime,
        source: 'manual',
        status: status,
        notes: notes,
        created_by: user?.id || ''
      };

      await apiPost('api/attendance', attendanceData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Attendance record created successfully');

      // Reset form
      setSelectedEmployee(employees.length > 0 ? employees[0].employee_id : '');
      setSelectedDate(new Date());
      setCheckInTime('09:00');
      setCheckOutTime('17:00');
      setStatus('present');
      setNotes('');

      // Close modal and refresh data after a short delay
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 1500);

    } catch (error: any) {
      setError(error.message || 'Failed to create attendance record');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[100] overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-2xl border border-gray-200 text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Manual Attendance Entry
                </h3>

                {error && (
                  <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {success && (
                  <div className="mt-2 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
                    {success}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="mt-4">
                  <div className="space-y-4">
                    {/* Employee Selection */}
                    <div>
                      <label htmlFor="employee" className="block text-sm font-medium text-secondary-dark mb-1">
                        Employee
                      </label>
                      <select
                        id="employee"
                        value={selectedEmployee}
                        onChange={(e) => setSelectedEmployee(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <option>Loading employees...</option>
                        ) : employees.length === 0 ? (
                          <option>No employees available</option>
                        ) : (
                          <>
                            <option value="">Select an employee</option>
                            {employees.map((emp) => (
                              <option key={emp.employee_id} value={emp.employee_id}>
                                {emp.full_name} ({emp.position || 'No position'})
                              </option>
                            ))}
                          </>
                        )}
                      </select>
                    </div>

                    {/* Date */}
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-1">
                        Date
                      </label>
                      <DatePicker
                        selected={selectedDate}
                        onChange={(date) => date && setSelectedDate(date)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        dateFormat="yyyy-MM-dd"
                        maxDate={new Date()}
                      />
                    </div>

                    {/* Check-in and Check-out Times */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="checkInTime" className="block text-sm font-medium text-secondary-dark mb-1">
                          Check-in Time
                        </label>
                        <input
                          type="time"
                          id="checkInTime"
                          value={checkInTime}
                          onChange={(e) => setCheckInTime(e.target.value)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label htmlFor="checkOutTime" className="block text-sm font-medium text-secondary-dark mb-1">
                          Check-out Time
                        </label>
                        <input
                          type="time"
                          id="checkOutTime"
                          value={checkOutTime}
                          onChange={(e) => setCheckOutTime(e.target.value)}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                      </div>
                    </div>

                    {/* Status */}
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-secondary-dark mb-1">
                        Status
                      </label>
                      <select
                        id="status"
                        value={status}
                        onChange={(e) => setStatus(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                        <option value="late">Late</option>
                        <option value="on_leave">On Leave</option>
                      </select>
                    </div>

                    {/* Notes */}
                    <div>
                      <label htmlFor="notes" className="block text-sm font-medium text-secondary-dark mb-1">
                        Notes
                      </label>
                      <textarea
                        id="notes"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        rows={3}
                        placeholder="Add any additional notes here..."
                      />
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : 'Submit'}
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManualAttendanceModal;
