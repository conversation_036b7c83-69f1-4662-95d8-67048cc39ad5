// components/dashboards/EmployeeDashboard.tsx
"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { apiGet } from '@/lib/api';
import { getAccessToken } from '@/lib/auth';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import DashboardChart from '@/components/ui/DashboardChart';
import EmployeeAnnouncementsWidget from '@/components/announcements/EmployeeAnnouncementsWidget';

// Extended User interface to match your API response
interface EmployeeInfo {
  created_at: string;
  department_id: string;
  email: string;
  employee_id: string;
  first_name: string;
  full_name: string;
  hire_date: string;
  id_number: string | null;
  last_name: string;
  phone_number: string | null;
  position: string | null;
  status: string;
  employee_type_id: string | null;
  updated_at: string;
}

interface ExtendedUser {
  employee_id: string;
  employee_info: EmployeeInfo;
  id: string;
  name: string;
  role: string;
  username: string;
}

interface AttendanceRecord {
  attendance_id?: string;
  employee_id: string;
  date: string;
  clock_in_time: string | null;
  clock_out_time: string | null;
  status: 'Present' | 'Absent' | 'Late' | 'In Progress';
  hours_worked?: number;
  created_at?: string;
  updated_at?: string;
}

// New interfaces for the statistics API response
interface AttendanceStatistics {
  ai_insights: {
    behavioral_patterns: {
      anomalies: string[];
      consistency_level: string;
      punctuality_trend: string;
      work_hours_pattern: string;
    };
    performance_summary: {
      areas_for_improvement: string[];
      overall_score: number;
      risk_level: string;
      strengths: string[];
    };
    predictive_indicators: {
      attendance_risk: string;
      burnout_risk: string;
      performance_trend: string;
    };
    recommendations: {
      immediate_actions: string[];
      long_term_goals: string[];
      manager_actions: string[];
    };
  };
  comparisons: {
    company: {
      avg_attendance_rate: number;
      avg_efficiency_rate: number;
      avg_punctuality_rate: number;
      employee_rank: number;
      employee_rates: Record<string, number>;
      total_employees: number;
    };
    department: {
      avg_attendance_rate: number;
      avg_efficiency_rate: number;
      avg_punctuality_rate: number;
      employee_count: number;
      employee_rank: number;
      employee_rates: Record<string, number>;
      name: string;
    };
  };
  employee: {
    department: {
      department_id: string;
      name: string;
    };
    email: string;
    employee_id: string;
    first_name: string;
    full_name: string;
    hire_date: string;
    id_number: string;
    last_name: string;
    phone_number: string;
    position: string;
    status: string;
    tenure_days: number;
  };
  period: {
    end_date: string;
    period_description: string;
    reference_date: string;
    start_date: string;
    total_days: number;
    type: string;
    working_days: number;
  };
  statistics: {
    attendance: {
      absent_days: number;
      attendance_rate: number;
      consistency_score: number;
      late_days: number;
      on_leave_days: number;
      present_days: number;
      punctuality_rate: number;
    };
    shift_compliance: {
      compliance_rate: number;
      compliant_days: number;
      early_arrivals: number;
      early_departures: number;
      has_shifts: boolean;
      late_arrivals: number;
      late_departures: number;
      shift_violations: any[];
      total_shift_days: number;
    };
    time: {
      average_daily_hours: number;
      efficiency_rate: number;
      expected_hours: number;
      overtime_hours: number;
      productivity_score: number;
      total_hours: number;
      undertime_hours: number;
    };
  };
  status: string;
  time_patterns: {
    average_check_in_time: string | null;
    average_check_out_time: string | null;
    day_of_week_patterns: Record<string, any>;
    most_common_check_in_hour: number | null;
    most_common_check_out_hour: number | null;
    time_consistency: number;
  };
  trends: {
    improvement_areas: string[];
    period_comparison: Array<{
      attendance_rate: number;
      end_date: string;
      period: string;
      present_days: number;
      start_date: string;
      working_days: number;
    }>;
    recommendations: string[];
    trend_direction: string;
    trend_strength: number;
  };
}

interface Shift {
  shift_id?: string;
  employee_id: string;
  date: string;
  start_time: string;
  end_time: string;
  shift_type: string;
  location?: string;
  status?: string;
}



interface LeaveType {
  leave_type_id: string;
  name: string;
  code: string;
  description: string;
  is_paid: boolean;
  requires_approval: boolean;
  requires_documentation: boolean;
  created_at: string;
  updated_at: string;
}

interface LeaveTypesResponse {
  code: number;
  extend: {
    leave_types: LeaveType[];
  };
  msg: string;
}



// Mock data for development
const mockAttendanceData: AttendanceRecord[] = [
  { employee_id: 'emp_001', date: '2025-05-31', clock_in_time: '08:00 AM', clock_out_time: '05:00 PM', status: 'Present' },
  { employee_id: 'emp_001', date: '2025-05-30', clock_in_time: '08:15 AM', clock_out_time: '05:00 PM', status: 'Late' },
  { employee_id: 'emp_001', date: '2025-05-29', clock_in_time: '08:00 AM', clock_out_time: '05:00 PM', status: 'Present' },
  { employee_id: 'emp_001', date: '2025-05-28', clock_in_time: '08:00 AM', clock_out_time: '05:00 PM', status: 'Present' },
  { employee_id: 'emp_001', date: '2025-05-27', clock_in_time: '08:00 AM', clock_out_time: '05:00 PM', status: 'Present' },
];

const mockShifts: Shift[] = [
  { employee_id: 'emp_001', date: '2025-06-01', start_time: '08:00', end_time: '17:00', shift_type: 'Day Shift', location: 'Main Office' },
  { employee_id: 'emp_001', date: '2025-06-02', start_time: '08:00', end_time: '17:00', shift_type: 'Day Shift', location: 'Main Office' },
  { employee_id: 'emp_001', date: '2025-06-03', start_time: '08:00', end_time: '17:00', shift_type: 'Day Shift', location: 'Main Office' },
];



const EmployeeDashboard = () => {
  const { user, companies } = useAuth();
  // Type assertion to use the extended user type
  const extendedUser = user as ExtendedUser;
  const [loading, setLoading] = useState({
    stats: false,
    attendance: false,
    shifts: false
  });

  // Employee stats state
  const [employeeStats, setEmployeeStats] = useState({
    hoursThisWeek: '0',
    leaveBalance: '0 days',
    attendanceRate: '0%',
    pendingRequests: '0',
    isLoading: true
  });

  // Data states
  const [recentAttendance, setRecentAttendance] = useState<AttendanceRecord[]>([]);
  const [upcomingShifts, setUpcomingShifts] = useState<Shift[]>([]);
  const [departmentInfo, setDepartmentInfo] = useState<{
    department_id: string;
    name: string;
    description: string;
    manager_id: string | null;
    created_at: string;
    updated_at: string;
  } | null>(null);

  // New state for attendance statistics
  const [attendanceStatistics, setAttendanceStatistics] = useState<AttendanceStatistics | null>(null);

  // Leave types state
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);

  // Period state for statistics (weekly, monthly, annual)
  const [selectedPeriod, setSelectedPeriod] = useState<'weekly' | 'monthly' | 'annual'>('monthly');

  // Memoized stats for performance optimization
  const stats = useMemo(() => {
    if (attendanceStatistics) {
      return [
        {
          title: `Hours (${selectedPeriod})`,
          value: `${attendanceStatistics.statistics.time.total_hours}h`,
          change: attendanceStatistics.statistics.time.overtime_hours > 0 ? `+${attendanceStatistics.statistics.time.overtime_hours}h` : '0h',
          changeType: attendanceStatistics.statistics.time.overtime_hours > 0 ? 'positive' as const : 'neutral' as const
        },
        {
          title: 'Attendance Rate',
          value: `${attendanceStatistics.statistics.attendance.attendance_rate}%`,
          change: attendanceStatistics.trends.trend_direction === 'stable' ? '0%' : attendanceStatistics.trends.trend_direction === 'improving' ? '+5%' : '-5%',
          changeType: attendanceStatistics.trends.trend_direction === 'improving' ? 'positive' as const : attendanceStatistics.trends.trend_direction === 'declining' ? 'negative' as const : 'neutral' as const
        },
        {
          title: 'Punctuality Rate',
          value: `${attendanceStatistics.statistics.attendance.punctuality_rate}%`,
          change: attendanceStatistics.statistics.attendance.late_days === 0 ? '+0%' : `-${attendanceStatistics.statistics.attendance.late_days}%`,
          changeType: attendanceStatistics.statistics.attendance.late_days === 0 ? 'positive' as const : 'negative' as const
        },
        {
          title: 'Performance Score',
          value: `${attendanceStatistics.ai_insights.performance_summary.overall_score}/100`,
          change: attendanceStatistics.ai_insights.performance_summary.risk_level === 'low' ? '+5' : attendanceStatistics.ai_insights.performance_summary.risk_level === 'high' ? '-10' : '0',
          changeType: attendanceStatistics.ai_insights.performance_summary.risk_level === 'low' ? 'positive' as const : attendanceStatistics.ai_insights.performance_summary.risk_level === 'high' ? 'negative' as const : 'neutral' as const
        },
      ];
    }

    return [
      { title: 'Hours This Week', value: employeeStats.isLoading ? '...' : employeeStats.hoursThisWeek, change: '+2.5', changeType: 'positive' as const },
      { title: 'Leave Balance', value: employeeStats.isLoading ? '...' : employeeStats.leaveBalance, change: '0', changeType: 'neutral' as const },
      { title: 'Attendance Rate', value: employeeStats.isLoading ? '...' : employeeStats.attendanceRate, change: '+1%', changeType: 'positive' as const },
      { title: 'Pending Requests', value: employeeStats.isLoading ? '...' : employeeStats.pendingRequests, change: '-1', changeType: 'positive' as const },
    ];
  }, [employeeStats, attendanceStatistics, selectedPeriod]);



  // Fetch leave types function
  const fetchLeaveTypes = useCallback(async () => {
    try {
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      // Get company_id from the stored auth data directly
      let companyId = null;

      // First try to get from companies array
      if (companies && companies.length > 0) {
        companyId = companies[0].company_id;
      } else {
        // If companies array is empty, try to get from stored auth data
        const authData = localStorage.getItem('kazisync_auth');
        if (authData) {
          try {
            const parsedData = JSON.parse(authData);
            // Check if company object exists (from login response)
            if (parsedData.company && parsedData.company.company_id) {
              companyId = parsedData.company.company_id;
            }
            // Also check if company_id is directly in the token payload
            else if (parsedData.company_id) {
              companyId = parsedData.company_id;
            }
          } catch (error) {
            // Silently handle parsing errors
          }
        }
      }

      if (!companyId) {
        return;
      }

      const response = await apiGet<LeaveTypesResponse>(
        `api/leave/types?company_id=${companyId}`,
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );

      if (response.extend && response.extend.leave_types) {
        setLeaveTypes(response.extend.leave_types);
      }
    } catch (error: any) {
      console.warn('Leave types API not available:', error);
    }
  }, [companies]);

  // Memoized function to fetch employee data for performance
  const fetchEmployeeData = useCallback(async () => {
    try {
      // Get company_id using robust method that handles both response structures
      let companyId = null;

      // First try to get from companies array
      if (companies && companies.length > 0) {
        companyId = companies[0].company_id;
      } else {
        // If companies array is empty, try to get from stored auth data
        const authData = localStorage.getItem('kazisync_auth');
        if (authData) {
          try {
            const parsedData = JSON.parse(authData);
            // Check if company object exists (from login response)
            if (parsedData.company && parsedData.company.company_id) {
              companyId = parsedData.company.company_id;
            }
            // Also check if company_id is directly in the token payload
            else if (parsedData.company_id) {
              companyId = parsedData.company_id;
            }
          } catch (error) {
            // Silently handle parsing errors
          }
        }
      }

      if (!companyId) {
        // Use mock data if no company info
        setRecentAttendance(mockAttendanceData);
        setUpcomingShifts(mockShifts);
        setEmployeeStats({
          hoursThisWeek: '32.5',
          leaveBalance: '15 days',
          attendanceRate: '95%',
          pendingRequests: '2',
          isLoading: false
        });
        return;
      }

      // Get employee ID from user object
      let employeeId = null;
      if (extendedUser?.employee_id) {
        employeeId = extendedUser.employee_id;
      } else if (extendedUser?.employee_info?.employee_id) {
        employeeId = extendedUser.employee_info.employee_id;
      }

      if (!employeeId) {
        // Use mock data if no employee ID
        setRecentAttendance(mockAttendanceData);
        setUpcomingShifts(mockShifts);
        setEmployeeStats({
          hoursThisWeek: '32.5',
          leaveBalance: '15 days',
          attendanceRate: '95%',
          pendingRequests: '2',
          isLoading: false
        });
        return;
      }

      // companyId is already set above
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      setLoading({ stats: true, attendance: true, shifts: true });

      // Fetch employee attendance statistics
      try {
        // Build the URL with period parameter
        let statisticsUrl = `api/attendance/employee/${employeeId}/statistics?company_id=${companyId}`;
        if (selectedPeriod !== 'monthly') {
          statisticsUrl += `&period=${selectedPeriod}`;
        }

        const statisticsResponse = await apiGet<AttendanceStatistics>(
          statisticsUrl,
          {
            headers: { 'Authorization': `Bearer ${token}` }
          }
        );

        if (statisticsResponse) {
          setAttendanceStatistics(statisticsResponse);

          // Update stats with real data from statistics
          setEmployeeStats(prev => ({
            ...prev,
            hoursThisWeek: statisticsResponse.statistics.time.total_hours.toString(),
            attendanceRate: `${statisticsResponse.statistics.attendance.attendance_rate.toFixed(1)}%`,
            leaveBalance: `${statisticsResponse.statistics.attendance.on_leave_days} days`,
            isLoading: false
          }));

          // Update department info from statistics response
          if (statisticsResponse.employee.department) {
            setDepartmentInfo({
              department_id: statisticsResponse.employee.department.department_id,
              name: statisticsResponse.employee.department.name,
              description: '',
              manager_id: null,
              created_at: '',
              updated_at: ''
            });
          }
        }
      } catch (error) {
        console.warn('Attendance statistics API not available, using mock data:', error);
        // Set mock data for attendance statistics
        setRecentAttendance(mockAttendanceData);
        setEmployeeStats(prev => ({
          ...prev,
          hoursThisWeek: '32.5',
          attendanceRate: '95%',
          leaveBalance: '15 days',
          isLoading: false
        }));
      }

      // Fetch employee shifts
      try {
        const shiftsResponse = await apiGet<{
          code: number;
          extend: {
            assignments: Array<{
              assignment_id: string;
              employee_id: string;
              shift_id: string;
              effective_start_date: string;
              effective_end_date: string;
              custom_start_time: string;
              custom_end_time: string;
              custom_break_duration: number;
              custom_working_days: string | null;
              is_active: boolean;
              shift: {
                shift_id: string;
                name: string;
                start_time: string;
                end_time: string;
                break_duration: number;
                working_days: string;
                description: string;
                is_night_shift: boolean;
                is_flexible: boolean;
              };
            }>;
          };
        }>(`api/employee-shifts?company_id=${companyId}&employee_id=${employeeId}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (shiftsResponse.extend && shiftsResponse.extend.assignments) {
          // Convert assignments to shift format for display
          const upcomingShiftsData = shiftsResponse.extend.assignments
            .filter(assignment => assignment.is_active)
            .slice(0, 3)
            .map(assignment => ({
              employee_id: assignment.employee_id,
              date: new Date().toISOString().split('T')[0], // Today's date as placeholder
              start_time: assignment.custom_start_time,
              end_time: assignment.custom_end_time,
              shift_type: assignment.shift.name,
              location: 'Office', // Default location
              status: 'scheduled'
            }));
          setUpcomingShifts(upcomingShiftsData);
        }
      } catch (error) {
        console.warn('Shifts API not available, using mock data:', error);
        setUpcomingShifts(mockShifts);
      }



      // Fetch department information if employee has department_id
      if (extendedUser?.employee_info?.department_id) {
        try {
          const departmentResponse = await apiGet<{
            departments: Array<{
              department_id: string;
              name: string;
              description: string;
              manager_id: string | null;
              created_at: string;
              updated_at: string;
            }>;
            success: boolean;
          }>(`api/departments?company_id=${companyId}`, {
            headers: { 'Authorization': `Bearer ${token}` }
          });

          if (departmentResponse.departments) {
            const userDepartment = departmentResponse.departments.find(
              dept => dept.department_id === extendedUser.employee_info.department_id
            );
            if (userDepartment) {
              setDepartmentInfo(userDepartment);
            }
          }
        } catch (error) {
          console.warn('Department API not available:', error);
        }
      }

      setEmployeeStats(prev => ({
        ...prev,
        isLoading: false
      }));

      setLoading({ stats: false, attendance: false, shifts: false });

    } catch (error) {
      console.warn('Employee data APIs not available, using mock data:', error);
      // Fallback to mock data
      setRecentAttendance(mockAttendanceData);
      setUpcomingShifts(mockShifts);
      setEmployeeStats({
        hoursThisWeek: '32.5',
        leaveBalance: '15 days',
        attendanceRate: '95%',
        pendingRequests: '2',
        isLoading: false
      });
      setLoading({ stats: false, attendance: false, shifts: false });
    }
  }, [companies, extendedUser, selectedPeriod]);

  // Fetch data when component mounts - optimized with memoized function
  useEffect(() => {
    fetchEmployeeData();
  }, [fetchEmployeeData]);

  // Fetch leave types separately
  useEffect(() => {
    fetchLeaveTypes();
  }, [fetchLeaveTypes]);



  return (
    <div className="space-y-6">
      {/* Enhanced Header with user info - Modern Clean Design */}
      <div className="bg-white border border-gray-200 rounded-xl p-6">
        <div className="flex flex-col lg:flex-row justify-between items-start gap-6">
          {/* Left Section - User Info */}
          <div className="flex items-start space-x-4 flex-1">
            {/* User Avatar */}
            <div className="flex-shrink-0">
              <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold text-blue-600">
                  {(extendedUser?.employee_info?.first_name || extendedUser?.name || 'E').charAt(0).toUpperCase()}
                </span>
              </div>
            </div>

            {/* User Details */}
            <div className="flex-1 min-w-0">
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {extendedUser?.employee_info?.first_name || extendedUser?.name || 'Employee'}!
              </h1>
              <p className="text-gray-600 mt-1">
                {extendedUser?.employee_info?.position || 'Employee'} • {departmentInfo?.name || 'Department'} • {companies?.[0]?.company_name || 'Company'}
              </p>
            </div>
          </div>

          {/* Right Section - Quick Actions */}
          <div className="flex flex-col sm:flex-row gap-3 lg:flex-shrink-0">
            <Link
              href="/dashboard/employee/attendance"
              className="bg-gray-100 hover:bg-gray-200 hover:text-gray-700 text-gray-700 py-3 px-4 text-sm font-medium rounded-lg transition-colors flex items-center justify-center"
              aria-label="View attendance records"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              Attendance
            </Link>
            <Link
              href="/dashboard/employee/shifts"
              className="bg-gray-100 hover:bg-gray-200 hover:text-gray-700 text-gray-700 py-3 px-4 text-sm font-medium rounded-lg transition-colors flex items-center justify-center"
              aria-label="View shift schedule"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Shifts
            </Link>
            <Link
              href="/dashboard/employee/leave/new"
              className="bg-blue-600 hover:bg-blue-700 hover:text-white text-white py-3 px-4 text-sm font-medium rounded-lg transition-colors flex items-center justify-center"
              aria-label="Request leave"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Request Leave
            </Link>
          </div>
        </div>
      </div>

      {/* Quick Info Grid - Separate section for better desktop layout */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-gray-500 text-sm font-medium">Employee ID</div>
          <div className="font-semibold text-gray-900 truncate mt-1" title={extendedUser?.employee_info?.employee_id}>
            {extendedUser?.employee_info?.employee_id?.slice(-8) || 'N/A'}
          </div>
        </div> */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-gray-500 text-sm font-medium">Status</div>
          <div className="flex items-center mt-1">
            <div className={`w-2 h-2 rounded-full mr-2 ${
              extendedUser?.employee_info?.status === 'active' ? 'bg-green-500' : 'bg-gray-400'
            }`}></div>
            <span className="font-semibold text-gray-900 capitalize">{extendedUser?.employee_info?.status || 'Active'}</span>
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-gray-500 text-sm font-medium">Department</div>
          <div className="font-semibold text-gray-900 truncate mt-1" title={departmentInfo?.name}>
            {departmentInfo?.name || 'Not assigned'}
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-gray-500 text-sm font-medium">Today</div>
          <div className="font-semibold text-gray-900 mt-1">{new Date().toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}</div>
        </div>
      </div>



      {/* Period Selector and Stats Cards */}
      <div className="space-y-6">
        {/* Period Selector */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-lg font-semibold text-gray-900">Performance Overview</h2>
          <div className="flex space-x-2">
            {(['weekly', 'monthly', 'annual'] as const).map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  selectedPeriod === period
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {stats.map((stat, index) => (
            <DashboardStats
              key={index}
              title={stat.title}
              value={stat.value}
              change={stat.change}
              changeType={stat.changeType}
              loading={loading.stats}
            />
          ))}
        </div>
      </div>

      {/* AI Insights Section */}
      {attendanceStatistics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance Summary */}
          <DashboardCard title="AI Performance Insights" loading={loading.stats}>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Overall Score</span>
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-2 ${
                    attendanceStatistics.ai_insights.performance_summary.risk_level === 'low' ? 'bg-green-500' :
                    attendanceStatistics.ai_insights.performance_summary.risk_level === 'medium' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-lg font-bold">{attendanceStatistics.ai_insights.performance_summary.overall_score}/100</span>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Strengths</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {attendanceStatistics.ai_insights.performance_summary.strengths.map((strength, index) => (
                      <li key={index} className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        {strength}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Areas for Improvement</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {attendanceStatistics.ai_insights.performance_summary.areas_for_improvement.map((area, index) => (
                      <li key={index} className="flex items-center">
                        <svg className="w-4 h-4 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {area}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </DashboardCard>

          {/* Recommendations */}
          <DashboardCard title="AI Recommendations" loading={loading.stats}>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Immediate Actions</h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  {attendanceStatistics.ai_insights.recommendations.immediate_actions.map((action, index) => (
                    <li key={index} className="flex items-start">
                      <svg className="w-4 h-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      {action}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Long-term Goals</h4>
                <ul className="text-sm text-gray-600 space-y-2">
                  {attendanceStatistics.ai_insights.recommendations.long_term_goals.map((goal, index) => (
                    <li key={index} className="flex items-start">
                      <svg className="w-4 h-4 text-purple-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      {goal}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </DashboardCard>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Attendance Chart */}
        <div className="lg:col-span-2">
          <DashboardCard title={`${selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)} Attendance Trends`} loading={loading.stats}>
            <div className="h-80">
              <DashboardChart
                type="bar"
                labels={attendanceStatistics?.trends.period_comparison.map(p => p.period) || ['Week 1', 'Week 2', 'Week 3', 'Week 4']}
                datasets={[
                  {
                    label: 'Attendance Rate (%)',
                    data: attendanceStatistics?.trends.period_comparison.map(p => p.attendance_rate) || [40, 38, 42, 32.5],
                    backgroundColor: 'rgba(0, 123, 255, 0.7)',
                  },
                  {
                    label: 'Present Days',
                    data: attendanceStatistics?.trends.period_comparison.map(p => p.present_days) || [5, 4, 5, 3],
                    backgroundColor: 'rgba(40, 167, 69, 0.7)',
                  },
                ]}
                loading={loading.stats}
              />
            </div>
          </DashboardCard>
        </div>

        {/* Attendance Breakdown */}
        <div>
          <DashboardCard title="Attendance Breakdown" loading={loading.stats}>
            <div className="h-80">
              <DashboardChart
                type="doughnut"
                labels={['Present Days', 'Absent Days', 'Late Days', 'Leave Days']}
                datasets={[
                  {
                    data: attendanceStatistics ? [
                      attendanceStatistics.statistics.attendance.present_days,
                      attendanceStatistics.statistics.attendance.absent_days,
                      attendanceStatistics.statistics.attendance.late_days,
                      attendanceStatistics.statistics.attendance.on_leave_days
                    ] : [5, 3, 2, 15],
                    backgroundColor: ['#28A745', '#DC3545', '#FFC107', '#007BFF'],
                  },
                ]}
                loading={loading.stats}
              />
              <div className="mt-4 text-center space-y-1">
                {attendanceStatistics ? (
                  <>
                    <p className="text-sm text-gray-700">
                      Present: <span className="font-semibold text-green-600">{attendanceStatistics.statistics.attendance.present_days} days</span>
                    </p>
                    <p className="text-sm text-gray-700">
                      Absent: <span className="font-semibold text-red-600">{attendanceStatistics.statistics.attendance.absent_days} days</span>
                    </p>
                    <p className="text-sm text-gray-700">
                      Late: <span className="font-semibold text-yellow-600">{attendanceStatistics.statistics.attendance.late_days} days</span>
                    </p>
                    <p className="text-sm text-gray-700">
                      Leave: <span className="font-semibold text-blue-600">{attendanceStatistics.statistics.attendance.on_leave_days} days</span>
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-sm text-gray-700">
                      Present: <span className="font-semibold">5 days</span>
                    </p>
                    <p className="text-sm text-gray-700">
                      Absent: <span className="font-semibold">3 days</span>
                    </p>
                    <p className="text-sm text-gray-700">
                      Late: <span className="font-semibold">2 days</span>
                    </p>
                  </>
                )}
              </div>
            </div>
          </DashboardCard>
        </div>
      </div>

      {/* Recent Attendance */}
      <DashboardCard 
        title="Recent Attendance" 
        loading={loading.attendance}
        action={
          <Link href="/dashboard/employee/attendance" className="text-sm text-blue-600 hover:text-blue-800">
            View full history
          </Link>
        }
      >
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {['Date', 'Day', 'Clock In', 'Clock Out', 'Status'].map((header) => (
                  <th 
                    key={header}
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentAttendance.length > 0 ? (
                recentAttendance.map((record, index) => {
                  // Get day name from date
                  const dayName = new Date(record.date).toLocaleDateString('en-US', { weekday: 'long' });

                  return (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{record.date}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{dayName}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{record.clock_in_time || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{record.clock_out_time || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            record.status === 'Present'
                              ? 'bg-green-100 text-green-800'
                              : record.status === 'In Progress'
                              ? 'bg-blue-100 text-blue-800'
                              : record.status === 'Late'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {record.status}
                        </span>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    {loading.attendance ? 'Loading...' : 'No attendance records found'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </DashboardCard>

      {/* Available Leave Types */}
      <DashboardCard
        title="Available Leave Types"
        loading={loading.stats}
        action={
          <Link href="/dashboard/employee/leave" className="text-sm text-blue-600 hover:text-blue-800">
            View all leave options
          </Link>
        }
      >

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {leaveTypes.slice(0, 3).map((leaveType) => (
            <div key={leaveType.leave_type_id} className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
              <div className="flex items-start justify-between mb-2 gap-2">
                <h3 className="text-sm font-medium text-gray-900 flex-1 min-w-0">{leaveType.name}</h3>
                <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 whitespace-nowrap flex-shrink-0 max-w-[80px] truncate">
                  {leaveType.code}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{leaveType.description}</p>
              <div className="space-y-2">
                <div className="flex items-center text-xs">
                  <span className={`w-2 h-2 rounded-full mr-2 flex-shrink-0 ${leaveType.is_paid ? 'bg-green-500' : 'bg-red-500'}`}></span>
                  <span className="text-gray-600">{leaveType.is_paid ? 'Paid Leave' : 'Unpaid Leave'}</span>
                </div>
                {leaveType.requires_approval && (
                  <div className="flex items-center text-xs">
                    <span className="w-2 h-2 rounded-full mr-2 bg-yellow-500 flex-shrink-0"></span>
                    <span className="text-gray-600">Requires Approval</span>
                  </div>
                )}
                {leaveType.requires_documentation && (
                  <div className="flex items-center text-xs">
                    <span className="w-2 h-2 rounded-full mr-2 bg-purple-500 flex-shrink-0"></span>
                    <span className="text-gray-600">Requires Documentation</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        {leaveTypes.length === 0 && !loading.stats && (
          <div className="text-center py-8">
            <p className="text-gray-500">No leave types available</p>
          </div>
        )}
        {leaveTypes.length > 3 && (
          <div className="text-center mt-4">
            <Link href="/dashboard/employee/leave" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              View all {leaveTypes.length} leave types →
            </Link>
          </div>
        )}
      </DashboardCard>

      {/* Additional Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Upcoming Shifts */}
        <DashboardCard 
          title="Upcoming Shifts"
          loading={loading.shifts}
          action={
            <Link href="/dashboard/employee/shifts" className="text-sm text-blue-600 hover:text-blue-800">
              View all
            </Link>
          }
        >
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {upcomingShifts.length > 0 ? (
              upcomingShifts.map((shift, index) => {
                // Get day name from date
                const dayName = new Date(shift.date).toLocaleDateString('en-US', { weekday: 'long' });
                // Format time range with proper time formatting
                const formatTime = (timeString: string) => {
                  try {
                    const [hours, minutes] = timeString.split(':');
                    const date = new Date();
                    date.setHours(parseInt(hours), parseInt(minutes));
                    return date.toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    });
                  } catch (error) {
                    return timeString;
                  }
                };
                const timeRange = `${formatTime(shift.start_time)} - ${formatTime(shift.end_time)}`;

                return (
                  <div key={index} className="border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-900">
                        Today ({dayName})
                      </span>
                      <span className="text-xs text-gray-500">{shift.location || 'Office'}</span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      <span className="font-medium">{shift.shift_type}</span> • {timeRange}
                    </p>
                  </div>
                );
              })
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">
                {loading.shifts ? 'Loading shifts...' : 'No upcoming shifts'}
              </p>
            )}
          </div>
        </DashboardCard>

        {/* Announcements */}
        <EmployeeAnnouncementsWidget limit={5} showViewAll={true} />
      </div>
    </div>
  );
};

export default EmployeeDashboard;