"use client";

import React from 'react';
import Link from 'next/link';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import Dashboard<PERSON>hart from '@/components/ui/DashboardChart';

const AdminDashboard = () => {
  // Mock data for demonstration
  const stats = [
    { title: 'Total Employees', value: '124', change: '+5%', changeType: 'positive' },
    { title: 'Attendance Rate', value: '94%', change: '+2%', changeType: 'positive' },
    { title: 'Leave Requests', value: '8', change: '-3%', changeType: 'positive' },
    { title: 'Active Shifts', value: '4', change: '0%', changeType: 'neutral' },
  ];

  const recentActivities = [
    { id: 1, type: 'Clock In', employee: '<PERSON>', time: '08:02 AM', status: 'On Time' },
    { id: 2, type: 'Leave Request', employee: '<PERSON>', time: '09:15 AM', status: 'Pending' },
    { id: 3, type: 'Clock Out', employee: '<PERSON>', time: '05:30 PM', status: 'On Time' },
    { id: 4, type: 'Shift Change', employee: '<PERSON>', time: '02:45 PM', status: 'Approved' },
    { id: 5, type: 'Clock In', employee: '<PERSON> <PERSON>', time: '08:10 AM', status: 'Late' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Company Admin Dashboard</h1>
        <div>
          <button className="btn-primary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Employee
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType as 'positive' | 'negative' | 'neutral'}
          />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Attendance Trend Chart */}
        <div className="lg:col-span-2">
          <DashboardCard title="Attendance Trend">
            <div className="h-80">
              <DashboardChart
                type="line"
                labels={['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']}
                datasets={[
                  {
                    label: 'Present',
                    data: [115, 118, 120, 112, 110, 45, 40],
                    borderColor: '#28A745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                  },
                  {
                    label: 'Absent',
                    data: [9, 6, 4, 12, 14, 5, 3],
                    borderColor: '#DC3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                  },
                ]}
              />
            </div>
          </DashboardCard>
        </div>

        {/* Department Distribution */}
        <div>
          <DashboardCard title="Department Distribution">
            <div className="h-80">
              <DashboardChart
                type="doughnut"
                labels={['IT', 'HR', 'Finance', 'Marketing', 'Operations']}
                datasets={[
                  {
                    data: [30, 15, 20, 25, 34],
                    backgroundColor: ['#007BFF', '#28A745', '#FFC107', '#17A2B8', '#6C757D'],
                  },
                ]}
              />
            </div>
          </DashboardCard>
        </div>
      </div>

      {/* Recent Activities */}
      <DashboardCard title="Recent Activities">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Activity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentActivities.map((activity) => (
                <tr key={activity.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-secondary-dark">{activity.type}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary">{activity.employee}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary">{activity.time}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        activity.status === 'On Time'
                          ? 'bg-green-100 text-green-800'
                          : activity.status === 'Late'
                          ? 'bg-yellow-100 text-yellow-800'
                          : activity.status === 'Pending'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {activity.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link href="#" className="text-primary hover:text-primary-dark">
                      View
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </DashboardCard>

      {/* Additional Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DashboardCard title="Leave Requests">
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {[1, 2, 3, 4].map((_, index) => (
              <div key={index} className="border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-secondary-dark">
                    {['Annual Leave', 'Sick Leave', 'Personal Leave', 'Vacation'][index % 4]}
                  </span>
                  <span
                    className={`px-2 text-xs leading-5 font-semibold rounded-full ${
                      index === 0
                        ? 'bg-yellow-100 text-yellow-800'
                        : index === 1
                        ? 'bg-green-100 text-green-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}
                  >
                    {index === 0 ? 'Pending' : index === 1 ? 'Approved' : 'New'}
                  </span>
                </div>
                <p className="text-xs text-secondary mt-1">
                  <span className="font-medium">
                    {['John Doe', 'Jane Smith', 'Michael Brown', 'Sarah Johnson'][index % 4]}
                  </span>{' '}
                  • {index === 0 ? 'May 15-20' : index === 1 ? 'May 10-12' : 'May 25-30'}
                </p>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Link href="/dashboard/admin/leave" className="text-sm text-primary hover:text-primary-dark">
              View all leave requests
            </Link>
          </div>
        </DashboardCard>

        <DashboardCard title="Device Activity">
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {[1, 2, 3, 4].map((_, index) => (
              <div key={index} className="border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-secondary-dark">
                    {['Main Entrance', 'IT Department', 'HR Office', 'Finance Department'][index % 4]}
                  </span>
                  <span
                    className={`px-2 text-xs leading-5 font-semibold rounded-full ${
                      index === 0 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {index === 0 ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <p className="text-xs text-secondary mt-1">
                  Last activity: {index === 0 ? '2 minutes ago' : index === 1 ? '1 hour ago' : '3 hours ago'}
                </p>
                <p className="text-xs text-secondary mt-1">
                  {index === 0 ? '24 clock-ins today' : index === 1 ? '15 clock-ins today' : '8 clock-ins today'}
                </p>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Link href="/dashboard/admin/devices" className="text-sm text-primary hover:text-primary-dark">
              View all devices
            </Link>
          </div>
        </DashboardCard>
      </div>
    </div>
  );
};

export default AdminDashboard;
