"use client";

import React from 'react';
import <PERSON> from 'next/link';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import Dashboard<PERSON>hart from '@/components/ui/DashboardChart';

const ManagerDashboard = () => {
  // Mock data for demonstration
  const stats = [
    { title: 'Team Members', value: '18', change: '+2%', changeType: 'positive' },
    { title: 'Present Today', value: '16', change: '+5%', changeType: 'positive' },
    { title: 'On Leave', value: '2', change: '0%', changeType: 'neutral' },
    { title: 'Pending Requests', value: '3', change: '-1%', changeType: 'positive' },
  ];

  const teamMembers = [
    { id: 1, name: '<PERSON>', position: 'Senior Developer', status: 'Present', clockIn: '08:02 AM' },
    { id: 2, name: '<PERSON>', position: '<PERSON>eloper', status: 'On Leave', clockIn: '-' },
    { id: 3, name: '<PERSON>', position: 'UI/UX Designer', status: 'Present', clockIn: '08:30 AM' },
    { id: 4, name: '<PERSON>', position: 'QA Engineer', status: 'Present', clockIn: '08:15 AM' },
    { id: 5, name: '<PERSON>', position: 'Developer', status: 'Late', clockIn: '09:10 AM' },
  ];

  const pendingRequests = [
    { id: 1, type: 'Leave Request', employee: '<PERSON> <PERSON>', date: 'May 15-20', reason: 'Vacation' },
    { id: 2, type: 'Attendance Correction', employee: 'Emily Davis', date: 'May 10', reason: 'Forgot to clock in' },
    { id: 3, type: 'Shift Change', employee: 'Robert Wilson', date: 'May 25', reason: 'Doctor appointment' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Manager Dashboard</h1>
        <div className="flex space-x-2">
          <button className="btn-primary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Team Report
          </button>
          <button className="btn-secondary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            Add Company
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType as 'positive' | 'negative' | 'neutral'}
          />
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Team Attendance Chart */}
        <div className="lg:col-span-2">
          <DashboardCard title="Team Attendance (Last 7 Days)">
            <div className="h-80">
              <DashboardChart
                type="line"
                labels={['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']}
                datasets={[
                  {
                    label: 'Present',
                    data: [16, 17, 18, 16, 15, 8, 7],
                    borderColor: '#28A745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                  },
                  {
                    label: 'Absent',
                    data: [2, 1, 0, 2, 3, 1, 1],
                    borderColor: '#DC3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                  },
                  {
                    label: 'Late',
                    data: [1, 0, 2, 1, 0, 0, 0],
                    borderColor: '#FFC107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                  },
                ]}
              />
            </div>
          </DashboardCard>
        </div>

        {/* Team Performance */}
        <div>
          <DashboardCard title="Team Performance">
            <div className="h-80">
              <DashboardChart
                type="radar"
                labels={['Productivity', 'Quality', 'Timeliness', 'Attendance', 'Collaboration']}
                datasets={[
                  {
                    label: 'Current Month',
                    data: [85, 90, 78, 92, 88],
                    backgroundColor: 'rgba(0, 123, 255, 0.2)',
                    borderColor: '#007BFF',
                  },
                  {
                    label: 'Previous Month',
                    data: [80, 85, 75, 90, 85],
                    backgroundColor: 'rgba(108, 117, 125, 0.2)',
                    borderColor: '#6C757D',
                  },
                ]}
              />
            </div>
          </DashboardCard>
        </div>
      </div>

      {/* Team Members */}
      <DashboardCard title="Team Members">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Position
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Clock In
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-secondary-dark uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {teamMembers.map((member) => (
                <tr key={member.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-primary text-white flex items-center justify-center mr-3">
                        <span className="text-sm font-medium">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div className="text-sm font-medium text-secondary-dark">{member.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary">{member.position}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        member.status === 'Present'
                          ? 'bg-green-100 text-green-800'
                          : member.status === 'Late'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}
                    >
                      {member.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-secondary">{member.clockIn}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link href={`/dashboard/manager/team/${member.id}`} className="text-primary hover:text-primary-dark">
                      View
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </DashboardCard>

      {/* Additional Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DashboardCard title="Pending Requests">
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {pendingRequests.map((request) => (
              <div key={request.id} className="border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-secondary-dark">
                    {request.type}
                  </span>
                  <span className="px-2 text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </div>
                <p className="text-xs text-secondary mt-1">
                  <span className="font-medium">{request.employee}</span> • {request.date}
                </p>
                <p className="text-xs text-secondary mt-1">
                  Reason: {request.reason}
                </p>
                <div className="mt-2 flex space-x-2">
                  <button className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded hover:bg-green-200">
                    Approve
                  </button>
                  <button className="text-xs px-2 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200">
                    Reject
                  </button>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Link href="/dashboard/manager/requests" className="text-sm text-primary hover:text-primary-dark">
              View all requests
            </Link>
          </div>
        </DashboardCard>

        <DashboardCard title="Upcoming Shifts">
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {[1, 2, 3, 4].map((_, index) => (
              <div key={index} className="border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-secondary-dark">
                    {['Morning Shift', 'Afternoon Shift', 'Evening Shift', 'Night Shift'][index % 4]}
                  </span>
                  <span className="text-xs text-secondary-light">
                    {index === 0 ? 'Today' : index === 1 ? 'Tomorrow' : `In ${index + 1} days`}
                  </span>
                </div>
                <p className="text-xs text-secondary mt-1">
                  Time: {['8:00 AM - 4:00 PM', '4:00 PM - 12:00 AM', '12:00 PM - 8:00 PM', '12:00 AM - 8:00 AM'][index % 4]}
                </p>
                <p className="text-xs text-secondary mt-1">
                  Team members: {index === 0 ? 'All' : index === 1 ? 'Group A' : index === 2 ? 'Group B' : 'Group C'}
                </p>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center">
            <Link href="/dashboard/manager/shifts" className="text-sm text-primary hover:text-primary-dark">
              View all shifts
            </Link>
          </div>
        </DashboardCard>
      </div>
    </div>
  );
};

export default ManagerDashboard;
