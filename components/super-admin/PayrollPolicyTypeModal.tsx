'use client';

import React, { useState, useEffect } from 'react';
import { 
  PayrollPolicyType, 
  CreatePayrollPolicyTypeRequest, 
  UpdatePayrollPolicyTypeRequest,
  CalculationMethod,
  AppliesTo
} from '@/types/payroll';
import { createPayrollPolicyType, updatePayrollPolicyType } from '@/lib/payroll';

interface PayrollPolicyTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  policyType?: PayrollPolicyType;
  isEditing?: boolean;
}

const PayrollPolicyTypeModal: React.FC<PayrollPolicyTypeModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  policyType,
  isEditing = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    calculation_method: 'PERCENTAGE' as CalculationMethod,
    description: '',
    is_mandatory: false,
    is_active: true,
    applies_to: 'ALL_EMPLOYEES' as AppliesTo
  });

  // Reset form when modal opens/closes or policy type changes
  useEffect(() => {
    if (isOpen) {
      if (isEditing && policyType) {
        setFormData({
          name: policyType.name,
          code: policyType.code,
          calculation_method: policyType.calculation_method,
          description: policyType.description,
          is_mandatory: policyType.is_mandatory,
          is_active: policyType.is_active,
          applies_to: policyType.applies_to
        });
      } else {
        // Reset to default values for new policy type
        setFormData({
          name: '',
          code: '',
          calculation_method: 'PERCENTAGE',
          description: '',
          is_mandatory: false,
          is_active: true,
          applies_to: 'ALL_EMPLOYEES'
        });
      }
      setError('');
      setSuccessMessage('');
    }
  }, [isOpen, isEditing, policyType]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    // Basic validation
    if (!formData.name.trim()) {
      setError('Name is required');
      setIsLoading(false);
      return;
    }

    if (!formData.code.trim()) {
      setError('Code is required');
      setIsLoading(false);
      return;
    }

    if (!formData.description.trim()) {
      setError('Description is required');
      setIsLoading(false);
      return;
    }

    try {
      if (isEditing && policyType) {
        // Update existing policy type
        const updateData: UpdatePayrollPolicyTypeRequest = {
          name: formData.name.trim(),
          description: formData.description.trim(),
          is_active: formData.is_active,
          calculation_method: formData.calculation_method,
          is_mandatory: formData.is_mandatory,
          applies_to: formData.applies_to
        };

        await updatePayrollPolicyType(policyType.policy_type_id, updateData);
        setSuccessMessage('Policy type updated successfully!');
      } else {
        // Create new policy type
        const createData: CreatePayrollPolicyTypeRequest = {
          name: formData.name.trim(),
          code: formData.code.trim().toUpperCase(),
          calculation_method: formData.calculation_method,
          description: formData.description.trim(),
          is_mandatory: formData.is_mandatory,
          applies_to: formData.applies_to
        };

        await createPayrollPolicyType(createData);
        setSuccessMessage('Policy type created successfully!');
      }

      // Call the onSuccess callback after a delay
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 1500);
    } catch (error: any) {
      console.error('Error with policy type operation:', error);
      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} policy type. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  {isEditing ? 'Edit Policy Type' : 'Create New Policy Type'}
                </h3>

                {error && (
                  <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {successMessage && (
                  <div className="mt-2 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
                    {successMessage}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="mt-4 space-y-4">
                  {/* Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Name *
                    </label>
                    <input
                      id="name"
                      name="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter policy type name"
                    />
                  </div>

                  {/* Code */}
                  <div>
                    <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-1">
                      Code *
                    </label>
                    <input
                      id="code"
                      name="code"
                      type="text"
                      required
                      value={formData.code}
                      onChange={handleChange}
                      disabled={isEditing}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                      placeholder="Enter policy type code (e.g., INCOME_TAX)"
                    />
                    {isEditing && (
                      <p className="text-xs text-gray-500 mt-1">Code cannot be changed after creation</p>
                    )}
                  </div>

                  {/* Description */}
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      Description *
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      required
                      rows={3}
                      value={formData.description}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter policy type description"
                    />
                  </div>

                  {/* Calculation Method */}
                  <div>
                    <label htmlFor="calculation_method" className="block text-sm font-medium text-gray-700 mb-1">
                      Calculation Method
                    </label>
                    <select
                      id="calculation_method"
                      name="calculation_method"
                      value={formData.calculation_method}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="PROGRESSIVE_TAX">Progressive Tax</option>
                      <option value="FLAT_RATE">Flat Rate</option>
                      <option value="PERCENTAGE">Percentage</option>
                      <option value="FIXED_AMOUNT">Fixed Amount</option>
                      <option value="TIERED">Tiered</option>
                      <option value="CUSTOM">Custom</option>
                    </select>
                  </div>

                  {/* Applies To */}
                  <div>
                    <label htmlFor="applies_to" className="block text-sm font-medium text-gray-700 mb-1">
                      Applies To
                    </label>
                    <select
                      id="applies_to"
                      name="applies_to"
                      value={formData.applies_to}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="ALL_EMPLOYEES">All Employees</option>
                      <option value="SPECIFIC_ROLES">Specific Roles</option>
                      <option value="SPECIFIC_DEPARTMENTS">Specific Departments</option>
                      <option value="CUSTOM">Custom</option>
                    </select>
                  </div>

                  {/* Checkboxes */}
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        id="is_mandatory"
                        name="is_mandatory"
                        type="checkbox"
                        checked={formData.is_mandatory}
                        onChange={handleChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is_mandatory" className="ml-2 block text-sm text-gray-900">
                        Mandatory policy type
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        id="is_active"
                        name="is_active"
                        type="checkbox"
                        checked={formData.is_active}
                        onChange={handleChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                        Active policy type
                      </label>
                    </div>
                  </div>

                  <div className="pt-4 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                      disabled={isLoading}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center"
                    >
                      {isLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {isEditing ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        isEditing ? 'Update Policy Type' : 'Create Policy Type'
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PayrollPolicyTypeModal;
