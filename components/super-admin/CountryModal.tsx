'use client';

import React, { useState, useEffect } from 'react';
import { Country, CreateCountryRequest, UpdateCountryRequest } from '@/types/payroll';
import { createCountry, updateCountry } from '@/lib/payroll';

interface CountryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  country?: Country;
  isEditing?: boolean;
}

const CountryModal: React.FC<CountryModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  country,
  isEditing = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    currency: '',
    time_zone: '',
    date_format: ''
  });

  // Common time zones
  const timeZones = [
    'Africa/Abidjan',
    'Africa/Accra',
    'Africa/Addis_Ababa',
    'Africa/Algiers',
    'Africa/Cairo',
    'Africa/Casablanca',
    'Africa/Dar_es_Salaam',
    'Africa/Johannesburg',
    'Africa/Kampala',
    'Africa/Kigali',
    'Africa/Lagos',
    'Africa/Nairobi',
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Asia/Dubai'
  ];

  // Common date formats
  const dateFormats = [
    'DD/MM/YYYY',
    'MM/DD/YYYY',
    'YYYY-MM-DD',
    'DD-MM-YYYY',
    'MM-DD-YYYY'
  ];

  // Reset form when modal opens/closes or country changes
  useEffect(() => {
    if (isOpen) {
      if (isEditing && country) {
        setFormData({
          name: country.name,
          code: country.code,
          currency: country.currency,
          time_zone: country.time_zone,
          date_format: country.date_format || ''
        });
      } else {
        // Reset to default values for new country
        setFormData({
          name: '',
          code: '',
          currency: '',
          time_zone: '',
          date_format: ''
        });
      }
      setError('');
      setSuccessMessage('');
    }
  }, [isOpen, isEditing, country]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    // Basic validation
    if (!formData.name.trim()) {
      setError('Country name is required');
      setIsLoading(false);
      return;
    }

    if (!formData.code.trim()) {
      setError('Country code is required');
      setIsLoading(false);
      return;
    }

    if (!formData.currency.trim()) {
      setError('Currency is required');
      setIsLoading(false);
      return;
    }

    if (!formData.time_zone.trim()) {
      setError('Time zone is required');
      setIsLoading(false);
      return;
    }

    try {
      if (isEditing && country) {
        // Update existing country
        const updateData: UpdateCountryRequest = {
          name: formData.name.trim(),
          code: formData.code.trim().toUpperCase(),
          currency: formData.currency.trim().toUpperCase(),
          time_zone: formData.time_zone.trim(),
          date_format: formData.date_format.trim() || undefined
        };

        await updateCountry(country.country_id, updateData);
        setSuccessMessage('Country updated successfully!');
      } else {
        // Create new country
        const createData: CreateCountryRequest = {
          name: formData.name.trim(),
          code: formData.code.trim().toUpperCase(),
          currency: formData.currency.trim().toUpperCase(),
          time_zone: formData.time_zone.trim(),
          date_format: formData.date_format.trim() || undefined
        };

        await createCountry(createData);
        setSuccessMessage('Country created successfully!');
      }

      // Call the onSuccess callback after a delay
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 1500);
    } catch (error: any) {
      console.error('Error with country operation:', error);
      setError(error.message || `Failed to ${isEditing ? 'update' : 'create'} country. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              onClick={onClose}
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  {isEditing ? 'Edit Country' : 'Add New Country'}
                </h3>

                {error && (
                  <div className="mt-2 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {successMessage && (
                  <div className="mt-2 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
                    {successMessage}
                  </div>
                )}

                <form onSubmit={handleSubmit} className="mt-4 space-y-4">
                  {/* Name */}
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Country Name *
                    </label>
                    <input
                      id="name"
                      name="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter country name"
                    />
                  </div>

                  {/* Code */}
                  <div>
                    <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-1">
                      Country Code *
                    </label>
                    <input
                      id="code"
                      name="code"
                      type="text"
                      required
                      maxLength={3}
                      value={formData.code}
                      onChange={handleChange}
                      disabled={isEditing}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                      placeholder="Enter country code (e.g., US, RW)"
                    />
                    {isEditing && (
                      <p className="text-xs text-gray-500 mt-1">Country code cannot be changed after creation</p>
                    )}
                  </div>

                  {/* Currency */}
                  <div>
                    <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                      Currency *
                    </label>
                    <input
                      id="currency"
                      name="currency"
                      type="text"
                      required
                      maxLength={3}
                      value={formData.currency}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter currency code (e.g., USD, RWF)"
                    />
                  </div>

                  {/* Time Zone */}
                  <div>
                    <label htmlFor="time_zone" className="block text-sm font-medium text-gray-700 mb-1">
                      Time Zone *
                    </label>
                    <select
                      id="time_zone"
                      name="time_zone"
                      required
                      value={formData.time_zone}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select time zone</option>
                      {timeZones.map((tz) => (
                        <option key={tz} value={tz}>
                          {tz}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Date Format */}
                  <div>
                    <label htmlFor="date_format" className="block text-sm font-medium text-gray-700 mb-1">
                      Date Format (Optional)
                    </label>
                    <select
                      id="date_format"
                      name="date_format"
                      value={formData.date_format}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select date format</option>
                      {dateFormats.map((format) => (
                        <option key={format} value={format}>
                          {format}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="pt-4 flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                      disabled={isLoading}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center"
                    >
                      {isLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          {isEditing ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        isEditing ? 'Update Country' : 'Add Country'
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountryModal;
