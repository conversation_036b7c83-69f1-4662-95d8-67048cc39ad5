'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Country } from '@/types/payroll';
import { getCountries, deleteCountry } from '@/lib/payroll';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import CountryModal from '@/components/super-admin/CountryModal';

const CountriesContent = () => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<Country | undefined>();
  
  // Delete confirmation state
  const [countryToDelete, setCountryToDelete] = useState<Country | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);

  // Fetch countries
  const fetchCountries = async () => {
    try {
      setIsLoading(true);
      setError('');

      const response = await getCountries();
      setCountries(response.countries);
    } catch (error: any) {
      console.error('Error fetching countries:', error);
      setError(error.message || 'Failed to fetch countries');
      setCountries([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch countries when component mounts
  useEffect(() => {
    fetchCountries();
  }, []);

  // Modal handlers
  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  const openEditModal = (country: Country) => {
    setSelectedCountry(country);
    setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
    setSelectedCountry(undefined);
    setIsEditModalOpen(false);
  };

  // Delete handlers
  const openDeleteConfirm = (country: Country) => {
    setCountryToDelete(country);
    setIsDeleteConfirmOpen(true);
  };
  const closeDeleteConfirm = () => {
    setCountryToDelete(null);
    setIsDeleteConfirmOpen(false);
  };

  const handleDeleteCountry = async () => {
    if (!countryToDelete) return;

    try {
      setIsLoading(true);
      
      await deleteCountry(countryToDelete.country_id);
      
      // Remove the deleted country from the state
      setCountries(countries.filter(c => c.country_id !== countryToDelete.country_id));
      closeDeleteConfirm();
    } catch (error: any) {
      console.error('Error deleting country:', error);
      setError(error.message || 'Failed to delete country');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate stats
  const totalCountries = countries.length;
  const readyCountries = countries.filter(c => c.payroll_status?.is_ready).length;
  const inProgressCountries = countries.filter(c => 
    c.payroll_status && c.payroll_status.setup_completion > 0 && !c.payroll_status.is_ready
  ).length;
  const notSetupCountries = countries.filter(c => 
    !c.payroll_status || c.payroll_status.setup_completion === 0
  ).length;

  const stats = [
    { title: 'Total Countries', value: totalCountries.toString(), change: '', changeType: 'neutral' as const },
    { title: 'Ready', value: readyCountries.toString(), change: '', changeType: 'positive' as const },
    { title: 'In Progress', value: inProgressCountries.toString(), change: '', changeType: 'neutral' as const },
    { title: 'Not Setup', value: notSetupCountries.toString(), change: '', changeType: 'negative' as const },
  ];

  return (
    <div className="space-y-6">
      {/* Create Country Modal */}
      <CountryModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        onSuccess={() => {
          closeCreateModal();
          fetchCountries();
        }}
      />

      {/* Edit Country Modal */}
      <CountryModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSuccess={() => {
          closeEditModal();
          fetchCountries();
        }}
        country={selectedCountry}
        isEditing={true}
      />

      {/* Delete Confirmation Dialog */}
      {isDeleteConfirmOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" onClick={closeDeleteConfirm}>
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Country</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete "{countryToDelete?.name}"? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDeleteCountry}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deleting...' : 'Delete'}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeDeleteConfirm}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Country Management</h1>
        <div className="flex items-center space-x-3">
          <Link
            href="/dashboard/super-admin/countries/payroll-policies"
            className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 text-sm font-medium rounded-md transition-colors flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Payroll Policies
          </Link>
          <button
            onClick={openCreateModal}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-colors flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Country
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-gray-600">
        <Link href="/dashboard/super-admin" className="hover:text-blue-600">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-gray-900">Countries</span>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            loading={isLoading}
          />
        ))}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Countries List */}
      <DashboardCard title="Countries" loading={isLoading}>
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Loading countries...</p>
          </div>
        ) : countries.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-gray-600">No countries found.</p>
            <button
              onClick={openCreateModal}
              className="mt-2 text-blue-600 hover:text-blue-800"
            >
              Add your first country
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {countries.map((country) => (
              <div key={country.country_id} className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 transition-colors">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{country.name}</h3>
                    <p className="text-sm text-gray-500">{country.code} • {country.currency}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Link
                      href={`/dashboard/super-admin/countries/${country.code.toLowerCase()}`}
                      className="bg-blue-600 hover:bg-blue-700 text-white hover:text-white py-1 px-3 text-xs font-medium rounded transition-colors"
                    >
                      Manage
                    </Link>
                    <button
                      onClick={() => openEditModal(country)}
                      className="text-blue-600 hover:text-blue-900 text-sm"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => openDeleteConfirm(country)}
                      className="text-red-600 hover:text-red-900 text-sm"
                    >
                      Delete
                    </button>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Time Zone:</span>
                    <span className="text-gray-900">{country.time_zone}</span>
                  </div>
                  {country.date_format && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Date Format:</span>
                      <span className="text-gray-900">{country.date_format}</span>
                    </div>
                  )}
                </div>

                {/* Payroll Status */}
                {country.payroll_status && (
                  <div className="border-t border-gray-100 pt-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Payroll Setup</span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        country.payroll_status.is_ready 
                          ? 'bg-green-100 text-green-800' 
                          : country.payroll_status.setup_completion > 0
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {country.payroll_status.is_ready 
                          ? 'Ready' 
                          : country.payroll_status.setup_completion > 0
                          ? 'In Progress'
                          : 'Not Setup'
                        }
                      </span>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${country.payroll_status.setup_completion}%` }}
                      ></div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-3">
                      <div>Policies: {country.payroll_status.policies_count}</div>
                      <div>Deductions: {country.payroll_status.deduction_types_count}</div>
                      <div>Employee Types: {country.payroll_status.employee_types_count}</div>
                      <div>Progress: {country.payroll_status.setup_completion}%</div>
                    </div>

                    <Link
                      href={`/dashboard/super-admin/countries/${country.code.toLowerCase()}`}
                      className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 font-medium"
                    >
                      Configure Payroll
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default CountriesContent;
