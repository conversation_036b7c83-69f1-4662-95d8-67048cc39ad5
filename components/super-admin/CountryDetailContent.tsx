'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Country } from '@/types/payroll';
import { getCountries } from '@/lib/payroll';
import DashboardCard from '@/components/ui/DashboardCard';
import TaxPoliciesTab from '@/components/super-admin/country-tabs/TaxPoliciesTab';
import EmployeeTypesTab from '@/components/super-admin/country-tabs/EmployeeTypesTab';
import DeductionsTab from '@/components/super-admin/country-tabs/DeductionsTab';

interface CountryDetailContentProps {
  countryCode: string;
}

type TabType = 'tax-policies' | 'employee-types' | 'deductions';

const CountryDetailContent: React.FC<CountryDetailContentProps> = ({ countryCode }) => {
  const [country, setCountry] = useState<Country | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>('tax-policies');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch country data
  const fetchCountry = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const response = await getCountries();
      const foundCountry = response.countries.find(c => 
        c.code.toLowerCase() === countryCode.toLowerCase()
      );
      
      if (!foundCountry) {
        throw new Error('Country not found');
      }
      
      setCountry(foundCountry);
    } catch (error: any) {
      console.error('Error fetching country:', error);
      setError(error.message || 'Failed to load country data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCountry();
  }, [countryCode]);

  const tabs = [
    {
      id: 'tax-policies' as TabType,
      name: 'Tax Policies',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
    },
    {
      id: 'employee-types' as TabType,
      name: 'Employee Types',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
    },
    {
      id: 'deductions' as TabType,
      name: 'Deductions',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error || !country) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Country Not Found</h1>
            <p className="text-gray-600">The requested country could not be found.</p>
          </div>
          <Link
            href="/dashboard/super-admin/countries"
            className="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 text-sm font-medium rounded-md transition-colors"
          >
            Back to Countries
          </Link>
        </div>
        {error && (
          <DashboardCard title="Error">
            <div className="text-red-600">{error}</div>
          </DashboardCard>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-3">
            <Link
              href="/dashboard/super-admin/countries"
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">
              {country.name} ({country.code})
            </h1>
          </div>
          <p className="text-gray-600 mt-1">
            Manage payroll policies, tax settings, and employee types for {country.name}
          </p>
        </div>
        
        {/* Country Status */}
        <div className="text-right">
          <div className="text-sm text-gray-500">Setup Progress</div>
          <div className="flex items-center space-x-2 mt-1">
            <div className="w-24 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${country.payroll_status?.setup_completion || 0}%` }}
              ></div>
            </div>
            <span className="text-sm font-medium text-gray-900">
              {country.payroll_status?.setup_completion || 0}%
            </span>
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'tax-policies' && (
          <TaxPoliciesTab countryCode={countryCode} country={country} />
        )}
        {activeTab === 'employee-types' && (
          <EmployeeTypesTab countryCode={countryCode} country={country} />
        )}
        {activeTab === 'deductions' && (
          <DeductionsTab countryCode={countryCode} country={country} />
        )}
      </div>
    </div>
  );
};

export default CountryDetailContent;
