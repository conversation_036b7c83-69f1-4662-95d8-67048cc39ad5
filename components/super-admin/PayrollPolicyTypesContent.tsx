'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { PayrollPolicyType } from '@/types/payroll';
import { 
  getPayrollPolicyTypes, 
  deletePayrollPolicyType,
  getPolicyTypeCountries
} from '@/lib/payroll';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import PayrollPolicyTypeModal from '@/components/super-admin/PayrollPolicyTypeModal';

const PayrollPolicyTypesContent = () => {
  const [policyTypes, setPolicyTypes] = useState<PayrollPolicyType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedPolicyType, setSelectedPolicyType] = useState<PayrollPolicyType | undefined>();
  
  // Delete confirmation state
  const [policyTypeToDelete, setPolicyTypeToDelete] = useState<PayrollPolicyType | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);

  // Fetch policy types
  const fetchPolicyTypes = async () => {
    try {
      setIsLoading(true);
      setError('');

      const response = await getPayrollPolicyTypes();
      setPolicyTypes(response.policyTypes);
    } catch (error: any) {
      console.error('Error fetching policy types:', error);
      setError(error.message || 'Failed to fetch policy types');
      setPolicyTypes([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch policy types when component mounts
  useEffect(() => {
    fetchPolicyTypes();
  }, []);

  // Modal handlers
  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  const openEditModal = (policyType: PayrollPolicyType) => {
    setSelectedPolicyType(policyType);
    setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
    setSelectedPolicyType(undefined);
    setIsEditModalOpen(false);
  };

  // Delete handlers
  const openDeleteConfirm = (policyType: PayrollPolicyType) => {
    setPolicyTypeToDelete(policyType);
    setIsDeleteConfirmOpen(true);
  };
  const closeDeleteConfirm = () => {
    setPolicyTypeToDelete(null);
    setIsDeleteConfirmOpen(false);
  };

  const handleDeletePolicyType = async () => {
    if (!policyTypeToDelete) return;

    try {
      setIsLoading(true);
      
      await deletePayrollPolicyType(policyTypeToDelete.policy_type_id);
      
      // Remove the deleted policy type from the state
      setPolicyTypes(policyTypes.filter(pt => pt.policy_type_id !== policyTypeToDelete.policy_type_id));
      closeDeleteConfirm();
    } catch (error: any) {
      console.error('Error deleting policy type:', error);
      setError(error.message || 'Failed to delete policy type');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate stats
  const totalPolicyTypes = policyTypes.length;
  const activePolicyTypes = policyTypes.filter(pt => pt.is_active).length;
  const mandatoryPolicyTypes = policyTypes.filter(pt => pt.is_mandatory).length;
  const inUsePolicyTypes = policyTypes.filter(pt => pt.usage_stats?.is_in_use).length;

  const stats = [
    { title: 'Total Policy Types', value: totalPolicyTypes.toString(), change: '', changeType: 'neutral' as const },
    { title: 'Active', value: activePolicyTypes.toString(), change: '', changeType: 'positive' as const },
    { title: 'Mandatory', value: mandatoryPolicyTypes.toString(), change: '', changeType: 'neutral' as const },
    { title: 'In Use', value: inUsePolicyTypes.toString(), change: '', changeType: 'positive' as const },
  ];

  return (
    <div className="space-y-6">
      {/* Create Policy Type Modal */}
      <PayrollPolicyTypeModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        onSuccess={() => {
          closeCreateModal();
          fetchPolicyTypes();
        }}
      />

      {/* Edit Policy Type Modal */}
      <PayrollPolicyTypeModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSuccess={() => {
          closeEditModal();
          fetchPolicyTypes();
        }}
        policyType={selectedPolicyType}
        isEditing={true}
      />

      {/* Delete Confirmation Dialog */}
      {isDeleteConfirmOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" onClick={closeDeleteConfirm}>
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Policy Type</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete "{policyTypeToDelete?.name}"? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDeletePolicyType}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deleting...' : 'Delete'}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeDeleteConfirm}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Global Payroll Policy Types</h1>
        <button
          onClick={openCreateModal}
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 text-sm font-medium rounded-md transition-colors flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Create Policy Type
        </button>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-gray-600">
        <Link href="/dashboard/super-admin" className="hover:text-blue-600">Dashboard</Link>
        <span className="mx-2">/</span>
        <Link href="/dashboard/super-admin/countries" className="hover:text-blue-600">Countries</Link>
        <span className="mx-2">/</span>
        <span className="text-gray-900">Payroll Policy Types</span>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            loading={isLoading}
          />
        ))}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Policy Types List */}
      <DashboardCard title="Policy Types" loading={isLoading}>
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Loading policy types...</p>
          </div>
        ) : policyTypes.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-gray-600">No policy types found.</p>
            <button
              onClick={openCreateModal}
              className="mt-2 text-blue-600 hover:text-blue-800"
            >
              Create your first policy type
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Usage
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {policyTypes.map((policyType) => (
                  <tr key={policyType.policy_type_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{policyType.name}</div>
                        <div className="text-sm text-gray-500">{policyType.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {policyType.code}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policyType.calculation_method.replace('_', ' ')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          policyType.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {policyType.is_active ? 'Active' : 'Inactive'}
                        </span>
                        {policyType.is_mandatory && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                            Mandatory
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {policyType.usage_stats?.countries_using || 0} countries
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => openEditModal(policyType)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => openDeleteConfirm(policyType)}
                          className="text-red-600 hover:text-red-900"
                          disabled={policyType.usage_stats?.is_in_use}
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default PayrollPolicyTypesContent;
