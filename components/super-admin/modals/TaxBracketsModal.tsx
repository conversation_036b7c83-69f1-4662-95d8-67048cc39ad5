'use client';

import React, { useState } from 'react';
import { TaxPolicy, CreateTaxBracketRequest } from '@/types/payroll';
import { createTaxBrackets } from '@/lib/payroll';

interface TaxBracketsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  policy: TaxPolicy;
}

const TaxBracketsModal: React.FC<TaxBracketsModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  policy
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [brackets, setBrackets] = useState<CreateTaxBracketRequest[]>([
    {
      bracket_order: 1,
      min_amount: 0,
      max_amount: 30000,
      tax_rate: 0,
      description: 'Tax-free bracket'
    }
  ]);

  const addBracket = () => {
    const lastBracket = brackets[brackets.length - 1];
    const newBracket: CreateTaxBracketRequest = {
      bracket_order: brackets.length + 1,
      min_amount: lastBracket.max_amount + 1,
      max_amount: lastBracket.max_amount + 50000,
      tax_rate: 0.1,
      description: `Tax bracket ${brackets.length + 1}`
    };
    setBrackets([...brackets, newBracket]);
  };

  const removeBracket = (index: number) => {
    if (brackets.length > 1) {
      const newBrackets = brackets.filter((_, i) => i !== index);
      // Reorder bracket_order
      const reorderedBrackets = newBrackets.map((bracket, i) => ({
        ...bracket,
        bracket_order: i + 1
      }));
      setBrackets(reorderedBrackets);
    }
  };

  const updateBracket = (index: number, field: keyof CreateTaxBracketRequest, value: any) => {
    const newBrackets = [...brackets];
    newBrackets[index] = {
      ...newBrackets[index],
      [field]: value
    };
    setBrackets(newBrackets);
  };

  const validateBrackets = (): boolean => {
    for (let i = 0; i < brackets.length; i++) {
      const bracket = brackets[i];
      
      if (bracket.min_amount < 0) {
        setError(`Bracket ${i + 1}: Minimum amount cannot be negative`);
        return false;
      }
      
      if (bracket.max_amount <= bracket.min_amount) {
        setError(`Bracket ${i + 1}: Maximum amount must be greater than minimum amount`);
        return false;
      }
      
      if (bracket.tax_rate < 0 || bracket.tax_rate > 1) {
        setError(`Bracket ${i + 1}: Tax rate must be between 0 and 1 (0% to 100%)`);
        return false;
      }
      
      if (!bracket.description.trim()) {
        setError(`Bracket ${i + 1}: Description is required`);
        return false;
      }
      
      // Check for overlapping brackets
      if (i > 0 && bracket.min_amount <= brackets[i - 1].max_amount) {
        setError(`Bracket ${i + 1}: Overlaps with previous bracket`);
        return false;
      }
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateBrackets()) {
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      await createTaxBrackets(policy.policy_id, { tax_brackets: brackets });
      setSuccessMessage('Tax brackets created successfully!');
      
      setTimeout(() => {
        onSuccess();
      }, 1000);
    } catch (error: any) {
      console.error('Error creating tax brackets:', error);
      setError(error.message || 'Failed to create tax brackets');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setBrackets([
        {
          bracket_order: 1,
          min_amount: 0,
          max_amount: 30000,
          tax_rate: 0,
          description: 'Tax-free bracket'
        }
      ]);
      setError('');
      setSuccessMessage('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Setup Tax Brackets</h2>
              <p className="text-sm text-gray-600 mt-1">
                {policy.policy_type_name} for {policy.employee_type_name}
              </p>
            </div>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              {successMessage}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Brackets List */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Tax Brackets</h3>
                <button
                  type="button"
                  onClick={addBracket}
                  className="bg-green-600 hover:bg-green-700 text-white py-1 px-3 text-sm font-medium rounded transition-colors"
                >
                  Add Bracket
                </button>
              </div>

              <div className="space-y-4">
                {brackets.map((bracket, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium text-gray-900">Bracket {index + 1}</h4>
                      {brackets.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeBracket(index)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Remove
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Min Amount
                        </label>
                        <input
                          type="number"
                          value={bracket.min_amount}
                          onChange={(e) => updateBracket(index, 'min_amount', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="0"
                          step="0.01"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Max Amount
                        </label>
                        <input
                          type="number"
                          value={bracket.max_amount}
                          onChange={(e) => updateBracket(index, 'max_amount', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="0"
                          step="0.01"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Tax Rate (0-1)
                        </label>
                        <input
                          type="number"
                          value={bracket.tax_rate}
                          onChange={(e) => updateBracket(index, 'tax_rate', parseFloat(e.target.value) || 0)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          min="0"
                          max="1"
                          step="0.01"
                          required
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          {(bracket.tax_rate * 100).toFixed(1)}%
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <input
                          type="text"
                          value={bracket.description}
                          onChange={(e) => updateBracket(index, 'description', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Bracket description"
                          required
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 flex items-center"
              >
                {isLoading && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {isLoading ? 'Creating...' : 'Create Brackets'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default TaxBracketsModal;
