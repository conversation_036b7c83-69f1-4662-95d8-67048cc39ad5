'use client';

import React, { useState } from 'react';
import { DeductionType, CreateDeductionPolicyRequest } from '@/types/payroll';
import { createDeductionPolicy } from '@/lib/payroll';

interface DeductionPolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  countryCode: string;
  deductionTypes: DeductionType[];
}

const DeductionPolicyModal: React.FC<DeductionPolicyModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  countryCode,
  deductionTypes
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const [formData, setFormData] = useState<CreateDeductionPolicyRequest>({
    deduction_type_code: '',
    employee_rate: 0,
    employer_rate: 0,
    effective_from: new Date().toISOString().split('T')[0],
    change_reason: ''
  });

  const [selectedDeductionType, setSelectedDeductionType] = useState<DeductionType | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name === 'deduction_type_code') {
      const deductionType = deductionTypes.find(dt => dt.code === value);
      setSelectedDeductionType(deductionType || null);
      
      // Reset rates when changing deduction type
      setFormData(prev => ({
        ...prev,
        [name]: value,
        employee_rate: 0,
        employer_rate: 0
      }));
    } else if (name === 'employee_rate' || name === 'employer_rate') {
      // Convert percentage to decimal (e.g., 6% -> 0.06)
      const rate = parseFloat(value) / 100;
      setFormData(prev => ({
        ...prev,
        [name]: isNaN(rate) ? 0 : rate
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const validateForm = (): boolean => {
    if (!formData.deduction_type_code) {
      setError('Deduction type is required');
      return false;
    }
    if (!formData.effective_from) {
      setError('Effective from date is required');
      return false;
    }
    if (!formData.change_reason.trim()) {
      setError('Change reason is required');
      return false;
    }

    // Validate rates based on deduction type
    if (selectedDeductionType) {
      if (selectedDeductionType.has_employee_contribution && formData.employee_rate <= 0) {
        setError('Employee rate must be greater than 0 for this deduction type');
        return false;
      }
      if (selectedDeductionType.has_employer_contribution && formData.employer_rate <= 0) {
        setError('Employer rate must be greater than 0 for this deduction type');
        return false;
      }
    }

    if (formData.employee_rate < 0 || formData.employee_rate > 1) {
      setError('Employee rate must be between 0% and 100%');
      return false;
    }
    if (formData.employer_rate < 0 || formData.employer_rate > 1) {
      setError('Employer rate must be between 0% and 100%');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      await createDeductionPolicy(countryCode, formData);
      setSuccessMessage('Deduction policy created successfully!');
      
      // Reset form
      setFormData({
        deduction_type_code: '',
        employee_rate: 0,
        employer_rate: 0,
        effective_from: new Date().toISOString().split('T')[0],
        change_reason: ''
      });
      setSelectedDeductionType(null);
      
      setTimeout(() => {
        onSuccess();
      }, 1000);
    } catch (error: any) {
      console.error('Error creating deduction policy:', error);
      setError(error.message || 'Failed to create deduction policy');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        deduction_type_code: '',
        employee_rate: 0,
        employer_rate: 0,
        effective_from: new Date().toISOString().split('T')[0],
        change_reason: ''
      });
      setSelectedDeductionType(null);
      setError('');
      setSuccessMessage('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Create Deduction Policy</h2>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              {successMessage}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Deduction Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Deduction Type *
              </label>
              <select
                name="deduction_type_code"
                value={formData.deduction_type_code}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select Deduction Type</option>
                {deductionTypes.filter(dt => dt.is_active).map((type) => (
                  <option key={type.deduction_type_id} value={type.code}>
                    {type.name} ({type.code})
                  </option>
                ))}
              </select>
            </div>

            {/* Deduction Type Info */}
            {selectedDeductionType && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="font-medium text-blue-900 mb-2">Deduction Type Details</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <div>Description: {selectedDeductionType.description}</div>
                  <div>Calculation Base: {selectedDeductionType.calculation_base}</div>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        selectedDeductionType.has_employee_contribution ? 'bg-green-500' : 'bg-gray-300'
                      }`}></span>
                      Employee Contribution
                    </div>
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        selectedDeductionType.has_employer_contribution ? 'bg-green-500' : 'bg-gray-300'
                      }`}></span>
                      Employer Contribution
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Rates */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employee Rate (%) *
                </label>
                <input
                  type="number"
                  name="employee_rate"
                  value={(formData.employee_rate * 100).toString()}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="0"
                  max="100"
                  step="0.01"
                  placeholder="0.00"
                  disabled={selectedDeductionType ? !selectedDeductionType.has_employee_contribution : false}
                  required={selectedDeductionType?.has_employee_contribution || false}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Employer Rate (%) *
                </label>
                <input
                  type="number"
                  name="employer_rate"
                  value={(formData.employer_rate * 100).toString()}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="0"
                  max="100"
                  step="0.01"
                  placeholder="0.00"
                  disabled={selectedDeductionType ? !selectedDeductionType.has_employer_contribution : false}
                  required={selectedDeductionType?.has_employer_contribution || false}
                />
              </div>
            </div>

            {/* Effective From */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Effective From *
              </label>
              <input
                type="date"
                name="effective_from"
                value={formData.effective_from}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* Change Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Change Reason *
              </label>
              <textarea
                name="change_reason"
                value={formData.change_reason}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe the reason for this policy..."
                required
              />
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 flex items-center"
              >
                {isLoading && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {isLoading ? 'Creating...' : 'Create Policy'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DeductionPolicyModal;
