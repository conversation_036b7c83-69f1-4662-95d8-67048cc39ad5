'use client';

import React, { useState, useEffect } from 'react';
import { DeductionType, CreateDeductionTypeRequest } from '@/types/payroll';
import { createDeductionType, updateDeductionType } from '@/lib/payroll';

interface DeductionTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  countryCode: string;
  deductionType?: DeductionType;
  isEditing?: boolean;
}

const DeductionTypeModal: React.FC<DeductionTypeModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  countryCode,
  deductionType,
  isEditing = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const [formData, setFormData] = useState<CreateDeductionTypeRequest>({
    name: '',
    code: '',
    description: '',
    has_employee_contribution: true,
    has_employer_contribution: false,
    calculation_base: 'GROSS_SALARY',
    is_mandatory: false,
    is_active: true
  });

  const calculationBases = [
    { value: 'GROSS_SALARY', label: 'Gross Salary' },
    { value: 'BASIC_SALARY', label: 'Basic Salary' },
    { value: 'PENSIONABLE_SALARY', label: 'Pensionable Salary' },
    { value: 'TAXABLE_INCOME', label: 'Taxable Income' },
    { value: 'FIXED_AMOUNT', label: 'Fixed Amount' }
  ];

  // Populate form when editing
  useEffect(() => {
    if (isEditing && deductionType) {
      setFormData({
        name: deductionType.name,
        code: deductionType.code,
        description: deductionType.description,
        has_employee_contribution: deductionType.has_employee_contribution,
        has_employer_contribution: deductionType.has_employer_contribution,
        calculation_base: deductionType.calculation_base,
        is_mandatory: deductionType.is_mandatory,
        is_active: deductionType.is_active
      });
    } else {
      setFormData({
        name: '',
        code: '',
        description: '',
        has_employee_contribution: true,
        has_employer_contribution: false,
        calculation_base: 'GROSS_SALARY',
        is_mandatory: false,
        is_active: true
      });
    }
  }, [isEditing, deductionType, isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      setError('Name is required');
      return false;
    }
    if (!formData.code.trim()) {
      setError('Code is required');
      return false;
    }
    if (!formData.description.trim()) {
      setError('Description is required');
      return false;
    }
    
    // Validate code format (uppercase letters and underscores only)
    if (!/^[A-Z_]+$/.test(formData.code)) {
      setError('Code must contain only uppercase letters and underscores');
      return false;
    }

    // At least one contribution type must be selected
    if (!formData.has_employee_contribution && !formData.has_employer_contribution) {
      setError('At least one contribution type (employee or employer) must be selected');
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      if (isEditing && deductionType) {
        await updateDeductionType(countryCode, deductionType.deduction_type_id, formData);
        setSuccessMessage('Deduction type updated successfully!');
      } else {
        await createDeductionType(countryCode, formData);
        setSuccessMessage('Deduction type created successfully!');
      }
      
      setTimeout(() => {
        onSuccess();
      }, 1000);
    } catch (error: any) {
      console.error('Error saving deduction type:', error);
      setError(error.message || 'Failed to save deduction type');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        name: '',
        code: '',
        description: '',
        has_employee_contribution: true,
        has_employer_contribution: false,
        calculation_base: 'GROSS_SALARY',
        is_mandatory: false,
        is_active: true
      });
      setError('');
      setSuccessMessage('');
      onClose();
    }
  };

  const generateCodeFromName = (name: string) => {
    return name
      .toUpperCase()
      .replace(/[^A-Z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .substring(0, 20);
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    setFormData(prev => ({
      ...prev,
      name,
      // Auto-generate code only when creating new deduction type
      ...((!isEditing || !deductionType) && { code: generateCodeFromName(name) })
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditing ? 'Edit Deduction Type' : 'Create Deduction Type'}
            </h2>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              {successMessage}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleNameChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Pension Contribution"
                required
              />
            </div>

            {/* Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Code *
              </label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., PENSION"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Use uppercase letters and underscores only
              </p>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe this deduction type..."
                required
              />
            </div>

            {/* Calculation Base */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Calculation Base *
              </label>
              <select
                name="calculation_base"
                value={formData.calculation_base}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                {calculationBases.map((base) => (
                  <option key={base.value} value={base.value}>
                    {base.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Contribution Types */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700">
                Contribution Types *
              </label>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="has_employee_contribution"
                    checked={formData.has_employee_contribution}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Employee Contribution
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name="has_employer_contribution"
                    checked={formData.has_employer_contribution}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-700">
                    Employer Contribution
                  </label>
                </div>
              </div>
            </div>

            {/* Other Options */}
            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="is_mandatory"
                  checked={formData.is_mandatory}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-700">
                  Mandatory Deduction
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-700">
                  Active
                </label>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 flex items-center"
              >
                {isLoading && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {isLoading 
                  ? (isEditing ? 'Updating...' : 'Creating...') 
                  : (isEditing ? 'Update Deduction Type' : 'Create Deduction Type')
                }
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DeductionTypeModal;
