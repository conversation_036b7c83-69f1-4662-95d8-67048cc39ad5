'use client';

import React, { useState } from 'react';
import { EmployeeType, CreateTaxPolicyRequest } from '@/types/payroll';
import { createTaxPolicy } from '@/lib/payroll';

interface TaxPolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  countryCode: string;
  employeeTypes: EmployeeType[];
}

const TaxPolicyModal: React.FC<TaxPolicyModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  countryCode,
  employeeTypes
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const [formData, setFormData] = useState<CreateTaxPolicyRequest>({
    policy_type_code: 'INCOME_TAX',
    employee_type_id: '',
    effective_from: new Date().toISOString().split('T')[0],
    effective_to: null,
    is_active: true,
    change_reason: ''
  });

  const policyTypes = [
    { code: 'INCOME_TAX', name: 'Personal Income Tax' },
    { code: 'CORPORATE_TAX', name: 'Corporate Tax' },
    { code: 'VAT', name: 'Value Added Tax' },
    { code: 'WITHHOLDING_TAX', name: 'Withholding Tax' },
    { code: 'SOCIAL_SECURITY', name: 'Social Security' },
    { code: 'PENSION', name: 'Pension Contribution' }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value === '' && name === 'effective_to' ? null : value
      }));
    }
  };

  const validateForm = (): boolean => {
    if (!formData.policy_type_code) {
      setError('Policy type is required');
      return false;
    }
    if (!formData.employee_type_id) {
      setError('Employee type is required');
      return false;
    }
    if (!formData.effective_from) {
      setError('Effective from date is required');
      return false;
    }
    if (!formData.change_reason.trim()) {
      setError('Change reason is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      await createTaxPolicy(countryCode, formData);
      setSuccessMessage('Tax policy created successfully!');
      
      // Reset form
      setFormData({
        policy_type_code: 'INCOME_TAX',
        employee_type_id: '',
        effective_from: new Date().toISOString().split('T')[0],
        effective_to: null,
        is_active: true,
        change_reason: ''
      });
      
      setTimeout(() => {
        onSuccess();
      }, 1000);
    } catch (error: any) {
      console.error('Error creating tax policy:', error);
      setError(error.message || 'Failed to create tax policy');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        policy_type_code: 'INCOME_TAX',
        employee_type_id: '',
        effective_from: new Date().toISOString().split('T')[0],
        effective_to: null,
        is_active: true,
        change_reason: ''
      });
      setError('');
      setSuccessMessage('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Create Tax Policy</h2>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              {successMessage}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Policy Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Policy Type *
              </label>
              <select
                name="policy_type_code"
                value={formData.policy_type_code}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                {policyTypes.map((type) => (
                  <option key={type.code} value={type.code}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Employee Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Employee Type *
              </label>
              <select
                name="employee_type_id"
                value={formData.employee_type_id}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select Employee Type</option>
                {employeeTypes.map((type) => (
                  <option key={type.employee_type_id} value={type.employee_type_id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Effective From */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Effective From *
              </label>
              <input
                type="date"
                name="effective_from"
                value={formData.effective_from}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* Effective To */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Effective To (Optional)
              </label>
              <input
                type="date"
                name="effective_to"
                value={formData.effective_to || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Change Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Change Reason *
              </label>
              <textarea
                name="change_reason"
                value={formData.change_reason}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe the reason for this policy change..."
                required
              />
            </div>

            {/* Is Active */}
            <div className="flex items-center">
              <input
                type="checkbox"
                name="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Active Policy
              </label>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 flex items-center"
              >
                {isLoading && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {isLoading ? 'Creating...' : 'Create Policy'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default TaxPolicyModal;
