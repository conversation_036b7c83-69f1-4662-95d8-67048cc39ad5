'use client';

import React, { useState, useEffect } from 'react';
import { Country, DeductionType, DeductionPolicy } from '@/types/payroll';
import { getDeductionTypes, getDeductionPolicies, deleteDeductionType } from '@/lib/payroll';
import DashboardCard from '@/components/ui/DashboardCard';
import DeductionTypeModal from '@/components/super-admin/modals/DeductionTypeModal';
import DeductionPolicyModal from '@/components/super-admin/modals/DeductionPolicyModal';

interface DeductionsTabProps {
  countryCode: string;
  country: Country;
}

type SubTabType = 'types' | 'policies';

const DeductionsTab: React.FC<DeductionsTabProps> = ({ countryCode, country }) => {
  const [activeSubTab, setActiveSubTab] = useState<SubTabType>('types');
  const [deductionTypes, setDeductionTypes] = useState<DeductionType[]>([]);
  const [deductionPolicies, setDeductionPolicies] = useState<DeductionPolicy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isTypeModalOpen, setIsTypeModalOpen] = useState(false);
  const [isPolicyModalOpen, setIsPolicyModalOpen] = useState(false);
  const [selectedDeductionType, setSelectedDeductionType] = useState<DeductionType | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  // Fetch data
  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError('');

      const [typesResponse, policiesResponse] = await Promise.all([
        getDeductionTypes(countryCode),
        getDeductionPolicies(countryCode)
      ]);

      setDeductionTypes(typesResponse.deductionTypes);
      setDeductionPolicies(policiesResponse.deductionPolicies);
    } catch (error: any) {
      console.error('Error fetching deduction data:', error);
      setError(error.message || 'Failed to load deduction data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [countryCode]);

  const handleTypeSuccess = () => {
    setIsTypeModalOpen(false);
    setIsEditModalOpen(false);
    setSelectedDeductionType(null);
    fetchData();
  };

  const handlePolicySuccess = () => {
    setIsPolicyModalOpen(false);
    fetchData();
  };

  const handleEdit = (deductionType: DeductionType) => {
    setSelectedDeductionType(deductionType);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (deductionTypeId: string) => {
    try {
      await deleteDeductionType(countryCode, deductionTypeId);
      setDeleteConfirm(null);
      fetchData();
    } catch (error: any) {
      console.error('Error deleting deduction type:', error);
      setError(error.message || 'Failed to delete deduction type');
    }
  };

  const subTabs = [
    { id: 'types' as SubTabType, name: 'Deduction Types', count: deductionTypes.length },
    { id: 'policies' as SubTabType, name: 'Deduction Policies', count: deductionPolicies.length },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Deductions</h2>
          <p className="text-sm text-gray-600">
            Manage deduction types and policies for {country.name}
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setIsPolicyModalOpen(true)}
            disabled={deductionTypes.length === 0}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-2 px-4 text-sm font-medium rounded-md transition-colors flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create Policy
          </button>
          <button
            onClick={() => setIsTypeModalOpen(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 text-sm font-medium rounded-md transition-colors flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create Type
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <DashboardCard title="Error">
          <div className="text-red-600">{error}</div>
        </DashboardCard>
      )}

      {/* Sub Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {subTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveSubTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeSubTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name} ({tab.count})
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeSubTab === 'types' && (
          <DashboardCard title={`Deduction Types (${deductionTypes.length})`}>
            {deductionTypes.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Deduction Types</h3>
                <p className="text-gray-600 mb-4">
                  Create your first deduction type to get started.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {deductionTypes.map((deductionType) => (
                  <div key={deductionType.deduction_type_id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">
                            {deductionType.name}
                          </h3>
                          <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                            {deductionType.code}
                          </span>
                          {deductionType.is_mandatory && (
                            <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                              Mandatory
                            </span>
                          )}
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            deductionType.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {deductionType.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>

                        <div className="mt-2 space-y-1 text-sm text-gray-600">
                          <div>{deductionType.description}</div>
                          <div>Calculation Base: {deductionType.calculation_base}</div>
                          <div className="flex items-center space-x-4">
                            <div className="flex items-center">
                              <span className={`w-2 h-2 rounded-full mr-2 ${
                                deductionType.has_employee_contribution ? 'bg-green-500' : 'bg-gray-300'
                              }`}></span>
                              Employee Contribution
                            </div>
                            <div className="flex items-center">
                              <span className={`w-2 h-2 rounded-full mr-2 ${
                                deductionType.has_employer_contribution ? 'bg-green-500' : 'bg-gray-300'
                              }`}></span>
                              Employer Contribution
                            </div>
                          </div>
                          <div>Created: {new Date(deductionType.created_at).toLocaleDateString()}</div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEdit(deductionType)}
                          className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 text-xs font-medium rounded transition-colors"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => setDeleteConfirm(deductionType.deduction_type_id)}
                          className="bg-red-600 hover:bg-red-700 text-white py-1 px-3 text-xs font-medium rounded transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </DashboardCard>
        )}

        {activeSubTab === 'policies' && (
          <DashboardCard title={`Deduction Policies (${deductionPolicies.length})`}>
            {deductionTypes.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-yellow-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Deduction Types Required</h3>
                <p className="text-gray-600 mb-4">
                  You need to create deduction types first before setting up policies.
                </p>
              </div>
            ) : deductionPolicies.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Deduction Policies</h3>
                <p className="text-gray-600 mb-4">
                  Create your first deduction policy to set rates and rules.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {deductionPolicies.map((policy) => (
                  <div key={policy.policy_id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-medium text-gray-900">
                            {policy.deduction_type_code}
                          </h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            policy.is_active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {policy.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>

                        <div className="mt-2 space-y-1 text-sm text-gray-600">
                          <div className="grid grid-cols-2 gap-4">
                            <div>Employee Rate: {(policy.employee_rate * 100).toFixed(2)}%</div>
                            <div>Employer Rate: {(policy.employer_rate * 100).toFixed(2)}%</div>
                          </div>
                          <div>Effective From: {new Date(policy.effective_from).toLocaleDateString()}</div>
                          <div>Version: {policy.version_number}</div>
                          {policy.change_reason && (
                            <div>Reason: {policy.change_reason}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </DashboardCard>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Delete</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this deduction type? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setDeleteConfirm(null)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDelete(deleteConfirm)}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      <DeductionTypeModal
        isOpen={isTypeModalOpen}
        onClose={() => setIsTypeModalOpen(false)}
        onSuccess={handleTypeSuccess}
        countryCode={countryCode}
      />

      {selectedDeductionType && (
        <DeductionTypeModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedDeductionType(null);
          }}
          onSuccess={handleTypeSuccess}
          countryCode={countryCode}
          deductionType={selectedDeductionType}
          isEditing={true}
        />
      )}

      <DeductionPolicyModal
        isOpen={isPolicyModalOpen}
        onClose={() => setIsPolicyModalOpen(false)}
        onSuccess={handlePolicySuccess}
        countryCode={countryCode}
        deductionTypes={deductionTypes}
      />
    </div>
  );
};

export default DeductionsTab;
