'use client';

import React, { useState, useEffect } from 'react';
import { Country, TaxPolicy, EmployeeType } from '@/types/payroll';
import { getTaxPolicies, getEmployeeTypes } from '@/lib/payroll';
import DashboardCard from '@/components/ui/DashboardCard';
import TaxPolicyModal from '@/components/super-admin/modals/TaxPolicyModal';
import TaxBracketsModal from '@/components/super-admin/modals/TaxBracketsModal';

interface TaxPoliciesTabProps {
  countryCode: string;
  country: Country;
}

const TaxPoliciesTab: React.FC<TaxPoliciesTabProps> = ({ countryCode, country }) => {
  const [policies, setPolicies] = useState<TaxPolicy[]>([]);
  const [employeeTypes, setEmployeeTypes] = useState<EmployeeType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isBracketsModalOpen, setIsBracketsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<TaxPolicy | null>(null);

  // Fetch tax policies and employee types
  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const [policiesResponse, employeeTypesResponse] = await Promise.all([
        getTaxPolicies(countryCode),
        getEmployeeTypes(countryCode)
      ]);
      
      setPolicies(policiesResponse.policies);
      setEmployeeTypes(employeeTypesResponse.employeeTypes);
    } catch (error: any) {
      console.error('Error fetching data:', error);
      setError(error.message || 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [countryCode]);

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    fetchData();
  };

  const handleSetupBrackets = (policy: TaxPolicy) => {
    setSelectedPolicy(policy);
    setIsBracketsModalOpen(true);
  };

  const handleBracketsSuccess = () => {
    setIsBracketsModalOpen(false);
    setSelectedPolicy(null);
    fetchData();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Tax Policies</h2>
          <p className="text-sm text-gray-600">
            Manage tax policies and brackets for {country.name}
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          disabled={employeeTypes.length === 0}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-2 px-4 text-sm font-medium rounded-md transition-colors flex items-center"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Create Tax Policy
        </button>
      </div>

      {/* Employee Types Warning */}
      {employeeTypes.length === 0 && (
        <DashboardCard title="Setup Required">
          <div className="text-center py-8">
            <div className="text-yellow-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Employee Types Required</h3>
            <p className="text-gray-600 mb-4">
              You need to create employee types first before setting up tax policies.
            </p>
          </div>
        </DashboardCard>
      )}

      {/* Error Display */}
      {error && (
        <DashboardCard title="Error">
          <div className="text-red-600">{error}</div>
        </DashboardCard>
      )}

      {/* Policies List */}
      {employeeTypes.length > 0 && (
        <DashboardCard title={`Tax Policies (${policies.length})`}>
          {policies.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Tax Policies</h3>
              <p className="text-gray-600 mb-4">
                Create your first tax policy to get started with payroll management.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {policies.map((policy) => (
                <div key={policy.policy_id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-medium text-gray-900">
                          {policy.policy_type_name}
                        </h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          policy.is_active 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {policy.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      
                      <div className="mt-2 space-y-1 text-sm text-gray-600">
                        <div>Employee Type: {policy.employee_type_name}</div>
                        <div>Effective From: {new Date(policy.effective_from).toLocaleDateString()}</div>
                        {policy.effective_to && (
                          <div>Effective To: {new Date(policy.effective_to).toLocaleDateString()}</div>
                        )}
                        <div>Version: {policy.version_number}</div>
                        {policy.change_reason && (
                          <div>Reason: {policy.change_reason}</div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleSetupBrackets(policy)}
                        className="bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 text-xs font-medium rounded transition-colors"
                      >
                        Setup Brackets
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </DashboardCard>
      )}

      {/* Modals */}
      <TaxPolicyModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
        countryCode={countryCode}
        employeeTypes={employeeTypes}
      />

      {selectedPolicy && (
        <TaxBracketsModal
          isOpen={isBracketsModalOpen}
          onClose={() => {
            setIsBracketsModalOpen(false);
            setSelectedPolicy(null);
          }}
          onSuccess={handleBracketsSuccess}
          policy={selectedPolicy}
        />
      )}
    </div>
  );
};

export default TaxPoliciesTab;
