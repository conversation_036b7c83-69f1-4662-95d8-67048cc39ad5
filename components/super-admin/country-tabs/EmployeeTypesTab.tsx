'use client';

import React, { useState, useEffect } from 'react';
import { Country, EmployeeType } from '@/types/payroll';
import { getEmployeeTypes, deleteEmployeeType } from '@/lib/payroll';
import DashboardCard from '@/components/ui/DashboardCard';
import EmployeeTypeModal from '@/components/super-admin/modals/EmployeeTypeModal';

interface EmployeeTypesTabProps {
  countryCode: string;
  country: Country;
}

const EmployeeTypesTab: React.FC<EmployeeTypesTabProps> = ({ countryCode, country }) => {
  const [employeeTypes, setEmployeeTypes] = useState<EmployeeType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedEmployeeType, setSelectedEmployeeType] = useState<EmployeeType | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  // Fetch employee types
  const fetchEmployeeTypes = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const response = await getEmployeeTypes(countryCode);
      setEmployeeTypes(response.employeeTypes);
    } catch (error: any) {
      console.error('Error fetching employee types:', error);
      setError(error.message || 'Failed to load employee types');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployeeTypes();
  }, [countryCode]);

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    fetchEmployeeTypes();
  };

  const handleEditSuccess = () => {
    setIsEditModalOpen(false);
    setSelectedEmployeeType(null);
    fetchEmployeeTypes();
  };

  const handleEdit = (employeeType: EmployeeType) => {
    setSelectedEmployeeType(employeeType);
    setIsEditModalOpen(true);
  };

  const handleDelete = async (employeeTypeId: string) => {
    try {
      await deleteEmployeeType(countryCode, employeeTypeId);
      setDeleteConfirm(null);
      fetchEmployeeTypes();
    } catch (error: any) {
      console.error('Error deleting employee type:', error);
      setError(error.message || 'Failed to delete employee type');
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Employee Types</h2>
          <p className="text-sm text-gray-600">
            Manage employee types for {country.name}
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 text-sm font-medium rounded-md transition-colors flex items-center"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Create Employee Type
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <DashboardCard title="Error">
          <div className="text-red-600">{error}</div>
        </DashboardCard>
      )}

      {/* Employee Types List */}
      <DashboardCard title={`Employee Types (${employeeTypes.length})`}>
        {employeeTypes.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Employee Types</h3>
            <p className="text-gray-600 mb-4">
              Create your first employee type to get started with payroll management.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {employeeTypes.map((employeeType) => (
              <div key={employeeType.employee_type_id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900">
                        {employeeType.name}
                      </h3>
                      <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                        {employeeType.code}
                      </span>
                      {employeeType.is_default && (
                        <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                          Default
                        </span>
                      )}
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        employeeType.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {employeeType.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    
                    <div className="mt-2 space-y-1 text-sm text-gray-600">
                      <div>{employeeType.description}</div>
                      <div>Sort Order: {employeeType.sort_order}</div>
                      <div>Created: {new Date(employeeType.created_at).toLocaleDateString()}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(employeeType)}
                      className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 text-xs font-medium rounded transition-colors"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => setDeleteConfirm(employeeType.employee_type_id)}
                      className="bg-red-600 hover:bg-red-700 text-white py-1 px-3 text-xs font-medium rounded transition-colors"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </DashboardCard>

      {/* Delete Confirmation Modal */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Delete</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete this employee type? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setDeleteConfirm(null)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDelete(deleteConfirm)}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      <EmployeeTypeModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
        countryCode={countryCode}
      />

      {selectedEmployeeType && (
        <EmployeeTypeModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedEmployeeType(null);
          }}
          onSuccess={handleEditSuccess}
          countryCode={countryCode}
          employeeType={selectedEmployeeType}
          isEditing={true}
        />
      )}
    </div>
  );
};

export default EmployeeTypesTab;
