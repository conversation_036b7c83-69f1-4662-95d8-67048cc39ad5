'use client';

import React, { useState, useEffect } from 'react';
import DashboardCard from '@/components/ui/DashboardCard';
import { getCompanies, addCompanyDevice, Company, Device } from '@/lib/companies';

const CompaniesContent = () => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [showAddDeviceModal, setShowAddDeviceModal] = useState(false);
  const [deviceForm, setDeviceForm] = useState({
    device_name: '',
    device_type: 'MOBILE'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch companies
  const fetchCompanies = async () => {
    try {
      setError('');
      setIsLoading(true);
      const response = await getCompanies();
      setCompanies(response.companies);
    } catch (error: any) {
      console.error('Error fetching companies:', error);
      setError(error.message || 'Failed to load companies. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, []);

  // Handle add device
  const handleAddDevice = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCompany) return;

    try {
      setIsSubmitting(true);
      setError('');

      await addCompanyDevice({
        company_id: selectedCompany.company_id,
        device_name: deviceForm.device_name,
        device_type: deviceForm.device_type
      });

      // Reset form and close modal
      setDeviceForm({ device_name: '', device_type: 'MOBILE' });
      setShowAddDeviceModal(false);
      setSelectedCompany(null);

      // Refresh companies list
      await fetchCompanies();
    } catch (error: any) {
      console.error('Error adding device:', error);
      setError(error.message || 'Failed to add device. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">Companies Management</h1>
          <p className="text-secondary mt-1">Manage companies and their devices</p>
        </div>
        <button
          onClick={fetchCompanies}
          disabled={isLoading}
          className="btn-outline"
        >
          {isLoading ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-lg">🏢</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Companies</p>
              <p className="text-2xl font-bold text-gray-900">{companies.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-lg">📱</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Devices</p>
              <p className="text-2xl font-bold text-gray-900">
                {companies.reduce((total, company) => total + company.devices.length, 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-lg">🗄️</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Databases</p>
              <p className="text-2xl font-bold text-gray-900">
                {companies.filter(c => c.database_name).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Companies List */}
      <DashboardCard title="Companies" loading={isLoading}>
        {companies.length === 0 && !isLoading ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No companies found.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {companies.map((company) => (
              <div key={company.company_id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">{company.company_name}</h3>
                    <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                      <div>
                        <p><strong>Company ID:</strong> {company.company_id}</p>
                        <p><strong>TIN:</strong> {company.company_tin || 'Not provided'}</p>
                        <p><strong>Phone:</strong> {company.phone_number || 'Not provided'}</p>
                      </div>
                      <div>
                        <p><strong>Database:</strong> {company.database_name}</p>
                        <p><strong>Created:</strong> {formatDate(company.created_at)}</p>
                        <p><strong>Devices:</strong> {company.devices.length}</p>
                      </div>
                    </div>

                    {/* Devices */}
                    {company.devices.length > 0 && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Devices:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {company.devices.map((device) => (
                            <div key={device.device_id} className="bg-gray-50 rounded p-2 text-xs">
                              <p><strong>{device.device_name}</strong> ({device.device_type})</p>
                              <p className="text-gray-500">Status: {device.status}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="ml-4">
                    <button
                      onClick={() => {
                        setSelectedCompany(company);
                        setShowAddDeviceModal(true);
                      }}
                      className="btn-outline text-sm"
                    >
                      Add Device
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </DashboardCard>

      {/* Add Device Modal */}
      {showAddDeviceModal && selectedCompany && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Add Device to {selectedCompany.company_name}
            </h3>

            <form onSubmit={handleAddDevice} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Device Name
                </label>
                <input
                  type="text"
                  value={deviceForm.device_name}
                  onChange={(e) => setDeviceForm({ ...deviceForm, device_name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Enter device name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Device Type
                </label>
                <select
                  value={deviceForm.device_type}
                  onChange={(e) => setDeviceForm({ ...deviceForm, device_type: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="MOBILE">Mobile</option>
                  <option value="TABLET">Tablet</option>
                  <option value="DESKTOP">Desktop</option>
                  <option value="KIOSK">Kiosk</option>
                </select>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddDeviceModal(false);
                    setSelectedCompany(null);
                    setDeviceForm({ device_name: '', device_type: 'MOBILE' });
                  }}
                  className="btn-outline"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Adding...' : 'Add Device'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompaniesContent;
