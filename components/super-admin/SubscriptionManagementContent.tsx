'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { SubscriptionPlan, CreateSubscriptionPlanRequest } from '@/types/subscription';
import {
  getSubscriptionPlans,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
  formatPrice
} from '@/lib/subscription';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';

const SubscriptionManagementContent: React.FC = () => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<SubscriptionPlan | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState<CreateSubscriptionPlanRequest>({
    name: '',
    description: '',
    flat_price: 0,
    price_per_employee: 0,
    billing_cycle: 'MONTHLY',
    max_employees: null,
    sort_order: 1,
    features: {
      employee_self_service: true,
      attendance_management: true,
      ai_attendance_analytics: { enabled: false, monthly_limit: null },
      leave_management: true,
      announcement_management: { enabled: false, monthly_limit: null },
      shift_management: { enabled: false, shift_limit: null },
      basic_reports: true,
      advanced_reports: false,
      support: 'email'
    }
  });

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const fetchedPlans = await getSubscriptionPlans();
      setPlans(fetchedPlans.sort((a, b) => a.sort_order - b.sort_order));
    } catch (err) {
      setError('Failed to load subscription plans. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePlan = () => {
    setEditingPlan(null);
    setFormData({
      name: '',
      description: '',
      flat_price: 0,
      price_per_employee: 0,
      billing_cycle: 'MONTHLY',
      max_employees: null,
      sort_order: plans.length + 1,
      features: {
        employee_self_service: true,
        attendance_management: true,
        ai_attendance_analytics: { enabled: false, monthly_limit: null },
        leave_management: true,
        announcement_management: { enabled: false, monthly_limit: null },
        shift_management: { enabled: false, shift_limit: null },
        basic_reports: true,
        advanced_reports: false,
        support: 'email'
      }
    });
    setIsModalOpen(true);
  };

  const handleEditPlan = (plan: SubscriptionPlan) => {
    setEditingPlan(plan);
    setFormData({
      name: plan.name,
      description: plan.description,
      flat_price: plan.flat_price,
      price_per_employee: plan.price_per_employee,
      billing_cycle: plan.billing_cycle,
      max_employees: plan.max_employees,
      sort_order: plan.sort_order,
      features: plan.features
    });
    setIsModalOpen(true);
  };

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('Are you sure you want to delete this subscription plan? This action cannot be undone.')) {
      return;
    }

    try {
      await deleteSubscriptionPlan(planId);
      await fetchPlans();
    } catch (err) {
      alert('Failed to delete plan. Please try again.');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (editingPlan) {
        await updateSubscriptionPlan(editingPlan.plan_id, formData);
      } else {
        await createSubscriptionPlan(formData);
      }
      
      setIsModalOpen(false);
      await fetchPlans();
    } catch (err) {
      alert('Failed to save plan. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const stats = [
    {
      title: 'Total Plans',
      value: plans.length.toString(),
      change: '',
      changeType: 'neutral' as const
    },
    {
      title: 'Active Plans',
      value: plans.filter(p => p.is_active).length.toString(),
      change: '',
      changeType: 'positive' as const
    },
    {
      title: 'Inactive Plans',
      value: plans.filter(p => !p.is_active).length.toString(),
      change: '',
      changeType: 'neutral' as const
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Subscription Management</h1>
        <button
          onClick={handleCreatePlan}
          className="btn-primary py-2 px-4 text-sm font-medium rounded-md transition-all flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Create Plan
        </button>
      </div>
      
      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/super-admin" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Subscriptions</span>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <DashboardStats
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
          />
        ))}
      </div>

      {/* Plans List */}
      <DashboardCard title="Subscription Plans">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading plans...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <p className="font-medium">Unable to load plans</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          </div>
        ) : plans.length === 0 ? (
          <div className="text-center py-8">
            <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p className="text-gray-600 mb-4">No subscription plans found</p>
            <button
              onClick={handleCreatePlan}
              className="btn-primary py-2 px-4 text-sm"
            >
              Create Your First Plan
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pricing
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Employees
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {plans.map((plan) => (
                  <tr key={plan.plan_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{plan.name}</div>
                        <div className="text-sm text-gray-500">{plan.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatPrice(plan.flat_price)}/mo
                      </div>
                      <div className="text-sm text-gray-500">
                        + {formatPrice(plan.price_per_employee)}/employee
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {plan.max_employees ? plan.max_employees : 'Unlimited'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        plan.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {plan.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleEditPlan(plan)}
                        className="text-primary hover:text-primary-dark"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeletePlan(plan.plan_id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </DashboardCard>

      {/* Create/Edit Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingPlan ? 'Edit Subscription Plan' : 'Create Subscription Plan'}
                </h3>
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4 max-h-[500px] overflow-y-auto">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Plan Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                      placeholder="e.g., Starter, Professional, Enterprise"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sort Order *
                    </label>
                    <input
                      type="number"
                      required
                      min="1"
                      value={formData.sort_order}
                      onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    required
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                    placeholder="Brief description of the plan"
                  />
                </div>

                {/* Pricing */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Base Price ($) *
                    </label>
                    <input
                      type="number"
                      required
                      min="0"
                      step="0.01"
                      value={formData.flat_price}
                      onChange={(e) => setFormData({ ...formData, flat_price: parseFloat(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Per Employee ($) *
                    </label>
                    <input
                      type="number"
                      required
                      min="0"
                      step="0.01"
                      value={formData.price_per_employee}
                      onChange={(e) => setFormData({ ...formData, price_per_employee: parseFloat(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Billing Cycle *
                    </label>
                    <select
                      required
                      value={formData.billing_cycle}
                      onChange={(e) => setFormData({ ...formData, billing_cycle: e.target.value as any })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                    >
                      <option value="MONTHLY">Monthly</option>
                      <option value="ANNUAL">Annual</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Max Employees
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={formData.max_employees || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        max_employees: e.target.value ? parseInt(e.target.value) : null
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                      placeholder="Leave empty for unlimited"
                    />
                  </div>
                </div>



                {/* Features */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-700">Features</h4>

                  {/* Basic Features */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.features.employee_self_service}
                        onChange={(e) => setFormData({
                          ...formData,
                          features: { ...formData.features, employee_self_service: e.target.checked }
                        })}
                        className="mr-2"
                      />
                      Employee Self Service
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.features.attendance_management}
                        onChange={(e) => setFormData({
                          ...formData,
                          features: { ...formData.features, attendance_management: e.target.checked }
                        })}
                        className="mr-2"
                      />
                      Attendance Management
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.features.leave_management}
                        onChange={(e) => setFormData({
                          ...formData,
                          features: { ...formData.features, leave_management: e.target.checked }
                        })}
                        className="mr-2"
                      />
                      Leave Management
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.features.basic_reports}
                        onChange={(e) => setFormData({
                          ...formData,
                          features: { ...formData.features, basic_reports: e.target.checked }
                        })}
                        className="mr-2"
                      />
                      Basic Reports
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.features.advanced_reports}
                        onChange={(e) => setFormData({
                          ...formData,
                          features: { ...formData.features, advanced_reports: e.target.checked }
                        })}
                        className="mr-2"
                      />
                      Advanced Reports
                    </label>
                  </div>

                  {/* AI Attendance Analytics */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center mb-3">
                      <input
                        type="checkbox"
                        checked={formData.features.ai_attendance_analytics.enabled}
                        onChange={(e) => setFormData({
                          ...formData,
                          features: {
                            ...formData.features,
                            ai_attendance_analytics: {
                              ...formData.features.ai_attendance_analytics,
                              enabled: e.target.checked
                            }
                          }
                        })}
                        className="mr-2"
                      />
                      <label className="font-medium text-gray-700">AI Attendance Analytics</label>
                    </div>
                    {formData.features.ai_attendance_analytics.enabled && (
                      <div className="ml-6">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Monthly Limit
                        </label>
                        <input
                          type="number"
                          min="1"
                          value={formData.features.ai_attendance_analytics.monthly_limit || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            features: {
                              ...formData.features,
                              ai_attendance_analytics: {
                                ...formData.features.ai_attendance_analytics,
                                monthly_limit: e.target.value ? parseInt(e.target.value) : null
                              }
                            }
                          })}
                          className="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                          placeholder="Unlimited"
                        />
                        <p className="text-xs text-gray-500 mt-1">Leave empty for unlimited</p>
                      </div>
                    )}
                  </div>

                  {/* Announcement Management */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center mb-3">
                      <input
                        type="checkbox"
                        checked={formData.features.announcement_management.enabled}
                        onChange={(e) => setFormData({
                          ...formData,
                          features: {
                            ...formData.features,
                            announcement_management: {
                              ...formData.features.announcement_management,
                              enabled: e.target.checked
                            }
                          }
                        })}
                        className="mr-2"
                      />
                      <label className="font-medium text-gray-700">Announcement Management</label>
                    </div>
                    {formData.features.announcement_management.enabled && (
                      <div className="ml-6">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Monthly Limit
                        </label>
                        <input
                          type="number"
                          min="1"
                          value={formData.features.announcement_management.monthly_limit || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            features: {
                              ...formData.features,
                              announcement_management: {
                                ...formData.features.announcement_management,
                                monthly_limit: e.target.value ? parseInt(e.target.value) : null
                              }
                            }
                          })}
                          className="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                          placeholder="Unlimited"
                        />
                        <p className="text-xs text-gray-500 mt-1">Leave empty for unlimited</p>
                      </div>
                    )}
                  </div>

                  {/* Shift Management */}
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center mb-3">
                      <input
                        type="checkbox"
                        checked={formData.features.shift_management.enabled}
                        onChange={(e) => setFormData({
                          ...formData,
                          features: {
                            ...formData.features,
                            shift_management: {
                              ...formData.features.shift_management,
                              enabled: e.target.checked
                            }
                          }
                        })}
                        className="mr-2"
                      />
                      <label className="font-medium text-gray-700">Shift Management</label>
                    </div>
                    {formData.features.shift_management.enabled && (
                      <div className="ml-6">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Shift Limit
                        </label>
                        <input
                          type="number"
                          min="1"
                          value={formData.features.shift_management.shift_limit || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            features: {
                              ...formData.features,
                              shift_management: {
                                ...formData.features.shift_management,
                                shift_limit: e.target.value ? parseInt(e.target.value) : null
                              }
                            }
                          })}
                          className="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                          placeholder="Unlimited"
                        />
                        <p className="text-xs text-gray-500 mt-1">Leave empty for unlimited</p>
                      </div>
                    )}
                  </div>

                  {/* Support Level */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Support Level
                    </label>
                    <select
                      value={formData.features.support}
                      onChange={(e) => setFormData({
                        ...formData,
                        features: { ...formData.features, support: e.target.value as any }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                    >
                      <option value="email">Email Support</option>
                      <option value="email_chat">Email & Chat Support</option>
                      <option value="priority">Priority Support</option>
                    </select>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    type="button"
                    onClick={() => setIsModalOpen(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
                  >
                    {isSubmitting ? 'Saving...' : (editingPlan ? 'Update Plan' : 'Create Plan')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionManagementContent;
