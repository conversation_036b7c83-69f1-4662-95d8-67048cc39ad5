import React from 'react';

const Benefits = () => {
  return (
    <section id="benefits" className="section bg-background">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
            Why Choose KaziSync?
          </h2>
          <p className="text-secondary text-lg">
            Discover how <PERSON><PERSON><PERSON><PERSON> can transform your attendance management and improve operational efficiency.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Left Column - Image */}
          <div className="flex items-center justify-center">
            <div className="relative">
              <div className="bg-white rounded-lg shadow-xl p-4 md:p-6 relative z-10">
                <div className="aspect-w-4 aspect-h-3 bg-background-dark rounded-lg flex items-center justify-center">
                  {/* Placeholder for benefits image/illustration */}
                  <div className="text-center p-12">
                    <svg
                      className="h-24 w-24 mx-auto text-primary opacity-80"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                      />
                    </svg>
                    <p className="mt-4 text-secondary-dark font-medium">Benefits Illustration</p>
                  </div>
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute -bottom-4 -left-4 w-48 h-48 bg-primary-light opacity-10 rounded-full z-0"></div>
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary opacity-10 rounded-full z-0"></div>
            </div>
          </div>

          {/* Right Column - Benefits List */}
          <div className="space-y-6">
            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Increased Productivity
                </h3>
                <p className="text-secondary">
                  Automate attendance tracking and leave management processes, freeing up HR resources for more strategic tasks.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Enhanced Accuracy
                </h3>
                <p className="text-secondary">
                  Eliminate manual errors and ensure precise attendance records with biometric.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Real-time Insights
                </h3>
                <p className="text-secondary">
                  Access up-to-date attendance data and analytics to make informed decisions about workforce management.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Improved Compliance
                </h3>
                <p className="text-secondary">
                  Maintain accurate records of working hours and leaves to ensure compliance with labor regulations.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 mt-1">
                <div className="bg-primary-light bg-opacity-20 rounded-full p-2">
                  <svg
                    className="h-6 w-6 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                  Cost Efficiency
                </h3>
                <p className="text-secondary">
                  Reduce administrative overhead and minimize time theft with accurate attendance tracking.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Benefits;
