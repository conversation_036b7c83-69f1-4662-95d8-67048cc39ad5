"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { apiPost } from '@/lib/api';

// Define the registration data interface
interface RegistrationData {
  first_name: string;
  last_name: string;
  email: string;
  confirm_email: string;
  password: string;
  confirm_password: string;
  phone_number: string;
  role: string;
}

// Define the registration response interface
interface RegistrationResponse {
  registered: boolean;
  message?: string;
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    username: string;
  };
}

const SignupForm = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    confirm_email: '',
    phone_number: '',
    password: '',
    confirm_password: '',
    agreeTerms: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    // Basic validation
    if (formData.password !== formData.confirm_password) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    if (formData.email !== formData.confirm_email) {
      setError('Email addresses do not match');
      setIsLoading(false);
      return;
    }

    if (!formData.agreeTerms) {
      setError('You must agree to the Terms of Service and Privacy Policy');
      setIsLoading(false);
      return;
    }

    try {
      // Validate all fields are filled
      const requiredFields = [
        'first_name', 'last_name', 'email', 'confirm_email',
        'password', 'confirm_password', 'phone_number'
      ];

      for (const field of requiredFields) {
        if (!formData[field as keyof typeof formData] ||
            (typeof formData[field as keyof typeof formData] === 'string' &&
             (formData[field as keyof typeof formData] as string).trim() === '')) {
          setError(`${field.replace('_', ' ')} is required`);
          setIsLoading(false);
          return;
        }
      }

      // Prepare the registration data
      const registrationData: RegistrationData = {
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        email: formData.email.trim(),
        confirm_email: formData.confirm_email.trim(),
        password: formData.password,
        confirm_password: formData.confirm_password,
        phone_number: formData.phone_number.trim(),
        role: 'hr' // Default role is hr
      };



      try {
        // Make the API call to register the user
        const response = await apiPost<any>(
          'register_user',
          registrationData
        );

        console.log('Registration response:', response);
        console.log('Response type:', typeof response);
        console.log('Response keys:', Object.keys(response));
        console.log('Response JSON:', JSON.stringify(response, null, 2));

        // Check if the response has the expected structure
        if (response && typeof response === 'object') {
          // Check for various success indicators in the response
          const isSuccess =
            response.registered === true ||
            response.success === true ||
            (response.user && response.user.id) ||
            (typeof response === 'object' && Object.keys(response).includes('user'));

          if (isSuccess) {
            setSuccessMessage('Account created successfully! Redirecting to login...');

            // Get the username (email) to pass to the login page
            const username = formData.email;

            // Redirect to login page with the username as a query parameter
            // Use a shorter delay for faster redirection
            setTimeout(() => {
              router.push(`/login?username=${encodeURIComponent(username)}`);
            }, 500);
          } else if (response.message) {
            setError(response.message);
          } else {
            // If we get here, the user might still be registered but the response format is unexpected
            console.warn('Unexpected success response format, but assuming success:', response);
            setSuccessMessage('Account may have been created. Redirecting to login...');

            // Get the username (email) to pass to the login page
            const username = formData.email;

            // Redirect to login page with the username as a query parameter
            // Use a shorter delay for faster redirection
            setTimeout(() => {
              router.push(`/login?username=${encodeURIComponent(username)}`);
            }, 500);
          }
        } else {
          console.error('Unexpected response format:', response);
          setError('Received an invalid response from the server. Please try again.');
        }
      } catch (apiError: any) {
        console.error('API error:', apiError);

        // Display the error message from the API
        if (apiError.message) {
          setError(apiError.message);
        } else {
          setError('An error occurred during registration. Please try again.');
        }
      }
    } catch (err: any) {
      console.error('Registration error:', err);

      // Display a user-friendly error message
      if (err.message && !err.message.startsWith('API error:')) {
        setError(err.message);
      } else {
        setError('An error occurred during registration. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
          {successMessage}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="first_name" className="block text-sm font-medium text-secondary-dark mb-1">
            First Name
          </label>
          <input
            id="first_name"
            name="first_name"
            type="text"
            required
            value={formData.first_name}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="John"
          />
        </div>
        <div>
          <label htmlFor="last_name" className="block text-sm font-medium text-secondary-dark mb-1">
            Last Name
          </label>
          <input
            id="last_name"
            name="last_name"
            type="text"
            required
            value={formData.last_name}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Doe"
          />
        </div>
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-secondary-dark mb-1">
          Email Address
        </label>
        <input
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          value={formData.email}
          onChange={handleChange}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          placeholder="<EMAIL>"
        />
      </div>

      <div>
        <label htmlFor="confirm_email" className="block text-sm font-medium text-secondary-dark mb-1">
          Confirm Email Address
        </label>
        <input
          id="confirm_email"
          name="confirm_email"
          type="email"
          autoComplete="email"
          required
          value={formData.confirm_email}
          onChange={handleChange}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          placeholder="<EMAIL>"
        />
      </div>

      <div>
        <label htmlFor="phone_number" className="block text-sm font-medium text-secondary-dark mb-1">
          Phone Number
        </label>
        <input
          id="phone_number"
          name="phone_number"
          type="tel"
          autoComplete="tel"
          required
          value={formData.phone_number}
          onChange={handleChange}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          placeholder="0788000000"
        />
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-secondary-dark mb-1">
          Password
        </label>
        <input
          id="password"
          name="password"
          type="password"
          autoComplete="new-password"
          required
          value={formData.password}
          onChange={handleChange}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          placeholder="••••••••"
        />
        <p className="mt-1 text-xs text-secondary">
          Password must be at least 6 characters long.
        </p>
      </div>

      <div>
        <label htmlFor="confirm_password" className="block text-sm font-medium text-secondary-dark mb-1">
          Confirm Password
        </label>
        <input
          id="confirm_password"
          name="confirm_password"
          type="password"
          autoComplete="new-password"
          required
          value={formData.confirm_password}
          onChange={handleChange}
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          placeholder="••••••••"
        />
      </div>

      <div className="flex items-center">
        <input
          id="agreeTerms"
          name="agreeTerms"
          type="checkbox"
          required
          checked={formData.agreeTerms}
          onChange={handleChange}
          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
        />
        <label htmlFor="agreeTerms" className="ml-2 block text-sm text-secondary">
          I agree to the Terms of Service and Privacy Policy
        </label>
      </div>

      <div>
        <button
          type="submit"
          disabled={isLoading}
          className="w-full btn-primary py-2 flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating Account...
            </>
          ) : (
            'Create Account'
          )}
        </button>
      </div>
    </form>
  );
};

export default SignupForm;
