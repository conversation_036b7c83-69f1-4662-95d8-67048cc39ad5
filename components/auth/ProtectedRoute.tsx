'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentUser } from '@/lib/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  allowedRoles = []
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Prefetch login page for potential redirects
    router.prefetch('/login');
    router.prefetch('/dashboard');

    // Function to check authorization
    const checkAuthorization = () => {
      // Get user directly from storage
      const user = getCurrentUser();

      if (!user) {
        // Not authenticated, redirect to login
        setIsLoading(false);
        setIsAuthorized(false);

        // Use a small timeout to ensure state updates before navigation
        setTimeout(() => {
          router.replace('/login');
        }, 10);
        return false;
      }

      // Check if user has required role
      if (allowedRoles.length > 0) {
        // Normalize roles for comparison (handle different formats)
        const normalizedUserRole = user.role.toLowerCase().replace(/\s+/g, '-');
        const normalizedAllowedRoles = allowedRoles.map(role => role.toLowerCase().replace(/\s+/g, '-'));

        if (!normalizedAllowedRoles.includes(normalizedUserRole)) {
          // Always update loading state before redirecting
          setIsLoading(false);
          setIsAuthorized(false);

          // Use a small timeout to ensure state updates before navigation
          setTimeout(() => {
            router.replace('/dashboard');
          }, 10);
          return false;
        }
      }

      // User is authenticated and authorized
      return true;
    };

    // Run the check and update state based on result
    const isUserAuthorized = checkAuthorization();

    if (isUserAuthorized) {
      // User is authenticated and authorized - update state
      setIsAuthorized(true);
      setIsLoading(false);
    }
  }, [router, allowedRoles]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin h-12 w-12 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-secondary-dark">Loading...</p>
        </div>
      </div>
    );
  }

  // If authorized, render children
  return isAuthorized ? <>{children}</> : null;
};

export default ProtectedRoute;
