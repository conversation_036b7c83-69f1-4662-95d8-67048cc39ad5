"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

const OnboardingForm = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Company Information
    companyName: '',
    industry: '',
    companySize: '',
    companyAddress: '',
    companyPhone: '',
    companyWebsite: '',
    
    // Work Settings
    workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
    workHoursStart: '09:00',
    workHoursEnd: '17:00',
    lunchBreakStart: '12:00',
    lunchBreakEnd: '13:00',
    
    // Leave Settings
    annualLeave: '20',
    sickLeave: '10',
    personalLeave: '5',
    
    // Locations
    locations: [{ name: 'Main Office', address: '' }],
    
    // Departments
    departments: [{ name: 'Administration', description: '' }],
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleWorkDayToggle = (day: string) => {
    const updatedWorkDays = formData.workDays.includes(day)
      ? formData.workDays.filter(d => d !== day)
      : [...formData.workDays, day];
    
    setFormData({
      ...formData,
      workDays: updatedWorkDays,
    });
  };

  const handleLocationChange = (index: number, field: string, value: string) => {
    const updatedLocations = [...formData.locations];
    updatedLocations[index] = { ...updatedLocations[index], [field]: value };
    
    setFormData({
      ...formData,
      locations: updatedLocations,
    });
  };

  const addLocation = () => {
    setFormData({
      ...formData,
      locations: [...formData.locations, { name: '', address: '' }],
    });
  };

  const removeLocation = (index: number) => {
    const updatedLocations = [...formData.locations];
    updatedLocations.splice(index, 1);
    
    setFormData({
      ...formData,
      locations: updatedLocations,
    });
  };

  const handleDepartmentChange = (index: number, field: string, value: string) => {
    const updatedDepartments = [...formData.departments];
    updatedDepartments[index] = { ...updatedDepartments[index], [field]: value };
    
    setFormData({
      ...formData,
      departments: updatedDepartments,
    });
  };

  const addDepartment = () => {
    setFormData({
      ...formData,
      departments: [...formData.departments, { name: '', description: '' }],
    });
  };

  const removeDepartment = (index: number) => {
    const updatedDepartments = [...formData.departments];
    updatedDepartments.splice(index, 1);
    
    setFormData({
      ...formData,
      departments: updatedDepartments,
    });
  };

  const nextStep = () => {
    setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // In a real application, this would be an API call to save the onboarding data
      // For now, we'll simulate a successful onboarding and redirect to the dashboard
      await new Promise((resolve) => setTimeout(resolve, 1500));
      router.push('/dashboard/admin');
    } catch (err) {
      console.error('Error during onboarding:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  currentStep >= step
                    ? 'bg-primary text-white'
                    : 'bg-gray-200 text-secondary'
                }`}
              >
                {step}
              </div>
              <span className="text-xs mt-2 text-secondary">
                {step === 1
                  ? 'Company Info'
                  : step === 2
                  ? 'Work Settings'
                  : step === 3
                  ? 'Locations'
                  : 'Departments'}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-2">
          <div className="absolute top-0 left-0 h-1 bg-gray-200 w-full"></div>
          <div
            className="absolute top-0 left-0 h-1 bg-primary transition-all duration-300"
            style={{ width: `${((currentStep - 1) / 3) * 100}%` }}
          ></div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Step 1: Company Information */}
        {currentStep === 1 && (
          <div className="space-y-6 animate-fade-in">
            <h2 className="text-xl font-semibold text-secondary-dark mb-4">Company Information</h2>
            
            <div>
              <label htmlFor="companyName" className="block text-sm font-medium text-secondary-dark mb-1">
                Company Name *
              </label>
              <input
                id="companyName"
                name="companyName"
                type="text"
                required
                value={formData.companyName}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="industry" className="block text-sm font-medium text-secondary-dark mb-1">
                  Industry *
                </label>
                <select
                  id="industry"
                  name="industry"
                  required
                  value={formData.industry}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">Select Industry</option>
                  <option value="Technology">Technology</option>
                  <option value="Healthcare">Healthcare</option>
                  <option value="Finance">Finance</option>
                  <option value="Education">Education</option>
                  <option value="Retail">Retail</option>
                  <option value="Manufacturing">Manufacturing</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="companySize" className="block text-sm font-medium text-secondary-dark mb-1">
                  Company Size *
                </label>
                <select
                  id="companySize"
                  name="companySize"
                  required
                  value={formData.companySize}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">Select Size</option>
                  <option value="1-10">1-10 employees</option>
                  <option value="11-50">11-50 employees</option>
                  <option value="51-200">51-200 employees</option>
                  <option value="201-500">201-500 employees</option>
                  <option value="501+">501+ employees</option>
                </select>
              </div>
            </div>
            
            <div>
              <label htmlFor="companyAddress" className="block text-sm font-medium text-secondary-dark mb-1">
                Company Address *
              </label>
              <textarea
                id="companyAddress"
                name="companyAddress"
                required
                value={formData.companyAddress}
                onChange={handleChange}
                rows={3}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              ></textarea>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="companyPhone" className="block text-sm font-medium text-secondary-dark mb-1">
                  Company Phone *
                </label>
                <input
                  id="companyPhone"
                  name="companyPhone"
                  type="tel"
                  required
                  value={formData.companyPhone}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="companyWebsite" className="block text-sm font-medium text-secondary-dark mb-1">
                  Company Website
                </label>
                <input
                  id="companyWebsite"
                  name="companyWebsite"
                  type="url"
                  value={formData.companyWebsite}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Work Settings */}
        {currentStep === 2 && (
          <div className="space-y-6 animate-fade-in">
            <h2 className="text-xl font-semibold text-secondary-dark mb-4">Work Settings</h2>
            
            <div>
              <label className="block text-sm font-medium text-secondary-dark mb-2">
                Work Days *
              </label>
              <div className="flex flex-wrap gap-2">
                {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                  <button
                    key={day}
                    type="button"
                    onClick={() => handleWorkDayToggle(day)}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      formData.workDays.includes(day)
                        ? 'bg-primary text-white'
                        : 'bg-gray-100 text-secondary-dark'
                    }`}
                  >
                    {day}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="workHoursStart" className="block text-sm font-medium text-secondary-dark mb-1">
                  Work Hours Start *
                </label>
                <input
                  id="workHoursStart"
                  name="workHoursStart"
                  type="time"
                  required
                  value={formData.workHoursStart}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="workHoursEnd" className="block text-sm font-medium text-secondary-dark mb-1">
                  Work Hours End *
                </label>
                <input
                  id="workHoursEnd"
                  name="workHoursEnd"
                  type="time"
                  required
                  value={formData.workHoursEnd}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="lunchBreakStart" className="block text-sm font-medium text-secondary-dark mb-1">
                  Lunch Break Start *
                </label>
                <input
                  id="lunchBreakStart"
                  name="lunchBreakStart"
                  type="time"
                  required
                  value={formData.lunchBreakStart}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="lunchBreakEnd" className="block text-sm font-medium text-secondary-dark mb-1">
                  Lunch Break End *
                </label>
                <input
                  id="lunchBreakEnd"
                  name="lunchBreakEnd"
                  type="time"
                  required
                  value={formData.lunchBreakEnd}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label htmlFor="annualLeave" className="block text-sm font-medium text-secondary-dark mb-1">
                  Annual Leave Days *
                </label>
                <input
                  id="annualLeave"
                  name="annualLeave"
                  type="number"
                  min="0"
                  required
                  value={formData.annualLeave}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="sickLeave" className="block text-sm font-medium text-secondary-dark mb-1">
                  Sick Leave Days *
                </label>
                <input
                  id="sickLeave"
                  name="sickLeave"
                  type="number"
                  min="0"
                  required
                  value={formData.sickLeave}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="personalLeave" className="block text-sm font-medium text-secondary-dark mb-1">
                  Personal Leave Days *
                </label>
                <input
                  id="personalLeave"
                  name="personalLeave"
                  type="number"
                  min="0"
                  required
                  value={formData.personalLeave}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Locations */}
        {currentStep === 3 && (
          <div className="space-y-6 animate-fade-in">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-secondary-dark">Locations</h2>
              <button
                type="button"
                onClick={addLocation}
                className="text-sm text-primary hover:text-primary-dark flex items-center"
              >
                <svg
                  className="h-5 w-5 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Location
              </button>
            </div>
            
            <div className="space-y-4">
              {formData.locations.map((location, index) => (
                <div key={index} className="border border-gray-200 rounded-md p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-md font-medium text-secondary-dark">Location {index + 1}</h3>
                    {formData.locations.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeLocation(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <svg
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-1">
                        Location Name *
                      </label>
                      <input
                        type="text"
                        required
                        value={location.name}
                        onChange={(e) => handleLocationChange(index, 'name', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-1">
                        Address *
                      </label>
                      <input
                        type="text"
                        required
                        value={location.address}
                        onChange={(e) => handleLocationChange(index, 'address', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Step 4: Departments */}
        {currentStep === 4 && (
          <div className="space-y-6 animate-fade-in">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-secondary-dark">Departments</h2>
              <button
                type="button"
                onClick={addDepartment}
                className="text-sm text-primary hover:text-primary-dark flex items-center"
              >
                <svg
                  className="h-5 w-5 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Department
              </button>
            </div>
            
            <div className="space-y-4">
              {formData.departments.map((department, index) => (
                <div key={index} className="border border-gray-200 rounded-md p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-md font-medium text-secondary-dark">Department {index + 1}</h3>
                    {formData.departments.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeDepartment(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <svg
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-1">
                        Department Name *
                      </label>
                      <input
                        type="text"
                        required
                        value={department.name}
                        onChange={(e) => handleDepartmentChange(index, 'name', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-secondary-dark mb-1">
                        Description
                      </label>
                      <textarea
                        rows={2}
                        value={department.description}
                        onChange={(e) => handleDepartmentChange(index, 'description', e.target.value)}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      ></textarea>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="mt-8 flex justify-between">
          {currentStep > 1 ? (
            <button
              type="button"
              onClick={prevStep}
              className="btn-outline py-2 px-6"
            >
              Previous
            </button>
          ) : (
            <div></div>
          )}
          
          {currentStep < 4 ? (
            <button
              type="button"
              onClick={nextStep}
              className="btn-primary py-2 px-6"
            >
              Next
            </button>
          ) : (
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary py-2 px-6 flex items-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Setting Up...
                </>
              ) : (
                'Complete Setup'
              )}
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default OnboardingForm;
