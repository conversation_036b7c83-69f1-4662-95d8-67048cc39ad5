import React from 'react';
import Link from 'next/link';

const Demo = () => {
  return (
    <section id="demo" className="section bg-white">
      <div className="container-custom">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-6">
            See KaziSync in Action
          </h2>
          <p className="text-lg text-secondary mb-8">
            Experience the power of KaziSync with our interactive demo and free trial.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="p-6 bg-background rounded-lg border border-gray-200">
              <div className="text-4xl mb-4">📽️</div>
              <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                Watch Demo Video
              </h3>
              <p className="text-secondary mb-4">
                See our product demo video showcasing key features and workflows.
              </p>
              <Link href="#" className="text-primary hover:text-primary-dark font-medium">
                Watch Now →
              </Link>
            </div>

            <div className="p-6 bg-background rounded-lg border border-gray-200">
              <div className="text-4xl mb-4">🧪</div>
              <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                Free 14-Day Trial
              </h3>
              <p className="text-secondary mb-4">
                Try the platform free for 14 days with full access to all features.
              </p>
              <Link href="/signup" className="text-primary hover:text-primary-dark font-medium">
                Start Trial →
              </Link>
            </div>

            <div className="p-6 bg-background rounded-lg border border-gray-200">
              <div className="text-4xl mb-4">📞</div>
              <h3 className="text-xl font-semibold text-secondary-dark mb-2">
                Personalized Walkthrough
              </h3>
              <p className="text-secondary mb-4">
                Book a personalized walkthrough with our team to see how KaziSync fits your needs.
              </p>
              <Link href="#contact" className="text-primary hover:text-primary-dark font-medium">
                Book Demo →
              </Link>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/signup" className="btn-primary px-8 py-3">
              Get Started
            </Link>
            <Link href="#contact" className="btn-outline px-8 py-3">
              Book Demo
            </Link>
            <Link href="#contact" className="btn-outline px-8 py-3">
              Contact Sales
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Demo;
