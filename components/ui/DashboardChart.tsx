"use client";

import React, { useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import { ChartData, ChartOptions, ChartType } from 'chart.js';

// Define our dataset configuration
interface DatasetConfig {
  label?: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  type?: string;
}

interface DashboardChartProps {
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'radar';
  labels: string[];
  datasets: DatasetConfig[];
  height?: string;
  loading?: boolean;
}

const DashboardChart: React.FC<DashboardChartProps> = ({
  type,
  labels,
  datasets,
  height = '100%',
  loading = false,
}) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);

  useEffect(() => {
    if (loading) return; // Don't create chart while loading

    if (chartRef.current) {
      // Destroy existing chart if it exists
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }

      // Create new chart
      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        // Create chart data with proper typing
        const chartData: ChartData<ChartType> = {
          labels,
          datasets: datasets as any, // Cast to any to bypass type checking
        };

        // Create chart options
        const chartOptions: ChartOptions = {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
            },
          },
        };

        chartInstance.current = new Chart(ctx, {
          type: type as ChartType,
          data: chartData,
          options: chartOptions,
        });
      }
    }

    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [type, labels, datasets, loading]);

  if (loading) {
    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading chart...</span>
      </div>
    );
  }

  return <canvas ref={chartRef} style={{ height }} />;
};

export default DashboardChart;