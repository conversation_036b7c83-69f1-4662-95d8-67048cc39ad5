"use client";

import React from 'react';

interface DashboardStatsProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  className?: string;
  loading?: boolean;
}

const DashboardStats: React.FC<DashboardStatsProps> = ({
  title,
  value,
  change,
  changeType,
  className = '',
  loading = false,
}) => {
  return (
    <div className={`bg-white rounded-xl border border-gray-200 p-6 hover:border-gray-300 transition-colors ${className}`}>
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-2 border-gray-200 border-t-blue-600"></div>
          <span className="ml-3 text-gray-600 text-sm font-medium">Loading...</span>
        </div>
      ) : (
        <>
          <p className="text-sm font-medium text-gray-600 mb-2">{title}</p>
          <div className="flex items-end justify-between">
            <p className="text-3xl font-bold text-gray-900">{value}</p>
            {change && (
              <div
                className={`flex items-center text-xs font-semibold px-2 py-1 rounded-full ${
                  changeType === 'positive'
                    ? 'text-green-700 bg-green-100'
                    : changeType === 'negative'
                    ? 'text-red-700 bg-red-100'
                    : 'text-gray-600 bg-gray-100'
                }`}
              >
                {changeType !== 'neutral' && (
                  <svg
                    className={`h-3 w-3 mr-1 ${changeType === 'negative' ? 'transform rotate-180' : ''}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 10l7-7m0 0l7 7m-7-7v18"
                    />
                  </svg>
                )}
                {change}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default DashboardStats;