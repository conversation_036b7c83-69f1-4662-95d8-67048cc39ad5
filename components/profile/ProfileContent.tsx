'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import Link from 'next/link';

const ProfileContent: React.FC = () => {
  const { user } = useAuth();
  // Extract first and last name from the full name
  const firstName = user?.name?.split(' ')[0] || '';
  const lastName = user?.name?.split(' ').slice(1).join(' ') || '';

  // Check if user is HR - they can edit their profile
  const isHRUser = user?.role === 'hr' || user?.role === 'admin' || user?.role === 'super-admin';

  // State for editing (only for HR users)
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: firstName,
    lastName: lastName,
    email: '', // We don't have email in the User interface yet
    phone: '', // We don't have phone in the User interface yet
    role: user?.role || '',
  });
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // This would be replaced with an actual API call
      // const { apiPut } = await import('@/lib/api');
      // const { getAccessToken } = await import('@/lib/auth');
      // const token = getAccessToken();

      // if (!token) {
      //   throw new Error('Authentication required');
      // }

      // const response = await apiPut(`api/users/${user?.id}`, {
      //   first_name: formData.firstName,
      //   last_name: formData.lastName,
      //   email: formData.email,
      //   phone: formData.phone,
      // }, {
      //   headers: {
      //     'Authorization': `Bearer ${token}`
      //   }
      // });

      // For now, just simulate a successful update
      setSuccessMessage('Profile updated successfully!');
      setIsEditing(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (error: any) {
      setErrorMessage(error.message || 'Failed to update profile');

      // Clear error message after 3 seconds
      setTimeout(() => {
        setErrorMessage('');
      }, 3000);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">My Profile</h1>
        {isHRUser ? (
          !isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="btn-primary py-1 px-3 text-sm"
            >
              Edit Profile
            </button>
          )
        ) : (
          <div className="text-sm text-gray-500">
            Contact HR to update your information
          </div>
        )}
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href={`/dashboard/${user?.role || 'hr'}`} className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Profile</span>
      </div>

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
          {successMessage}
        </div>
      )}

      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {errorMessage}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <DashboardCard title="Profile Picture">
            <div className="flex flex-col items-center py-6">
              <div className="h-32 w-32 rounded-full bg-primary text-white flex items-center justify-center text-4xl font-bold mb-4">
                {user?.name ? user.name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2) : 'JD'}
              </div>
              <p className="text-lg font-medium text-secondary-dark">{user?.name || 'John Doe'}</p>
              <p className="text-sm text-secondary">{user?.role === 'hr' ? 'HR Manager' : user?.role || 'Employee'}</p>
            </div>
          </DashboardCard>
        </div>

        <div className="md:col-span-2">
          <DashboardCard title="Personal Information">
            {isHRUser && isEditing ? (
              <form onSubmit={handleSubmit} className="space-y-4 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-secondary-dark mb-1">
                      First Name
                    </label>
                    <input
                      id="firstName"
                      name="firstName"
                      type="text"
                      value={formData.firstName}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-secondary-dark mb-1">
                      Last Name
                    </label>
                    <input
                      id="lastName"
                      name="lastName"
                      type="text"
                      value={formData.lastName}
                      onChange={handleChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-secondary-dark mb-1">
                    Email
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-secondary-dark mb-1">
                    Phone Number
                  </label>
                  <input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label htmlFor="role" className="block text-sm font-medium text-secondary-dark mb-1">
                    Role
                  </label>
                  <input
                    id="role"
                    name="role"
                    type="text"
                    value={formData.role === 'hr' ? 'HR Manager' : formData.role}
                    disabled
                    className="w-full px-4 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="btn-outline py-2 px-4"
                  >
                    Cancel
                  </button>

                  <button
                    type="submit"
                    className="btn-primary py-2 px-6"
                  >
                    Save Changes
                  </button>
                </div>
              </form>
            ) : (
              <div className="space-y-4 py-4">
                {!isHRUser && (
                  <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md text-sm mb-4">
                    <p className="font-medium">Information Update Policy</p>
                    <p className="mt-1">To update your personal information, please contact your HR department. This ensures data accuracy and compliance with company policies.</p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-secondary">First Name</p>
                    <p className="text-base text-secondary-dark">{formData.firstName || 'N/A'}</p>
                  </div>

                  <div>
                    <p className="text-sm text-secondary">Last Name</p>
                    <p className="text-base text-secondary-dark">{formData.lastName || 'N/A'}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-secondary">Email</p>
                  <p className="text-base text-secondary-dark">
                    {isHRUser ? (formData.email || 'Not provided') : 'Contact HR to view/update'}
                  </p>
                </div>

                <div>
                  <p className="text-sm text-secondary">Phone Number</p>
                  <p className="text-base text-secondary-dark">
                    {isHRUser ? (formData.phone || 'Not provided') : 'Contact HR to view/update'}
                  </p>
                </div>

                <div>
                  <p className="text-sm text-secondary">Role</p>
                  <p className="text-base text-secondary-dark">{formData.role === 'hr' ? 'HR Manager' : formData.role || 'N/A'}</p>
                </div>
              </div>
            )}
          </DashboardCard>
        </div>
      </div>

      <DashboardCard title="Account Settings">
        <div className="space-y-4 py-4">
          <div className="flex justify-between items-center border-b border-gray-100 pb-4">
            <div>
              <p className="text-base font-medium text-secondary-dark">Change Password</p>
              <p className="text-sm text-secondary">Update your password for enhanced security</p>
            </div>
            <button className="btn-outline py-1 px-3 text-sm">
              Change Password
            </button>
          </div>

          <div className="flex justify-between items-center border-b border-gray-100 pb-4">
            <div>
              <p className="text-base font-medium text-secondary-dark">Two-Factor Authentication</p>
              <p className="text-sm text-secondary">Add an extra layer of security to your account</p>
            </div>
            <button className="btn-outline py-1 px-3 text-sm">
              Enable
            </button>
          </div>

          <div className="flex justify-between items-center">
            <div>
              <p className="text-base font-medium text-secondary-dark">
                {isHRUser ? 'Account Management' : 'Account Support'}
              </p>
              <p className="text-sm text-secondary">
                {isHRUser
                  ? 'Manage your HR account settings and preferences'
                  : 'Contact HR for account-related assistance'
                }
              </p>
            </div>
            <button className="btn-outline py-1 px-3 text-sm">
              {isHRUser ? 'Manage Account' : 'Contact HR'}
            </button>
          </div>
        </div>
      </DashboardCard>
    </div>
  );
};

export default ProfileContent;
