'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';

const CompanyDisplay: React.FC = () => {
  const { companies } = useAuth();

  if (!companies || companies.length === 0) {
    return null;
  }

  // Display the company
  const company = companies[0];

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
      <h2 className="text-lg font-semibold text-secondary-dark mb-2">Company Information</h2>
      <div className="text-secondary">
        <p className="font-medium">{company.company_name}</p>
      </div>
    </div>
  );
};

export default CompanyDisplay;
