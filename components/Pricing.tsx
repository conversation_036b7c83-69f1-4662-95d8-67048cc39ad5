"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { SubscriptionPlan } from '@/types/subscription';
import { getSubscriptionPlans, formatFeaturesForDisplay, formatPrice, getPlanColor } from '@/lib/subscription';

const Pricing = () => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const fetchedPlans = await getSubscriptionPlans();
        // Sort plans by sort_order
        const sortedPlans = fetchedPlans
          .filter(plan => plan.is_active)
          .sort((a, b) => a.sort_order - b.sort_order);
        setPlans(sortedPlans);
      } catch (err) {
        setError('Failed to load pricing plans. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlans();
  }, []);

  // Determine which plan should be highlighted (middle plan or Professional)
  const getHighlightedPlan = (plans: SubscriptionPlan[]) => {
    if (plans.length === 0) return null;

    // Look for Professional plan first
    const professionalPlan = plans.find(plan =>
      plan.name.toLowerCase().includes('professional') ||
      plan.name.toLowerCase().includes('growth')
    );

    if (professionalPlan) return professionalPlan.plan_id;

    // Otherwise, highlight the middle plan
    const middleIndex = Math.floor(plans.length / 2);
    return plans[middleIndex]?.plan_id || null;
  };

  const highlightedPlanId = getHighlightedPlan(plans);

  return (
    <section id="pricing" className="section bg-white">
      <div className="container-custom">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-dark mb-4">
            Pricing That Grows with You
          </h2>
          <p className="text-secondary text-lg mb-8">
            We offer flexible pricing plans to fit every business size, with a minimum monthly fee + per-employee rate. All plans include core modules with 14-day free trial.
          </p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-16">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            <p className="mt-4 text-secondary">Loading pricing plans...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-16">
            <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-lg max-w-md mx-auto">
              <p className="font-medium">Unable to load pricing</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          </div>
        )}

        {/* Plans Grid */}
        {!isLoading && !error && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => {
              const isHighlighted = plan.plan_id === highlightedPlanId;
              const features = formatFeaturesForDisplay(plan.features);
              const planColor = getPlanColor(plan.name, plan.sort_order);

              return (
                <div
                  key={plan.plan_id}
                  className={`card overflow-hidden transition-all duration-300 ${
                    isHighlighted
                      ? 'border-2 border-primary relative transform hover:-translate-y-2'
                      : 'hover:border-gray-300'
                  }`}
                >
                  {isHighlighted && (
                    <div className="bg-primary text-white text-center py-1 text-sm font-medium">
                      Most Popular
                    </div>
                  )}
                  <div className="p-6 md:p-8">
                    <h3 className="text-2xl font-bold text-secondary-dark mb-2">{plan.name}</h3>
                    <p className="text-secondary mb-6">{plan.description}</p>
                    <div className="mb-6">
                      <div className="text-4xl font-bold text-secondary-dark">
                        {formatPrice(plan.flat_price)}<span className="text-lg">/mo</span>
                      </div>
                      <div className="text-secondary text-sm mt-1">
                        + {formatPrice(plan.price_per_employee)}/employee
                      </div>
                      {plan.max_employees && (
                        <div className="text-secondary text-xs mt-1">
                          Up to {plan.max_employees} employees
                        </div>
                      )}
                    </div>
                    <ul className="space-y-3 mb-8">
                      {features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <svg
                            className="h-5 w-5 text-success mt-0.5 mr-2 flex-shrink-0"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          <span className="text-secondary-dark text-sm">
                            {feature.name}
                            {feature.limit && (
                              <span className="text-gray-500"> ({feature.limit === null ? 'Unlimited' : feature.limit})</span>
                            )}
                          </span>
                        </li>
                      ))}
                    </ul>
                    <Link
                      href={plan.name.toLowerCase().includes('enterprise') ? '/contact' : '/signup'}
                      className={`block w-full text-center py-3 px-6 rounded-lg font-medium transition-all duration-200 ${
                        isHighlighted
                          ? 'bg-primary text-white hover:bg-primary-dark hover:text-white'
                          : 'bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white'
                      }`}
                    >
                      {plan.name.toLowerCase().includes('enterprise') ? 'Contact Sales' : 'Get Started'}
                    </Link>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        <div className="mt-16 text-center max-w-3xl mx-auto">
          <h3 className="text-xl font-semibold text-secondary-dark mb-4">
            Need a custom solution?
          </h3>
          <p className="text-secondary mb-6">
            Contact our sales team for a custom quote tailored to your organization's specific needs.
          </p>
          <Link href="#contact" className="btn-outline px-8 py-3">
            Contact Sales
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
