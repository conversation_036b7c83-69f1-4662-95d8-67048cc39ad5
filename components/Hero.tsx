import React from 'react';
import Link from 'next/link';

const Hero = () => {
  return (
    <section className="bg-gradient-to-b from-background to-background-dark py-16 md:py-24">
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="animate-slide-up">
            <h1 className="text-4xl md:text-5xl font-bold text-secondary-dark mb-6">
              KaziSync – <span className="text-primary">Work in Sync</span>
            </h1>
            <p className="text-lg text-secondary mb-8 max-w-lg">
              The modern HRMS built for teams of all sizes. From payroll to performance, KaziS<PERSON> helps you manage people, not paperwork.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/signup" className="btn-primary text-center px-8 py-3">
                Get Started
              </Link>
              {/* <Link href="#demo" className="btn-outline text-center px-8 py-3">
                Book Demo
              </Link> */}
              <Link href="#contact" className="btn-outline text-center px-8 py-3">
                Contact Sales
              </Link>
            </div>
            <div className="mt-8 flex items-center text-sm text-secondary">
              <svg
                className="h-5 w-5 mr-2 text-success"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span>Try the platform free for 14 days</span>
            </div>
          </div>
          <div className="relative animate-fade-in">
            <div className="bg-white rounded-2xl border border-gray-200 p-4 md:p-8 relative z-10">
              <div className="aspect-w-16 aspect-h-9 bg-background-dark rounded-lg overflow-hidden">
                {/* Dashboard Preview Image */}
                <img
                  src="/images/dashboard-preview.png"
                  alt="KaziSync Dashboard Preview"
                  className="w-full h-full object-cover rounded-lg"
                />
              </div>
            </div>
            {/* Decorative elements */}
            <div className="absolute -bottom-4 -right-4 w-64 h-64 bg-primary-light opacity-10 rounded-full z-0"></div>
            <div className="absolute -top-4 -left-4 w-32 h-32 bg-primary opacity-10 rounded-full z-0"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
