import React from 'react';
import Link from 'next/link';

interface ComingSoonProps {
  title: string;
  description: string;
  icon?: string;
  backLink?: string;
  backLinkText?: string;
}

const ComingSoon: React.FC<ComingSoonProps> = ({
  title,
  description,
  icon = '🚧',
  backLink = '/dashboard',
  backLinkText = 'Back to Dashboard'
}) => {
  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-4">
        <div className="mb-8">
          <div className="text-6xl mb-4">{icon}</div>
          <h1 className="text-3xl font-bold text-secondary-dark mb-4">{title}</h1>
          <p className="text-lg text-secondary mb-8">{description}</p>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <h3 className="text-lg font-semibold text-blue-900 mb-2">We're Working on It!</h3>
          <p className="text-blue-700 text-sm">
            This feature is currently under development. We're working hard to bring you the best experience possible.
          </p>
        </div>

        <div className="space-y-4">
          <Link
            href={backLink}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            {backLinkText}
          </Link>
          
          <div className="text-sm text-gray-500">
            <p>Need help? <Link href="/contact" className="text-primary hover:text-primary-dark">Contact our support team</Link></p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComingSoon;
