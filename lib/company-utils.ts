// lib/company-utils.ts

import { getCompanyCurrency, getCompanyTimezone, getCompanyDateFormat, getCompanyCountryName, getCompanyInfo } from './auth';

/**
 * Format currency value with company currency
 * @param amount The amount to format
 * @param showSymbol Whether to show currency symbol (default: true)
 * @returns Formatted currency string
 */
export function formatCompanyCurrency(amount: number, showSymbol: boolean = true): string {
  const currency = getCompanyCurrency() || 'USD';
  
  if (!showSymbol) {
    return amount.toLocaleString();
  }
  
  // Common currency symbols
  const currencySymbols: { [key: string]: string } = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'RWF': 'RWF',
    'KES': 'KES',
    'UGX': 'UGX',
    'TZS': 'TZS',
    'NGN': '₦',
    'ZAR': 'R',
    'GHS': '₵',
    'XOF': 'CFA',
    'XAF': 'FCFA',
    'MAD': 'MAD',
    'EGP': 'E£',
    'ETB': 'Br',
    'KSH': 'KSh',
    'BWP': 'P',
    'MUR': '₨',
    'SCR': '₨',
    'SZL': 'L',
    'LSL': 'L',
    'NAD': 'N$',
    'AOA': 'Kz',
    'MZN': 'MT',
    'ZMW': 'ZK',
    'ZWL': 'Z$'
  };
  
  const symbol = currencySymbols[currency] || currency;
  
  // For currencies that typically show symbol after amount
  const symbolAfterCurrencies = ['RWF', 'KES', 'UGX', 'TZS', 'MAD', 'ETB', 'KSH', 'BWP', 'SZL', 'LSL', 'AOA', 'MZN', 'ZMW', 'ZWL'];
  
  if (symbolAfterCurrencies.includes(currency)) {
    return `${amount.toLocaleString()} ${symbol}`;
  } else {
    return `${symbol}${amount.toLocaleString()}`;
  }
}

/**
 * Format date according to company date format
 * @param date The date to format
 * @returns Formatted date string
 */
export function formatCompanyDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const dateFormat = getCompanyDateFormat();
  
  // Default to ISO format if no company date format is set
  if (!dateFormat) {
    return dateObj.toISOString().split('T')[0];
  }
  
  // Handle common date formats
  switch (dateFormat.toLowerCase()) {
    case 'dd/mm/yyyy':
      return dateObj.toLocaleDateString('en-GB');
    case 'mm/dd/yyyy':
      return dateObj.toLocaleDateString('en-US');
    case 'yyyy-mm-dd':
      return dateObj.toISOString().split('T')[0];
    case 'dd-mm-yyyy':
      return dateObj.toLocaleDateString('en-GB').replace(/\//g, '-');
    case 'mm-dd-yyyy':
      return dateObj.toLocaleDateString('en-US').replace(/\//g, '-');
    default:
      return dateObj.toISOString().split('T')[0];
  }
}

/**
 * Format time according to company timezone
 * @param date The date/time to format
 * @param includeDate Whether to include date (default: false)
 * @returns Formatted time string
 */
export function formatCompanyTime(date: Date | string, includeDate: boolean = false): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const timezone = getCompanyTimezone();
  
  const options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
    timeZone: timezone || 'UTC'
  };
  
  if (includeDate) {
    options.year = 'numeric';
    options.month = '2-digit';
    options.day = '2-digit';
  }
  
  return dateObj.toLocaleString('en-US', options);
}

/**
 * Get company display information
 * @returns Object with formatted company information
 */
export function getCompanyDisplayInfo(): {
  currency: string;
  currencySymbol: string;
  timezone: string;
  dateFormat: string;
  countryName: string;
} {
  const info = getCompanyInfo();
  const currency = info?.currency || 'USD';
  
  // Get currency symbol
  const currencySymbols: { [key: string]: string } = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'RWF': 'RWF',
    'KES': 'KES',
    'UGX': 'UGX',
    'TZS': 'TZS',
    'NGN': '₦',
    'ZAR': 'R',
    'GHS': '₵',
    'XOF': 'CFA',
    'XAF': 'FCFA',
    'MAD': 'MAD',
    'EGP': 'E£',
    'ETB': 'Br',
    'KSH': 'KSh',
    'BWP': 'P',
    'MUR': '₨',
    'SCR': '₨',
    'SZL': 'L',
    'LSL': 'L',
    'NAD': 'N$',
    'AOA': 'Kz',
    'MZN': 'MT',
    'ZMW': 'ZK',
    'ZWL': 'Z$'
  };
  
  return {
    currency,
    currencySymbol: currencySymbols[currency] || currency,
    timezone: info?.timezone || 'UTC',
    dateFormat: info?.dateFormat || 'yyyy-mm-dd',
    countryName: info?.countryName || 'Unknown'
  };
}

/**
 * Validate if company data is available
 * @returns Boolean indicating if company data is properly loaded
 */
export function isCompanyDataAvailable(): boolean {
  const info = getCompanyInfo();
  return !!(info && info.companyId && info.countryCode);
}

/**
 * Get currency input placeholder based on company currency
 * @param baseAmount Optional base amount for placeholder
 * @returns Placeholder string for currency inputs
 */
export function getCurrencyPlaceholder(baseAmount?: number): string {
  const currency = getCompanyCurrency() || 'USD';
  const amount = baseAmount || 100000;
  
  // Adjust placeholder amounts based on currency
  const adjustedAmounts: { [key: string]: number } = {
    'USD': 5000,
    'EUR': 4500,
    'GBP': 4000,
    'RWF': 5000000,
    'KES': 500000,
    'UGX': 18000000,
    'TZS': 11500000,
    'NGN': 2000000,
    'ZAR': 75000,
    'GHS': 30000,
    'XOF': 3000000,
    'XAF': 3000000,
    'MAD': 50000,
    'EGP': 80000,
    'ETB': 150000,
    'KSH': 500000,
    'BWP': 55000,
    'MUR': 200000,
    'SCR': 70000,
    'SZL': 75000,
    'LSL': 75000,
    'NAD': 75000,
    'AOA': 2500000,
    'MZN': 320000,
    'ZMW': 100000,
    'ZWL': 360000
  };
  
  const placeholderAmount = adjustedAmounts[currency] || amount;
  return placeholderAmount.toLocaleString();
}
