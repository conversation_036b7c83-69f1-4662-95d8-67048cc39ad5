/**
 * Tests for working days utilities
 * These tests ensure null/undefined safety
 */

import {
  parseWorkingDays,
  formatWorkingDaysShort,
  formatWorkingDaysFull,
  getDayInfo,
  isWorkingDay,
  workingDaysToString,
  getWorkingDaysCount,
  isValidWorkingDaysFormat
} from '../working-days-utils';

describe('Working Days Utils', () => {
  describe('parseWorkingDays', () => {
    it('should handle null and undefined', () => {
      expect(parseWorkingDays(null)).toEqual([]);
      expect(parseWorkingDays(undefined)).toEqual([]);
    });

    it('should handle empty string', () => {
      expect(parseWorkingDays('')).toEqual([]);
      expect(parseWorkingDays('   ')).toEqual([]);
    });

    it('should parse valid working days', () => {
      expect(parseWorkingDays('1,2,3,4,5')).toEqual([1, 2, 3, 4, 5]);
      expect(parseWorkingDays('0,6')).toEqual([0, 6]);
    });

    it('should handle malformed strings', () => {
      expect(parseWorkingDays('1,2,invalid,4')).toEqual([1, 2, 4]);
      expect(parseWorkingDays('1,,2,3')).toEqual([1, 2, 3]);
      expect(parseWorkingDays(' 1 , 2 , 3 ')).toEqual([1, 2, 3]);
    });
  });

  describe('formatWorkingDaysShort', () => {
    it('should handle null and undefined', () => {
      expect(formatWorkingDaysShort(null)).toBe('No working days set');
      expect(formatWorkingDaysShort(undefined)).toBe('No working days set');
    });

    it('should format working days correctly', () => {
      expect(formatWorkingDaysShort('1,2,3,4,5')).toBe('Mon, Tue, Wed, Thu, Fri');
      expect(formatWorkingDaysShort('0,6')).toBe('Sun, Sat');
    });
  });

  describe('formatWorkingDaysFull', () => {
    it('should handle null and undefined', () => {
      expect(formatWorkingDaysFull(null)).toBe('No working days set');
      expect(formatWorkingDaysFull(undefined)).toBe('No working days set');
    });

    it('should format working days correctly', () => {
      expect(formatWorkingDaysFull('1,2,3,4,5')).toBe('Monday, Tuesday, Wednesday, Thursday, Friday');
    });
  });

  describe('isWorkingDay', () => {
    it('should handle null and undefined', () => {
      expect(isWorkingDay(null, 1)).toBe(false);
      expect(isWorkingDay(undefined, 1)).toBe(false);
    });

    it('should check working days correctly', () => {
      expect(isWorkingDay('1,2,3,4,5', 1)).toBe(true);
      expect(isWorkingDay('1,2,3,4,5', 0)).toBe(false);
      expect(isWorkingDay('1,2,3,4,5', 6)).toBe(false);
    });
  });

  describe('getWorkingDaysCount', () => {
    it('should handle null and undefined', () => {
      expect(getWorkingDaysCount(null)).toBe(0);
      expect(getWorkingDaysCount(undefined)).toBe(0);
    });

    it('should count working days correctly', () => {
      expect(getWorkingDaysCount('1,2,3,4,5')).toBe(5);
      expect(getWorkingDaysCount('0,6')).toBe(2);
    });
  });

  describe('isValidWorkingDaysFormat', () => {
    it('should handle null and undefined as valid', () => {
      expect(isValidWorkingDaysFormat(null)).toBe(true);
      expect(isValidWorkingDaysFormat(undefined)).toBe(true);
      expect(isValidWorkingDaysFormat('')).toBe(true);
    });

    it('should validate format correctly', () => {
      expect(isValidWorkingDaysFormat('1,2,3,4,5')).toBe(true);
      expect(isValidWorkingDaysFormat('invalid')).toBe(false);
    });
  });
});
