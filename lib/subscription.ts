// lib/subscription.ts

import { apiGet, apiPost, apiPatch, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  SubscriptionPlan,
  SubscriptionPlansResponse,
  SubscriptionPlanResponse,
  CreateSubscriptionPlanRequest,
  UpdateSubscriptionPlanRequest,
  FeatureDisplay,
  AssignSubscriptionRequest,
  AssignSubscriptionResponse
} from '@/types/subscription';

/**
 * Get all subscription plans (public endpoint - no auth required)
 */
export async function getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
  try {
    const response = await apiGet<SubscriptionPlansResponse>('api/subscription-plans');
    return response.plans || [];
  } catch (error) {
    throw new Error('Unable to load subscription plans. Please check your connection and try again.');
  }
}

/**
 * Get a specific subscription plan by ID (public endpoint - no auth required)
 */
export async function getSubscriptionPlan(planId: string): Promise<SubscriptionPlan> {
  try {
    const response = await apiGet<SubscriptionPlanResponse>(`api/subscription-plans/${planId}`);
    return response.plan;
  } catch (error) {
    throw new Error('Unable to load subscription plan details. Please try again.');
  }
}

/**
 * Create a new subscription plan (super admin only)
 */
export async function createSubscriptionPlan(data: CreateSubscriptionPlanRequest): Promise<SubscriptionPlan> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiPost<SubscriptionPlanResponse>('api/subscription-plans', data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.plan;
  } catch (error) {
    throw new Error('Unable to create subscription plan. Please check your input and try again.');
  }
}

/**
 * Update a subscription plan (super admin only)
 */
export async function updateSubscriptionPlan(planId: string, data: UpdateSubscriptionPlanRequest): Promise<SubscriptionPlan> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiPatch<SubscriptionPlanResponse>(`api/subscription-plans/${planId}`, data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.plan;
  } catch (error) {
    throw new Error('Unable to update subscription plan. Please check your input and try again.');
  }
}

/**
 * Delete a subscription plan (super admin only)
 */
export async function deleteSubscriptionPlan(planId: string): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    await apiDelete(`api/subscription-plans/${planId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
  } catch (error) {
    throw new Error('Unable to delete subscription plan. Please try again.');
  }
}

/**
 * Assign a subscription to a company (super admin only)
 */
export async function assignSubscriptionToCompany(data: AssignSubscriptionRequest): Promise<AssignSubscriptionResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiPost<AssignSubscriptionResponse>('api/subscriptions', data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response;
  } catch (error) {
    throw new Error('Unable to assign subscription to company. Please check your input and try again.');
  }
}

/**
 * Convert subscription plan features to display format
 */
export function formatFeaturesForDisplay(features: any): FeatureDisplay[] {
  const featureList: FeatureDisplay[] = [];

  // Basic features
  if (features.employee_self_service) {
    featureList.push({
      name: 'Employee Self Service',
      enabled: true,
      description: 'Employees can manage their own profiles and requests'
    });
  }

  if (features.attendance_management) {
    featureList.push({
      name: 'Attendance Management',
      enabled: true,
      description: 'Track and manage employee attendance'
    });
  }

  if (features.leave_management) {
    featureList.push({
      name: 'Leave Management',
      enabled: true,
      description: 'Manage employee leave requests and balances'
    });
  }

  if (features.basic_reports) {
    featureList.push({
      name: 'Basic Reports',
      enabled: true,
      description: 'Generate basic attendance and leave reports'
    });
  }

  if (features.advanced_reports) {
    featureList.push({
      name: 'Advanced Reports',
      enabled: true,
      description: 'Advanced analytics and detailed reporting'
    });
  }

  // AI Attendance Analytics
  if (features.ai_attendance_analytics?.enabled) {
    featureList.push({
      name: 'AI Attendance Analytics',
      enabled: true,
      limit: features.ai_attendance_analytics.monthly_limit,
      description: features.ai_attendance_analytics.monthly_limit 
        ? `AI-powered insights (${features.ai_attendance_analytics.monthly_limit}/month)`
        : 'Unlimited AI-powered attendance insights'
    });
  }

  // Announcement Management
  if (features.announcement_management?.enabled) {
    featureList.push({
      name: 'Announcement Management',
      enabled: true,
      limit: features.announcement_management.monthly_limit,
      description: features.announcement_management.monthly_limit 
        ? `Company announcements (${features.announcement_management.monthly_limit}/month)`
        : 'Unlimited company announcements'
    });
  }

  // Shift Management
  if (features.shift_management?.enabled) {
    featureList.push({
      name: 'Shift Management',
      enabled: true,
      limit: features.shift_management.shift_limit,
      description: features.shift_management.shift_limit 
        ? `Manage work shifts (up to ${features.shift_management.shift_limit} shifts)`
        : 'Unlimited shift management'
    });
  }

  // Support
  const supportLevels = {
    email: 'Email Support',
    email_chat: 'Email & Chat Support',
    priority: 'Priority Support'
  };

  featureList.push({
    name: supportLevels[features.support as keyof typeof supportLevels] || 'Support',
    enabled: true,
    description: 'Customer support access'
  });

  return featureList;
}

/**
 * Format price for display
 */
export function formatPrice(price: number): string {
  return `$${price.toFixed(2)}`;
}

/**
 * Get plan color based on name or sort order
 */
export function getPlanColor(_planName: string, sortOrder: number): string {
  const colors = ['green', 'blue', 'purple'];
  const colorIndex = (sortOrder - 1) % colors.length;
  return colors[colorIndex];
}
