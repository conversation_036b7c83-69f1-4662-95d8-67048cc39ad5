// Authentication utilities for KaziSync
import { createApiUrl } from './api';

interface LoginCredentials {
  username: string;
  password: string;
}

interface User {
  employee_id: string;
  employee_info: {
    created_at: string;
    department_id: string;
    email: string;
    employee_id: string;
    first_name: string;
    full_name: string;
    hire_date: string;
    id_number: string | null;
    last_name: string;
    phone_number: string | null;
    position: string | null;
    status: string;
    updated_at: string;
  };
  id: string;
  name: string;
  role: string;
  username: string;
}

export function isAuthenticated(): boolean {
  // Simple check if we have a user
  return !!getCurrentUser();
}

interface Company {
  company_id: string;
  company_name: string;
  database_name?: string;
  company_dict?: {
    company_id: string;
    company_name: string;
    company_tin: string;
    country: {
      code: string;
      currency: string;
      name: string;
    };
    country_code: string;
    country_currency: string;
    country_id: string;
    country_name: string;
    created_at: string;
    database_name: string;
    date_format: string | null;
    devices: any[];
    phone_number: string | null;
    time_zone: string;
  };
}

interface UserResponse {
  success: boolean;
  user: {
    companies: Company[];
    company_names: string[];
    created_at: string;
    email: string;
    first_name: string;
    full_name: string;
    last_name: string;
    phone_number: string;
    role: string;
    user_id: string;
    username: string;
  };
}

interface AuthResponse {
  access_token: string;
  authenticated: boolean;
  companies?: Company[];  // For HR users
  company?: Company;      // For employee users
  refresh_token: string;
  user: User;
}

/**
 * Authenticate a user with the API
 * @param credentials User credentials (username and password)
 * @returns Authentication response with tokens and user info
 */
export async function login(credentials: LoginCredentials): Promise<AuthResponse> {
  try {
    // Make the API call using our utility function
    const url = createApiUrl('login');
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    // Clone the response to read it twice (once for logging, once for processing)
    const responseClone = response.clone();
    const responseText = await responseClone.text();

    if (!response.ok) {
      // Try to extract error message from response
      let errorMessage = 'Authentication failed';
      try {
        if (responseText) {
          const errorData = JSON.parse(responseText);
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        }
      } catch (parseError) {
        // If we can't parse the error response, use fallback based on status
        switch (response.status) {
          case 401:
            errorMessage = 'Invalid credentials. Please check your username and password.';
            break;
          case 403:
            errorMessage = 'Access denied. Your account may be disabled.';
            break;
          case 429:
            errorMessage = 'Too many login attempts. Please try again later.';
            break;
          case 500:
            errorMessage = 'Server error. Please try again later.';
            break;
          default:
            errorMessage = `Authentication failed: ${response.status} ${response.statusText}`;
        }
      }
      throw new Error(errorMessage);
    }

    // Parse the response text instead of calling response.json() again
    let data: AuthResponse = {
      access_token: '',
      authenticated: false,
      refresh_token: '',
      user: {
        employee_id: '',
        employee_info: {
          created_at: '',
          department_id: '',
          email: '',
          employee_id: '',
          first_name: '',
          full_name: '',
          hire_date: '',
          id_number: null,
          last_name: '',
          phone_number: null,
          position: null,
          status: '',
          updated_at: ''
        },
        id: '',
        name: '',
        role: '',
        username: ''
      }
    };

    try {
      data = responseText ? JSON.parse(responseText) : data;
    } catch (parseError) {
      throw new Error('Invalid response format from server');
    }

    // Store auth data in localStorage
    if (data.authenticated) {
      // Make sure we have a valid user object before storing
      if (!data.user || !data.user.id || !data.user.role) {
        throw new Error('Invalid user data received from server');
      }

      // Store the data in localStorage
      localStorage.setItem('kazisync_auth', JSON.stringify(data));
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Get the current authenticated user from localStorage
 * @returns User object or null if not authenticated
 */
export function getCurrentUser(): User | null {
  if (typeof window === 'undefined') {
    return null; // Return null during SSR
  }

  // Try to get auth data from localStorage
  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) return null;

  try {
    const parsedData = JSON.parse(authData);

    if (!parsedData.user) {
      return null;
    }

    // Validate that the user object has the required fields
    const user = parsedData.user;
    if (!user.id || !user.role) {
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error parsing auth data:', error);
    return null;
  }
}

/**
 * Get the companies for the current authenticated user
 * @returns Array of Company objects or empty array if not authenticated
 */
export function getCurrentUserCompanies(): Company[] {
  if (typeof window === 'undefined') {
    return []; // Return empty array during SSR
  }

  // Try to get auth data from localStorage
  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) return [];

  try {
    const parsedData = JSON.parse(authData);

    // Check if companies array exists (HR users)
    if (parsedData.companies && Array.isArray(parsedData.companies)) {
      return parsedData.companies;
    }

    // Check if single company object exists (employee users)
    if (parsedData.company && parsedData.company.company_id) {
      return [parsedData.company];
    }

    return [];
  } catch (error) {
    return [];
  }
}

/**
 * Get the country code from the first company
 * @returns Country code string or null if not available
 */
export function getCompanyCountryCode(): string | null {
  const companies = getCurrentUserCompanies();

  if (companies.length === 0) {
    return null;
  }

  const company = companies[0];

  // Try to get country code from company_dict first (more detailed structure)
  if (company.company_dict?.country_code) {
    return company.company_dict.country_code;
  }

  // Fallback to direct country_code if available
  if ((company as any).country_code) {
    return (company as any).country_code;
  }

  return null;
}

/**
 * Get the currency from the first company
 * @returns Currency string or null if not available
 */
export function getCompanyCurrency(): string | null {
  const companies = getCurrentUserCompanies();
  if (companies.length === 0) return null;

  const company = companies[0];

  // Try to get currency from company_dict first (more detailed structure)
  if (company.company_dict?.country_currency) {
    return company.company_dict.country_currency;
  }

  // Try alternative currency field
  if (company.company_dict?.country?.currency) {
    return company.company_dict.country.currency;
  }

  // Fallback to direct currency if available
  if ((company as any).currency) {
    return (company as any).currency;
  }

  return null;
}

/**
 * Get the timezone from the first company
 * @returns Timezone string or null if not available
 */
export function getCompanyTimezone(): string | null {
  const companies = getCurrentUserCompanies();
  if (companies.length === 0) return null;

  const company = companies[0];

  // Try to get timezone from company_dict first (more detailed structure)
  if (company.company_dict?.time_zone) {
    return company.company_dict.time_zone;
  }

  // Fallback to direct timezone if available
  if ((company as any).time_zone) {
    return (company as any).time_zone;
  }

  return null;
}

/**
 * Get the date format from the first company
 * @returns Date format string or null if not available
 */
export function getCompanyDateFormat(): string | null {
  const companies = getCurrentUserCompanies();
  if (companies.length === 0) return null;

  const company = companies[0];

  // Try to get date format from company_dict first (more detailed structure)
  if (company.company_dict?.date_format) {
    return company.company_dict.date_format;
  }

  // Fallback to direct date_format if available
  if ((company as any).date_format) {
    return (company as any).date_format;
  }

  return null;
}

/**
 * Get the country name from the first company
 * @returns Country name string or null if not available
 */
export function getCompanyCountryName(): string | null {
  const companies = getCurrentUserCompanies();
  if (companies.length === 0) return null;

  const company = companies[0];

  // Try to get country name from company_dict first (more detailed structure)
  if (company.company_dict?.country_name) {
    return company.company_dict.country_name;
  }

  // Try alternative country name field
  if (company.company_dict?.country?.name) {
    return company.company_dict.country.name;
  }

  // Fallback to direct country_name if available
  if ((company as any).country_name) {
    return (company as any).country_name;
  }

  return null;
}

/**
 * Get complete company information from the first company
 * @returns Company information object or null if not available
 */
export function getCompanyInfo(): {
  countryCode: string | null;
  currency: string | null;
  timezone: string | null;
  dateFormat: string | null;
  countryName: string | null;
  companyId: string | null;
  companyName: string | null;
} | null {
  const companies = getCurrentUserCompanies();
  if (companies.length === 0) return null;

  const company = companies[0];

  return {
    countryCode: getCompanyCountryCode(),
    currency: getCompanyCurrency(),
    timezone: getCompanyTimezone(),
    dateFormat: getCompanyDateFormat(),
    countryName: getCompanyCountryName(),
    companyId: company.company_id,
    companyName: company.company_name
  };
}

/**
 * Get the stored access token
 * @returns Access token string or null if not authenticated
 */
export function getAccessToken(): string | null {
  if (typeof window === 'undefined') {
    return null; // Return null during SSR
  }

  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) return null;

  try {
    const parsedData = JSON.parse(authData);
    return parsedData.access_token || null;
  } catch (error) {
    return null;
  }
}

/**
 * Log out the current user
 */
export function logout(): void {
  // Clear auth data from localStorage
  localStorage.removeItem('kazisync_auth');
}

/**
 * Get the dashboard path based on user role
 * @param role User role
 * @returns Dashboard path
 */
// Create a mapping object for faster lookups
const ROLE_DASHBOARD_MAP: Record<string, string> = {
  'super-admin': '/dashboard/super-admin',
  'company-admin': '/dashboard/admin',
  'admin': '/dashboard/admin',
  'hr-manager': '/dashboard/hr',
  'hr': '/dashboard/hr',
  'manager': '/dashboard/manager',
  'employee': '/dashboard/employee',
  'user': '/dashboard/employee' // Map 'user' role to employee dashboard
};

export function getDashboardPathByRole(role: string): string {
  if (!role) {
    return '/dashboard';
  }

  // Convert to lowercase and normalize spaces to hyphens for consistent lookup
  const normalizedRole = role.toLowerCase().replace(/\s+/g, '-');

  // Use the map for direct lookup instead of switch statement
  const dashboardPath = ROLE_DASHBOARD_MAP[normalizedRole];

  if (!dashboardPath) {
    return '/dashboard';
  }

  return dashboardPath;
}

/**
 * Fetch complete company data and update localStorage
 * @param companyId The company ID
 * @returns Complete company data
 */
export async function fetchCompleteCompanyData(companyId: string): Promise<any> {


  // Import apiGet dynamically to avoid circular dependencies
  const { apiGet } = await import('./api');
  const token = getAccessToken();

  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // Try to fetch company details - this might be a different endpoint
    // Let's try a few possible endpoints
    let response;

    try {
      // Try the user endpoint first as it might include complete company data
      const currentUser = getCurrentUser();
      if (currentUser?.id) {

        response = await apiGet<UserResponse>(`get_user/${currentUser.id}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.success && response.user?.companies) {

          updateStoredCompanyData(response.user.companies);
          return response.user.companies;
        }
      }
    } catch (error) {

    }

    // If user endpoint doesn't work, try direct company endpoint
    try {

      response = await apiGet<any>(`api/companies/${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response && response.company) {

        updateStoredCompanyData([response.company]);
        return [response.company];
      }
    } catch (error) {

    }

    throw new Error('Unable to fetch complete company data from any endpoint');

  } catch (error) {

    throw error;
  }
}

/**
 * Update stored company data in localStorage
 * @param companies Array of companies with complete data
 */
function updateStoredCompanyData(companies: any[]): void {
  try {
    const authData = localStorage.getItem('kazisync_auth');
    if (authData) {
      const parsedData = JSON.parse(authData);

      // Check if existing companies have company_dict and new companies don't
      // If so, preserve the existing company_dict data
      if (parsedData.companies && Array.isArray(parsedData.companies)) {
        const updatedCompanies = companies.map((newCompany: any) => {
          const existingCompany = parsedData.companies.find((existing: any) =>
            existing.company_id === newCompany.company_id
          );

          // If existing company has company_dict but new one doesn't, preserve the existing one
          if (existingCompany?.company_dict && !newCompany.company_dict) {
            return {
              ...newCompany,
              company_dict: existingCompany.company_dict
            };
          }

          return newCompany;
        });

        parsedData.companies = updatedCompanies;
      } else {
        parsedData.companies = companies;
      }

      localStorage.setItem('kazisync_auth', JSON.stringify(parsedData));
    }
  } catch (error) {
    // Silently handle errors
  }
}

/**
 * Fetch user data from the API
 * @param userId The user ID
 * @returns User data including company information
 */
export async function fetchUserData(userId: string): Promise<UserResponse> {
  // Import apiGet dynamically to avoid circular dependencies
  const { apiGet } = await import('./api');
  const token = getAccessToken();

  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiGet<UserResponse>(`get_user/${userId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    // If successful, update the local storage with the latest company data
    if (response.success && response.user) {
      updateStoredCompanyData(response.user.companies);
    }

    return response;
  } catch (error) {
    throw error;
  }
}
