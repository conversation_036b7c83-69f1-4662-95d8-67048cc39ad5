/**
 * Utility functions for handling working days safely
 * Prevents null/undefined errors when working with working days strings
 */

export interface WorkingDayMap {
  [key: string]: string;
}

export interface DayInfo {
  name: string;
  active: boolean;
}

/**
 * Safely parse working days string into an array of numbers
 * @param workingDays - Comma-separated string of day numbers (e.g., "1,2,3,4,5")
 * @returns Array of valid day numbers
 */
export function parseWorkingDays(workingDays: string | null | undefined): number[] {
  if (!workingDays || workingDays.trim() === '') {
    return [];
  }

  try {
    return workingDays
      .split(',')
      .map(day => day.trim())
      .filter(day => day !== '')
      .map(Number)
      .filter(day => !isNaN(day) && day >= 0 && day <= 6); // Valid day range
  } catch (error) {
    console.warn('Error parsing working days:', error);
    return [];
  }
}

/**
 * Format working days for display with short day names
 * @param workingDays - Comma-separated string of day numbers
 * @returns Formatted string (e.g., "<PERSON>, <PERSON><PERSON>, Wed")
 */
export function formatWorkingDaysShort(workingDays: string | null | undefined): string {
  const days = parseWorkingDays(workingDays);
  
  if (days.length === 0) {
    return 'No working days set';
  }

  const dayMap: WorkingDayMap = {
    '0': 'Sun',
    '1': 'Mon',
    '2': 'Tue',
    '3': 'Wed',
    '4': 'Thu',
    '5': 'Fri',
    '6': 'Sat'
  };

  return days
    .map(day => dayMap[day.toString()] || `Day ${day}`)
    .join(', ');
}

/**
 * Format working days for display with full day names
 * @param workingDays - Comma-separated string of day numbers
 * @returns Formatted string (e.g., "Monday, Tuesday, Wednesday")
 */
export function formatWorkingDaysFull(workingDays: string | null | undefined): string {
  const days = parseWorkingDays(workingDays);
  
  if (days.length === 0) {
    return 'No working days set';
  }

  const dayMap: WorkingDayMap = {
    '0': 'Sunday',
    '1': 'Monday',
    '2': 'Tuesday',
    '3': 'Wednesday',
    '4': 'Thursday',
    '5': 'Friday',
    '6': 'Saturday'
  };

  return days
    .map(day => dayMap[day.toString()] || `Day ${day}`)
    .join(', ');
}

/**
 * Get day information for UI display (e.g., checkboxes)
 * @param workingDays - Comma-separated string of day numbers
 * @returns Array of day info objects
 */
export function getDayInfo(workingDays: string | null | undefined): DayInfo[] {
  const activeDays = parseWorkingDays(workingDays);
  const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  return dayNames.map((name, index) => ({
    name,
    active: activeDays.includes(index + 1) || (index === 6 && activeDays.includes(0)) // Handle Sunday (0 or 7)
  }));
}

/**
 * Check if a specific day is a working day
 * @param workingDays - Comma-separated string of day numbers
 * @param dayOfWeek - Day of week (0 = Sunday, 1 = Monday, etc.)
 * @returns Boolean indicating if the day is a working day
 */
export function isWorkingDay(workingDays: string | null | undefined, dayOfWeek: number): boolean {
  const days = parseWorkingDays(workingDays);
  return days.includes(dayOfWeek);
}

/**
 * Convert working days array back to string format
 * @param days - Array of day numbers
 * @returns Comma-separated string
 */
export function workingDaysToString(days: number[]): string {
  return days
    .filter(day => !isNaN(day) && day >= 0 && day <= 6)
    .sort((a, b) => a - b)
    .join(',');
}

/**
 * Get working days count
 * @param workingDays - Comma-separated string of day numbers
 * @returns Number of working days
 */
export function getWorkingDaysCount(workingDays: string | null | undefined): number {
  return parseWorkingDays(workingDays).length;
}

/**
 * Validate working days string format
 * @param workingDays - Comma-separated string of day numbers
 * @returns Boolean indicating if format is valid
 */
export function isValidWorkingDaysFormat(workingDays: string | null | undefined): boolean {
  if (!workingDays || workingDays.trim() === '') {
    return true; // Empty is considered valid
  }

  try {
    const days = parseWorkingDays(workingDays);
    return days.length > 0; // Must have at least one valid day
  } catch (error) {
    return false;
  }
}
