// lib/payroll.ts

import { apiGet, apiPost, apiPatch, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  PayrollPolicyType,
  CreatePayrollPolicyTypeRequest,
  UpdatePayrollPolicyTypeRequest,
  PayrollPolicyTypesResponse,
  PayrollPolicyTypeResponse,
  PolicyTypeCountriesResponse,
  Country,
  CreateCountryRequest,
  UpdateCountryRequest,
  CountriesResponse,
  CountryResponse,
  TaxPolicy,
  CreateTaxPolicyRequest,
  TaxPolicyResponse,
  TaxPoliciesResponse,
  TaxBracket,
  TaxBracketsRequest,
  TaxBracketsResponse,
  EmployeeType,
  CreateEmployeeTypeRequest,
  EmployeeTypeResponse,
  EmployeeTypesResponse,
  DeductionType,
  CreateDeductionTypeRequest,
  DeductionTypeResponse,
  DeductionTypesResponse,
  DeductionPolicy,
  CreateDeductionPolicyRequest,
  DeductionPolicyResponse,
  DeductionPoliciesResponse
} from '@/types/payroll';

/**
 * Create a new payroll policy type
 */
export async function createPayrollPolicyType(data: CreatePayrollPolicyTypeRequest): Promise<PayrollPolicyType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<PayrollPolicyTypeResponse>('api/payroll/policy-types', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.policy_type;
}

/**
 * Get all payroll policy types
 */
export async function getPayrollPolicyTypes(): Promise<{ policyTypes: PayrollPolicyType[]; totalCount: number }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<PayrollPolicyTypesResponse>('api/payroll/policy-types', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return {
    policyTypes: response.policy_types,
    totalCount: response.total_count
  };
}

/**
 * Get a single payroll policy type by ID
 */
export async function getPayrollPolicyType(policyTypeId: string): Promise<PayrollPolicyType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<PayrollPolicyTypeResponse>(
    `api/payroll/policy-types/${policyTypeId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response.policy_type;
}

/**
 * Update a payroll policy type
 */
export async function updatePayrollPolicyType(
  policyTypeId: string,
  data: UpdatePayrollPolicyTypeRequest
): Promise<PayrollPolicyType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPatch<PayrollPolicyTypeResponse>(
    `api/payroll/policy-types/${policyTypeId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.policy_type;
}

/**
 * Delete a payroll policy type
 */
export async function deletePayrollPolicyType(policyTypeId: string): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiDelete(`api/payroll/policy-types/${policyTypeId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}

/**
 * Get countries using a specific policy type
 */
export async function getPolicyTypeCountries(policyTypeId: string): Promise<PolicyTypeCountriesResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<PolicyTypeCountriesResponse>(
    `api/payroll/policy-types/${policyTypeId}/countries`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response;
}

// Countries API Functions

/**
 * Create a new country
 */
export async function createCountry(data: CreateCountryRequest): Promise<Country> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<CountryResponse>('api/countries', data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.country;
}

/**
 * Get all countries
 */
export async function getCountries(): Promise<{ countries: Country[]; totalCount: number }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<CountriesResponse>('api/countries', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return {
    countries: response.countries,
    totalCount: response.total_count
  };
}

/**
 * Get a single country by ID
 */
export async function getCountry(countryId: string): Promise<Country> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<CountryResponse>(`api/countries/${countryId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  return response.country;
}

/**
 * Update a country
 */
export async function updateCountry(
  countryId: string,
  data: UpdateCountryRequest
): Promise<Country> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPatch<CountryResponse>(`api/countries/${countryId}`, data, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  return response.country;
}

/**
 * Delete a country
 */
export async function deleteCountry(countryId: string): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiDelete(`api/countries/${countryId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}

// Tax Policies API Functions

/**
 * Create a tax policy for a specific country
 */
export async function createTaxPolicy(countryCode: string, data: CreateTaxPolicyRequest): Promise<TaxPolicy> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<TaxPolicyResponse>(
    `api/countries/${countryCode}/tax-policies`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.policy;
}

/**
 * Get all tax policies for a specific country
 */
export async function getTaxPolicies(countryCode: string): Promise<{ policies: TaxPolicy[]; totalCount: number; country: { code: string; name: string } }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<TaxPoliciesResponse>(
    `api/countries/${countryCode}/tax-policies`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return {
    policies: response.policies,
    totalCount: response.total_count,
    country: response.country
  };
}

/**
 * Create tax brackets for a policy
 */
export async function createTaxBrackets(policyId: string, data: TaxBracketsRequest): Promise<TaxBracket[]> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<TaxBracketsResponse>(
    `api/payroll/policies/${policyId}/tax-brackets`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.tax_brackets;
}

// Employee Types API Functions

/**
 * Create an employee type for a specific country
 */
export async function createEmployeeType(countryCode: string, data: CreateEmployeeTypeRequest): Promise<EmployeeType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<EmployeeTypeResponse>(
    `api/countries/${countryCode}/employee-types`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.employee_type;
}

/**
 * Get all employee types for a specific country
 */
export async function getEmployeeTypes(countryCode: string): Promise<{ employeeTypes: EmployeeType[]; totalCount: number; country: { code: string; name: string } }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<EmployeeTypesResponse>(
    `api/countries/${countryCode}/employee-types`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return {
    employeeTypes: response.employee_types,
    totalCount: response.total_count || response.employee_types.length,
    country: response.country
  };
}

/**
 * Update an employee type
 */
export async function updateEmployeeType(
  countryCode: string,
  employeeTypeId: string,
  data: Partial<CreateEmployeeTypeRequest>
): Promise<EmployeeType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPatch<EmployeeTypeResponse>(
    `api/countries/${countryCode}/employee-types/${employeeTypeId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.employee_type;
}

/**
 * Delete an employee type
 */
export async function deleteEmployeeType(countryCode: string, employeeTypeId: string): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiDelete(`api/countries/${countryCode}/employee-types/${employeeTypeId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}

// Deduction Types API Functions

/**
 * Create a deduction type for a specific country
 */
export async function createDeductionType(countryCode: string, data: CreateDeductionTypeRequest): Promise<DeductionType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<DeductionTypeResponse>(
    `api/countries/${countryCode}/deduction-types`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.deduction_type;
}

/**
 * Get all deduction types for a specific country
 */
export async function getDeductionTypes(countryCode: string): Promise<{ deductionTypes: DeductionType[]; totalCount: number; country: { code: string; name: string } }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<DeductionTypesResponse>(
    `api/countries/${countryCode}/deduction-types`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return {
    deductionTypes: response.deduction_types,
    totalCount: response.total_count || response.deduction_types.length,
    country: response.country
  };
}

/**
 * Update a deduction type
 */
export async function updateDeductionType(
  countryCode: string,
  deductionTypeId: string,
  data: Partial<CreateDeductionTypeRequest>
): Promise<DeductionType> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPatch<DeductionTypeResponse>(
    `api/countries/${countryCode}/deduction-types/${deductionTypeId}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.deduction_type;
}

/**
 * Delete a deduction type
 */
export async function deleteDeductionType(countryCode: string, deductionTypeId: string): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  await apiDelete(`api/countries/${countryCode}/deduction-types/${deductionTypeId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
}

// Deduction Policies API Functions

/**
 * Create a deduction policy for a specific country
 */
export async function createDeductionPolicy(countryCode: string, data: CreateDeductionPolicyRequest): Promise<DeductionPolicy> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<DeductionPolicyResponse>(
    `api/countries/${countryCode}/deduction-policies`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.deduction_policy;
}

/**
 * Get all deduction policies for a specific country
 */
export async function getDeductionPolicies(countryCode: string): Promise<{ deductionPolicies: DeductionPolicy[]; totalCount: number; country: { code: string; name: string } }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<DeductionPoliciesResponse>(
    `api/countries/${countryCode}/deduction-policies`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return {
    deductionPolicies: response.deduction_policies,
    totalCount: response.total_count || response.deduction_policies.length,
    country: response.country
  };
}
