// lib/ai-analytics.ts

import { apiGet } from './api';
import { getAccessToken } from './auth';
import { AIAnalyticsResponse, AIAnalyticsUsage } from '@/types/subscription';

export type AnalyticsPeriod = 'daily' | 'weekly' | 'monthly' | 'annual';

/**
 * Check if AI Analytics feature is enabled for the current subscription
 * TODO: Replace with actual subscription API when available
 */
export function checkAIAnalyticsAccess(): { enabled: boolean; monthly_limit: number | null } {
  // Mock subscription check - replace with actual API call
  // For now, assume all users have access with a limit of 10 requests per month
  return {
    enabled: true,
    monthly_limit: 10
  };
}

/**
 * Get AI Analytics usage for the current month
 * TODO: Implement actual usage tracking API when available
 */
export async function getAIAnalyticsUsage(): Promise<AIAnalyticsUsage> {
  // Mock usage data - replace with actual API call
  const mockUsage: AIAnalyticsUsage = {
    current_month_usage: 3,
    monthly_limit: 10,
    remaining_requests: 7,
    reset_date: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString()
  };
  
  return mockUsage;
}

/**
 * Fetch AI Analytics data for daily attendance
 */
export async function fetchDailyAIAnalytics(companyId: string): Promise<AIAnalyticsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  // Check subscription access
  const access = checkAIAnalyticsAccess();
  if (!access.enabled) {
    throw new Error('AI Analytics feature is not available in your current subscription plan');
  }

  // Check usage limits
  const usage = await getAIAnalyticsUsage();
  if (access.monthly_limit && usage.current_month_usage >= access.monthly_limit) {
    throw new Error(`Monthly AI Analytics limit reached (${access.monthly_limit} requests). Limit resets on ${new Date(usage.reset_date).toLocaleDateString()}`);
  }

  try {
    const response = await apiGet<AIAnalyticsResponse>(
      `api/attendance/daily?company_id=${companyId}&include_ai_insights=true`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    return response;
  } catch (error: any) {
    console.error('Error fetching daily AI analytics:', error);
    throw new Error(error.message || 'Failed to fetch AI analytics data');
  }
}

/**
 * Fetch AI Analytics data for weekly, monthly, or annual periods
 */
export async function fetchPeriodAIAnalytics(
  companyId: string, 
  period: 'weekly' | 'monthly' | 'annual'
): Promise<AIAnalyticsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  // Check subscription access
  const access = checkAIAnalyticsAccess();
  if (!access.enabled) {
    throw new Error('AI Analytics feature is not available in your current subscription plan');
  }

  // Check usage limits
  const usage = await getAIAnalyticsUsage();
  if (access.monthly_limit && usage.current_month_usage >= access.monthly_limit) {
    throw new Error(`Monthly AI Analytics limit reached (${access.monthly_limit} requests). Limit resets on ${new Date(usage.reset_date).toLocaleDateString()}`);
  }

  try {
    const response = await apiGet<AIAnalyticsResponse>(
      `api/attendance/statistics?company_id=${companyId}&period=${period}&include_ai_insights=true`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    return response;
  } catch (error: any) {
    console.error(`Error fetching ${period} AI analytics:`, error);
    throw new Error(error.message || 'Failed to fetch AI analytics data');
  }
}

/**
 * Fetch AI Analytics data based on selected period
 */
export async function fetchAIAnalytics(
  companyId: string, 
  period: AnalyticsPeriod
): Promise<AIAnalyticsResponse> {
  if (period === 'daily') {
    return fetchDailyAIAnalytics(companyId);
  } else {
    return fetchPeriodAIAnalytics(companyId, period);
  }
}

/**
 * Format AI insights content for display
 */
export function formatAIInsightsContent(content: string): string {
  // Remove markdown headers and format for better display
  return content
    .replace(/#{1,6}\s/g, '') // Remove markdown headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markdown
    .replace(/\*(.*?)\*/g, '$1') // Remove italic markdown
    .trim();
}

/**
 * Get priority color class based on AI insights priority
 */
export function getPriorityColorClass(priority: string): string {
  switch (priority.toLowerCase()) {
    case 'urgent':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'high':
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case 'medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'low':
    default:
      return 'text-blue-600 bg-blue-50 border-blue-200';
  }
}

/**
 * Extract key metrics from AI insights content
 */
export function extractKeyMetrics(content: string): { [key: string]: string } {
  const metrics: { [key: string]: string } = {};
  
  // Extract attendance rate
  const attendanceMatch = content.match(/attendance rate of ([\d.]+)%/i);
  if (attendanceMatch) {
    metrics['Attendance Rate'] = `${attendanceMatch[1]}%`;
  }
  
  // Extract absence rate
  const absenceMatch = content.match(/absence rate.*?([\d.]+)%/i);
  if (absenceMatch) {
    metrics['Absence Rate'] = `${absenceMatch[1]}%`;
  }
  
  // Extract productivity impact
  const productivityMatch = content.match(/productivity.*?loss.*?([\d,]+)/i);
  if (productivityMatch) {
    metrics['Productivity Impact'] = productivityMatch[1];
  }
  
  return metrics;
}
