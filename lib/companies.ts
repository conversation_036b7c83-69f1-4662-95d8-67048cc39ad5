import { apiGet, apiPost } from './api';
import { getAccessToken } from './auth';

export interface Company {
  company_id: string;
  company_name: string;
  company_tin: string | null;
  created_at: string;
  database_name: string;
  devices: Device[];
  phone_number: string | null;
}

export interface Device {
  device_id: string;
  device_name: string;
  device_type: string;
  status: string;
  created_at: string;
}

export interface CompaniesResponse {
  companies: Company[];
}

export interface AddDeviceRequest {
  company_id: string;
  device_name: string;
  device_type: string;
}

export interface AddDeviceResponse {
  success: boolean;
  message: string;
  device?: Device;
}

/**
 * Get all companies
 */
export async function getCompanies(): Promise<CompaniesResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<CompaniesResponse>('get_companies', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response;
}

/**
 * Add a device to a company
 */
export async function addCompanyDevice(data: AddDeviceRequest): Promise<AddDeviceResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<AddDeviceResponse>('add_company_device', data, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response;
}
