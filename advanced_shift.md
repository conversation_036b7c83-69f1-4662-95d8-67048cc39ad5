get scheduling template examples:https://sms.remmittance.com/api/scheduling/examples/business-requirements
sample response: {
    "examples": {
        "hospital_icu": {
            "business_requirements": {
                "coverage_rules": {
                    "allow_understaffing": false,
                    "emergency_coverage_plan": "mandatory_overtime",
                    "minimum_coverage_percentage": 95
                },
                "schedule_pattern": {
                    "cycle_length": 7,
                    "description": "Continuous 24/7 patient care coverage",
                    "type": "weekly"
                },
                "shifts": [
                    {
                        "days_of_week": [
                            1,
                            2,
                            3,
                            4,
                            5,
                            6,
                            7
                        ],
                        "roles_required": [
                            {
                                "minimum": 4,
                                "preferred": 5,
                                "role": "RN"
                            },
                            {
                                "minimum": 2,
                                "preferred": 3,
                                "role": "LPN"
                            }
                        ],
                        "shift_id": "REPLACE_WITH_ACTUAL_DAY_SHIFT_ID",
                        "staffing": {
                            "maximum_staff": 10,
                            "minimum_staff": 6,
                            "preferred_staff": 8
                        }
                    },
                    {
                        "days_of_week": [
                            1,
                            2,
                            3,
                            4,
                            5,
                            6,
                            7
                        ],
                        "roles_required": [
                            {
                                "minimum": 3,
                                "preferred": 4,
                                "role": "RN"
                            },
                            {
                                "minimum": 1,
                                "preferred": 1,
                                "role": "LPN"
                            }
                        ],
                        "shift_id": "REPLACE_WITH_ACTUAL_NIGHT_SHIFT_ID",
                        "staffing": {
                            "maximum_staff": 7,
                            "minimum_staff": 4,
                            "preferred_staff": 5
                        }
                    }
                ],
                "work_rules": {
                    "max_consecutive_days": 4,
                    "max_hours_per_week": 48,
                    "min_rest_days": 2,
                    "overtime_allowed": true,
                    "weekend_requirements": "at_least_one_off"
                }
            },
            "name": "Hospital ICU 24/7 Coverage",
            "pattern_type": "weekly"
        },
        "manufacturing_3_shift": {
            "business_requirements": {
                "schedule_pattern": {
                    "cycle_length": 21,
                    "type": "weekly"
                },
                "shifts": [
                    {
                        "days_of_week": [
                            1,
                            2,
                            3,
                            4,
                            5
                        ],
                        "shift_id": "REPLACE_WITH_ACTUAL_MORNING_SHIFT_ID",
                        "staffing": {
                            "maximum_staff": 20,
                            "minimum_staff": 15,
                            "preferred_staff": 18
                        }
                    },
                    {
                        "days_of_week": [
                            1,
                            2,
                            3,
                            4,
                            5
                        ],
                        "shift_id": "REPLACE_WITH_ACTUAL_AFTERNOON_SHIFT_ID",
                        "staffing": {
                            "maximum_staff": 18,
                            "minimum_staff": 12,
                            "preferred_staff": 15
                        }
                    },
                    {
                        "days_of_week": [
                            1,
                            2,
                            3,
                            4,
                            5
                        ],
                        "shift_id": "REPLACE_WITH_ACTUAL_NIGHT_SHIFT_ID",
                        "staffing": {
                            "maximum_staff": 12,
                            "minimum_staff": 8,
                            "preferred_staff": 10
                        }
                    }
                ],
                "work_rules": {
                    "max_consecutive_days": 5,
                    "max_hours_per_week": 40,
                    "min_rest_days": 2,
                    "overtime_allowed": true,
                    "weekend_requirements": "both_off"
                }
            },
            "name": "Manufacturing 3-Shift Rotation",
            "pattern_type": "weekly"
        },
        "office_customer_service": {
            "business_requirements": {
                "schedule_pattern": {
                    "cycle_length": 7,
                    "type": "weekly"
                },
                "shifts": [
                    {
                        "days_of_week": [
                            1,
                            2,
                            3,
                            4,
                            5
                        ],
                        "roles_required": [
                            {
                                "minimum": 6,
                                "preferred": 8,
                                "role": "Customer Service Rep"
                            },
                            {
                                "minimum": 1,
                                "preferred": 2,
                                "role": "Team Lead"
                            }
                        ],
                        "shift_id": "REPLACE_WITH_ACTUAL_BUSINESS_HOURS_SHIFT_ID",
                        "staffing": {
                            "maximum_staff": 15,
                            "minimum_staff": 8,
                            "preferred_staff": 12
                        }
                    }
                ],
                "work_rules": {
                    "max_consecutive_days": 5,
                    "max_hours_per_week": 40,
                    "min_rest_days": 2,
                    "overtime_allowed": false,
                    "weekend_requirements": "both_off"
                }
            },
            "name": "Customer Service Team",
            "pattern_type": "weekly"
        }
    },
    "message": "Business requirements examples retrieved successfully",
    "usage": {
        "description": "Use these examples as templates for creating your own business requirements",
        "endpoint": "POST /api/scheduling/create-structured-template",
        "instructions": [
            "1. First get available shifts: GET /api/scheduling/available-shifts?company_id=xxx",
            "2. Replace REPLACE_WITH_ACTUAL_*_SHIFT_ID with actual shift IDs from step 1",
            "3. Validate requirements: POST /api/scheduling/validate-requirements",
            "4. Create template: POST /api/scheduling/create-structured-template"
        ],
        "shifts_endpoint": "GET /api/scheduling/available-shifts",
        "validation_endpoint": "POST /api/scheduling/validate-requirements"
    }
}

create a structured scheduling template[POST]:
 - weekly schedule for a company: https://sms.remmittance.com/api/scheduling/create-structured-template
    sample request body: {
    "company_id": "e7d11f4e-3112-4e39-8619-5c7aaeac6554",
    "name": "Standard Morning Schedule Template",
    "description": "Template for standard morning shift coverage",
    "pattern_type": "weekly",
    "business_requirements": {
        "schedule_pattern": {
        "type": "weekly",
        "cycle_length": 7
        },
        "shifts": [
        {
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "time_range": {
            "start_time": "09:30",
            "end_time": "17:30"
            },
            "staffing": {
            "minimum_staff": 2,
            "preferred_staff": 4,
            "maximum_staff": 6
            },
            "days_of_week": [1, 2, 3, 4, 5],
            "break_duration": 45,
            "overtime_eligible": true
        }
        ],
        "work_rules": {
        "max_consecutive_days": 5,
        "min_rest_days": 2,
        "max_hours_per_week": 40,
        "max_hours_per_day": 8,
        "overtime_allowed": true,
        "weekend_requirements": "flexible"
        }
    }
    }
    sample response: {
        "message": "Structured shift template created successfully",
        "template": {
            "business_requirements": {
                "coverage_rules": {
                    "allow_understaffing": false,
                    "emergency_coverage_plan": "call_in_overtime",
                    "minimum_coverage_percentage": 90
                },
                "employee_preferences": {
                    "consider_employee_requests": true,
                    "fair_rotation": true,
                    "seniority_priority": false
                },
                "schedule_pattern": {
                    "cycle_length": 7,
                    "type": "weekly"
                },
                "shifts": [
                    {
                        "break_duration": 45,
                        "days_of_week": [
                            1,
                            2,
                            3,
                            4,
                            5
                        ],
                        "overtime_eligible": true,
                        "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
                        "staffing": {
                            "maximum_staff": 6,
                            "minimum_staff": 2,
                            "preferred_staff": 4
                        },
                        "time_range": {
                            "end_time": "17:30",
                            "start_time": "09:30"
                        }
                    }
                ],
                "work_rules": {
                    "max_consecutive_days": 5,
                    "max_hours_per_day": 8,
                    "max_hours_per_week": 40,
                    "min_rest_days": 2,
                    "overtime_allowed": true,
                    "weekend_requirements": "flexible"
                }
            },
            "created_at": "2025-08-16 11:34:05",
            "description": "Template for standard morning shift coverage",
            "name": "Standard Morning Schedule Template",
            "pattern_type": "weekly",
            "staffing_summary": {
                "daily_totals": {
                    "1": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "2": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "3": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "4": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "5": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "6": {
                        "max_staff": 0,
                        "min_staff": 0,
                        "preferred_staff": 0
                    },
                    "7": {
                        "max_staff": 0,
                        "min_staff": 0,
                        "preferred_staff": 0
                    }
                },
                "shift_details": [
                    {
                        "days_count": 5,
                        "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
                        "staffing": {
                            "maximum_staff": 6,
                            "minimum_staff": 2,
                            "preferred_staff": 4
                        }
                    }
                ],
                "total_shifts": 1,
                "weekly_totals": {
                    "max_staff_hours": 240,
                    "min_staff_hours": 80,
                    "preferred_staff_hours": 160
                }
            },
            "template_id": "cc657336-1f24-47da-8669-1789692ace05"
        }
    }
- weekly department schedule: 

avalable scheduling templates: https://sms.remmittance.com/api/scheduling/templates/business-friendly?company_id={{company_id}}
sample response: {
    "count": 3,
    "message": "Business-friendly templates retrieved successfully",
    "templates": [
        {
            "created_at": "2025-08-16 11:36:45",
            "description": "24/7 critical care with extended hours and high staffing",
            "has_business_requirements": true,
            "is_24_7_coverage": false,
            "is_active": true,
            "name": "ICU Intensive Care Template",
            "pattern_type": "weekly",
            "staffing_summary": {
                "daily_totals": {
                    "1": {
                        "max_staff": 15,
                        "min_staff": 8,
                        "preferred_staff": 12
                    },
                    "2": {
                        "max_staff": 15,
                        "min_staff": 8,
                        "preferred_staff": 12
                    },
                    "3": {
                        "max_staff": 15,
                        "min_staff": 8,
                        "preferred_staff": 12
                    },
                    "4": {
                        "max_staff": 15,
                        "min_staff": 8,
                        "preferred_staff": 12
                    },
                    "5": {
                        "max_staff": 15,
                        "min_staff": 8,
                        "preferred_staff": 12
                    },
                    "6": {
                        "max_staff": 15,
                        "min_staff": 8,
                        "preferred_staff": 12
                    },
                    "7": {
                        "max_staff": 15,
                        "min_staff": 8,
                        "preferred_staff": 12
                    }
                },
                "shift_details": [
                    {
                        "days_count": 7,
                        "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
                        "staffing": {
                            "maximum_staff": 15,
                            "minimum_staff": 8,
                            "preferred_staff": 12
                        }
                    }
                ],
                "total_shifts": 1,
                "weekly_totals": {
                    "max_staff_hours": 840,
                    "min_staff_hours": 448,
                    "preferred_staff_hours": 672
                }
            },
            "template_id": "58dbb315-aa89-4c9f-85a7-83a081c884ab",
            "weekly_coverage_hours": 1008,
            "work_rules_summary": {
                "max_consecutive_days": 4,
                "max_hours_per_day": 12,
                "max_hours_per_week": 48,
                "min_rest_days": 2,
                "overtime_allowed": true,
                "weekend_requirements": "flexible"
            }
        },
        {
            "created_at": "2025-08-16 10:32:42",
            "description": "Template for standard morning shift coverage",
            "has_business_requirements": true,
            "is_24_7_coverage": false,
            "is_active": true,
            "name": "Standard Morning Schedule Template",
            "pattern_type": "weekly",
            "staffing_summary": {
                "daily_totals": {
                    "1": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "2": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "3": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "4": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "5": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "6": {
                        "max_staff": 0,
                        "min_staff": 0,
                        "preferred_staff": 0
                    },
                    "7": {
                        "max_staff": 0,
                        "min_staff": 0,
                        "preferred_staff": 0
                    }
                },
                "shift_details": [
                    {
                        "days_count": 5,
                        "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
                        "staffing": {
                            "maximum_staff": 6,
                            "minimum_staff": 2,
                            "preferred_staff": 4
                        }
                    }
                ],
                "total_shifts": 1,
                "weekly_totals": {
                    "max_staff_hours": 240,
                    "min_staff_hours": 80,
                    "preferred_staff_hours": 160
                }
            },
            "template_id": "36e1d1c5-f423-4d03-8dc7-73dd0ae7f554",
            "weekly_coverage_hours": 160,
            "work_rules_summary": {
                "max_consecutive_days": 5,
                "max_hours_per_day": 8,
                "max_hours_per_week": 40,
                "min_rest_days": 2,
                "overtime_allowed": true,
                "weekend_requirements": "flexible"
            }
        },
        {
            "created_at": "2025-08-16 11:34:05",
            "description": "Template for standard morning shift coverage",
            "has_business_requirements": true,
            "is_24_7_coverage": false,
            "is_active": true,
            "name": "Standard Morning Schedule Template",
            "pattern_type": "weekly",
            "staffing_summary": {
                "daily_totals": {
                    "1": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "2": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "3": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "4": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "5": {
                        "max_staff": 6,
                        "min_staff": 2,
                        "preferred_staff": 4
                    },
                    "6": {
                        "max_staff": 0,
                        "min_staff": 0,
                        "preferred_staff": 0
                    },
                    "7": {
                        "max_staff": 0,
                        "min_staff": 0,
                        "preferred_staff": 0
                    }
                },
                "shift_details": [
                    {
                        "days_count": 5,
                        "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
                        "staffing": {
                            "maximum_staff": 6,
                            "minimum_staff": 2,
                            "preferred_staff": 4
                        }
                    }
                ],
                "total_shifts": 1,
                "weekly_totals": {
                    "max_staff_hours": 240,
                    "min_staff_hours": 80,
                    "preferred_staff_hours": 160
                }
            },
            "template_id": "cc657336-1f24-47da-8669-1789692ace05",
            "weekly_coverage_hours": 160,
            "work_rules_summary": {
                "max_consecutive_days": 5,
                "max_hours_per_day": 8,
                "max_hours_per_week": 40,
                "min_rest_days": 2,
                "overtime_allowed": true,
                "weekend_requirements": "flexible"
            }
        }
    ]
}

analyse a template[GET]: https://sms.remmittance.com/api/scheduling/template-analysis/58dbb315-aa89-4c9f-85a7-83a081c884ab?company_id={{company_id}}
sample response: {
    "analysis": {
        "business_requirements": {
            "coverage_rules": {
                "allow_understaffing": false,
                "emergency_coverage_plan": "call_in_overtime",
                "minimum_coverage_percentage": 90
            },
            "employee_preferences": {
                "consider_employee_requests": true,
                "fair_rotation": true,
                "seniority_priority": false
            },
            "schedule_pattern": {
                "cycle_length": 7,
                "type": "weekly"
            },
            "shifts": [
                {
                    "break_duration": 45,
                    "break_start_time": "13:00",
                    "days_of_week": [
                        1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        7
                    ],
                    "overtime_eligible": true,
                    "shift_details": {
                        "description": "Standard morning shift from 8 AM to 5 PM",
                        "end_time": "17:30",
                        "is_night_shift": false,
                        "name": "Standard morning shift",
                        "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
                        "start_time": "09:30",
                        "working_days": "1,2,3,4,5"
                    },
                    "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
                    "staffing": {
                        "maximum_staff": 15,
                        "minimum_staff": 8,
                        "preferred_staff": 12
                    },
                    "time_range": {
                        "end_time": "19:00",
                        "start_time": "07:00"
                    }
                }
            ],
            "work_rules": {
                "max_consecutive_days": 4,
                "max_hours_per_day": 12,
                "max_hours_per_week": 48,
                "min_rest_days": 2,
                "minimum_turnaround_hours": 8,
                "overtime_allowed": true,
                "weekend_requirements": "flexible"
            }
        },
        "daily_staff_totals": {},
        "is_24_7_coverage": false,
        "staffing_summary": {
            "daily_totals": {
                "1": {
                    "max_staff": 15,
                    "min_staff": 8,
                    "preferred_staff": 12
                },
                "2": {
                    "max_staff": 15,
                    "min_staff": 8,
                    "preferred_staff": 12
                },
                "3": {
                    "max_staff": 15,
                    "min_staff": 8,
                    "preferred_staff": 12
                },
                "4": {
                    "max_staff": 15,
                    "min_staff": 8,
                    "preferred_staff": 12
                },
                "5": {
                    "max_staff": 15,
                    "min_staff": 8,
                    "preferred_staff": 12
                },
                "6": {
                    "max_staff": 15,
                    "min_staff": 8,
                    "preferred_staff": 12
                },
                "7": {
                    "max_staff": 15,
                    "min_staff": 8,
                    "preferred_staff": 12
                }
            },
            "shift_details": [
                {
                    "days_count": 7,
                    "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
                    "staffing": {
                        "maximum_staff": 15,
                        "minimum_staff": 8,
                        "preferred_staff": 12
                    }
                }
            ],
            "total_shifts": 1,
            "weekly_totals": {
                "max_staff_hours": 840,
                "min_staff_hours": 448,
                "preferred_staff_hours": 672
            }
        },
        "template_info": {
            "created_at": "2025-08-16 11:36:45",
            "description": "24/7 critical care with extended hours and high staffing",
            "name": "ICU Intensive Care Template",
            "pattern_type": "weekly",
            "template_id": "58dbb315-aa89-4c9f-85a7-83a081c884ab"
        },
        "validation_status": [
            true,
            "Valid business requirements"
        ],
        "weekly_coverage_hours": 1008,
        "work_rules_summary": {
            "max_consecutive_days": 4,
            "max_hours_per_day": 12,
            "max_hours_per_week": 48,
            "min_rest_days": 2,
            "overtime_allowed": true,
            "weekend_requirements": "flexible"
        }
    },
    "message": "Template analysis retrieved successfully"
}
Generate Schedule[POST]: https://sms.remmittance.com/api/scheduling/generate-schedule
request body: {
  "company_id": "{{company_id}}",
  "template_id": "{{template_id}}",
  "start_date": "2025-08-25",
  "end_date": "2025-10-28",
  "schedule_name": "August-October 2025 schedule"
}
sample response: {
    "message": "Schedule generated successfully",
    "schedule": {
        "created_at": "2025-08-25 20:28:40",
        "end_date": "2025-10-28",
        "schedule_id": "ed11d428-9c84-4281-9979-777d359255db",
        "schedule_name": "August-October 2025 schedule",
        "start_date": "2025-08-25",
        "status": "draft",
        "template_id": "58dbb315-aa89-4c9f-85a7-83a081c884ab"
    }
}
asign to employees[POST]: https://sms.remmittance.com/api/scheduling/assign-schedule
request body: {
  "company_id": "{{company_id}}",
  "schedule_id": "ed11d428-9c84-4281-9979-777d359255db",
  "assignment_type": "employee",
  "target_ids": [
    "55b6ea35-5527-4909-acc3-38a5a5d67897",
    "4504dff8-8ee5-43f3-95c8-127891508607",
    "e2527899-de7a-430d-9e21-8566416cf2ab",
    "19bf3782-34fc-4f21-890c-198c173823d4"
  ],
  "assignment_strategy": "automatic"
}
sample response: {
    "assignments": [
        {
            "assignment_id": "e4130546-3963-44f4-bf13-0daf996cddb2",
            "employee_id": "55b6ea35-5527-4909-acc3-38a5a5d67897",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        },
        {
            "assignment_id": "d96b8ed2-d9f1-4f20-93ca-1cecc4ba97e5",
            "employee_id": "4504dff8-8ee5-43f3-95c8-127891508607",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        },
        {
            "assignment_id": "d8ace5ea-aab8-4658-b4e0-d6accf17e6d5",
            "employee_id": "e2527899-de7a-430d-9e21-8566416cf2ab",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        },
        {
            "assignment_id": "b9e92ebf-6d1c-4c9c-9854-72666f89742d",
            "employee_id": "19bf3782-34fc-4f21-890c-198c173823d4",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        }
    ],
    "assignments_created": 4,
    "errors": [],
    "message": "Schedule assignment completed",
    "schedule_id": "ed11d428-9c84-4281-9979-777d359255db",
    "summary": {
        "assignment_strategy": "automatic",
        "assignment_type": "employee",
        "failed_assignments": 0,
        "total_assignments": 4
    }
}
asign to schedule to all employees in the department[POST]:https://sms.remmittance.com/api/scheduling/assign-schedule
request body: {
  "company_id": "{{company_id}}",
  "schedule_id": "ed11d428-9c84-4281-9979-777d359255db",
  "assignment_type": "department",
  "target_ids": [
    "d8475d75-6d23-4b1b-ab6d-105d3694af37"],
  "assignment_strategy": "automatic"
}
sample reponse: {
    "assignments": [
        {
            "assignment_id": "c87d0da4-2b1e-4b1c-8dfd-7bb19d63bd67",
            "employee_id": "64cf70b0-d9eb-4253-a8f9-61526a9965a7",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        },
        {
            "assignment_id": "c2308d12-f5ea-4ec3-88e0-c737e1a2c66c",
            "employee_id": "4e138227-fb55-48c6-ad32-afa52e8543ca",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        },
        {
            "assignment_id": "af9a0188-b9ad-4182-b5ab-2c8bf6d472ef",
            "employee_id": "841298f3-949a-45d0-8e71-c2450f58f959",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        },
        {
            "assignment_id": "7bb19737-ef0e-4752-8852-d200a8ccc5a9",
            "employee_id": "8f98adce-85ea-402a-8ce3-bcdd228943a4",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        },
        {
            "assignment_id": "321e023d-398b-4e95-a712-31a865a40c6f",
            "employee_id": "9cc0564d-9c90-4b36-bb6c-329022c0fe3d",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        },
        {
            "assignment_id": "48907888-6922-4160-9079-e70d6fc33c2b",
            "employee_id": "4504dff8-8ee5-43f3-95c8-127891508607",
            "end_date": "2025-10-28",
            "shift_id": "0befa062-0b58-4347-bbb6-1ac4fa44ab6b",
            "start_date": "2025-08-25"
        }
    ],
    "assignments_created": 6,
    "errors": [],
    "message": "Schedule assignment completed",
    "schedule_id": "ed11d428-9c84-4281-9979-777d359255db",
    "summary": {
        "assignment_strategy": "automatic",
        "assignment_type": "department",
        "failed_assignments": 0,
        "total_assignments": 6
    }
}

