export interface Shift {
  shift_id: string;
  company_id: string;
  name: string;
  description?: string;
  start_time: string; // Format: "HH:MM" (24-hour)
  end_time: string; // Format: "HH:MM" (24-hour)
  grace_period_late?: number; // Minutes
  grace_period_early?: number; // Minutes
  break_duration?: number; // Minutes
  break_start_time?: string; // Format: "HH:MM" (24-hour)
  is_night_shift?: boolean;
  is_flexible?: boolean;
  working_days?: string; // Comma-separated list of day numbers (1-7, where 1 is Monday)
  created_at?: string;
  updated_at?: string;
}

export interface ShiftResponse {
  code?: number;
  extend?: {
    shifts: Shift[];
    pagination: {
      has_next: boolean;
      has_prev: boolean;
      page: number;
      pages: number;
      per_page: number;
      total_count: number;
    };
  };
  shifts?: Shift[]; // Fallback for direct structure
  pagination?: {
    has_next: boolean;
    has_prev: boolean;
    page: number;
    pages: number;
    per_page: number;
    total_count: number;
  };
  success?: boolean;
  msg?: string;
}

export interface ShiftDetailResponse {
  code?: number;
  extend?: {
    shift: Shift;
  };
  shift?: Shift; // Fallback
  success?: boolean;
  msg?: string;
}

export interface ShiftCreateResponse {
  code?: number;
  extend?: {
    shift: Shift;
  };
  shift?: Shift; // Fallback
  success?: boolean;
  msg?: string;
}

export interface ShiftUpdateResponse {
  code?: number;
  extend?: {
    shift: Shift;
  };
  shift?: Shift; // Fallback
  success?: boolean;
  msg?: string;
}

export interface ShiftDeleteResponse {
  code?: number;
  success?: boolean;
  msg?: string;
}

// Helper function to format time (HH:MM) for display
export const formatTime = (time: string): string => {
  if (!time) return '';

  // Parse the time string (HH:MM)
  const [hours, minutes] = time.split(':').map(Number);

  // Convert to 12-hour format
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

  // Format the time
  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
};

// Helper function to format working days
export const formatWorkingDays = (workingDays: string | null | undefined): string => {
  // Handle null, undefined, or empty string
  if (!workingDays || workingDays.trim() === '') {
    return 'No working days specified';
  }

  try {
    const days = workingDays.split(',')
      .map(day => day.trim()) // Trim whitespace
      .filter(day => day !== '') // Remove empty strings
      .map(Number)
      .filter(day => !isNaN(day)); // Remove invalid numbers

    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    // Map day numbers to day names
    const formattedDays = days
      .map(day => dayNames[day - 1])
      .filter(dayName => dayName !== undefined); // Remove undefined day names

    return formattedDays.length > 0 ? formattedDays.join(', ') : 'No valid working days';
  } catch (error) {
    console.warn('Error formatting working days:', error);
    return 'Invalid working days format';
  }
};

// Helper function to get short day names for display
export const getShortDayNames = (workingDays: string | null | undefined): { name: string; active: boolean }[] => {
  let days: number[] = [];

  // Handle null, undefined, or empty string
  if (workingDays && workingDays.trim() !== '') {
    try {
      days = workingDays.split(',')
        .map(day => day.trim()) // Trim whitespace
        .filter(day => day !== '') // Remove empty strings
        .map(Number)
        .filter(day => !isNaN(day)); // Remove invalid numbers
    } catch (error) {
      console.warn('Error parsing working days:', error);
      days = [];
    }
  }

  const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  return dayNames.map((name, index) => ({
    name,
    active: days.includes(index + 1)
  }));
};

// Helper function to calculate shift duration
export const calculateShiftDuration = (startTime: string, endTime: string, breakDuration: number = 0): string => {
  if (!startTime || !endTime) return '';

  // Parse the time strings
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);

  // Calculate total minutes
  let totalMinutes = (endHours * 60 + endMinutes) - (startHours * 60 + startMinutes);

  // Handle overnight shifts
  if (totalMinutes <= 0) {
    totalMinutes += 24 * 60; // Add 24 hours in minutes
  }

  // Subtract break duration
  totalMinutes -= breakDuration;

  // Convert to hours and minutes
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  return `${hours}h ${minutes}m`;
};
