// Advanced Shift Management Types
// Based on the API specifications in advanced_shift.md

export interface CoverageRules {
  allow_understaffing: boolean;
  emergency_coverage_plan: string;
  minimum_coverage_percentage: number;
}

export interface EmployeePreferences {
  consider_employee_requests: boolean;
  fair_rotation: boolean;
  seniority_priority: boolean;
}

export interface SchedulePattern {
  type: 'weekly' | 'monthly' | 'custom';
  cycle_length: number;
  description?: string;
}

export interface RoleRequirement {
  role: string;
  minimum: number;
  preferred: number;
}

export interface Staffing {
  minimum_staff: number;
  preferred_staff: number;
  maximum_staff: number;
}

export interface TimeRange {
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
}

export interface ShiftRequirement {
  shift_id: string;
  days_of_week: number[]; // 1-7, where 1 is Monday
  staffing: Staffing;
  time_range?: TimeRange;
  break_duration?: number;
  break_start_time?: string;
  overtime_eligible?: boolean;
  roles_required?: RoleRequirement[];
}

export interface WorkRules {
  max_consecutive_days: number;
  min_rest_days: number;
  max_hours_per_week: number;
  max_hours_per_day?: number;
  overtime_allowed: boolean;
  weekend_requirements: 'flexible' | 'both_off' | 'at_least_one_off' | 'both_required';
  minimum_turnaround_hours?: number;
}

export interface BusinessRequirements {
  schedule_pattern: SchedulePattern;
  shifts: ShiftRequirement[];
  work_rules: WorkRules;
  coverage_rules?: CoverageRules;
  employee_preferences?: EmployeePreferences;
}

export interface SchedulingTemplate {
  template_id: string;
  company_id?: string;
  name: string;
  description: string;
  pattern_type: 'weekly' | 'monthly' | 'custom';
  business_requirements: BusinessRequirements;
  created_at: string;
  updated_at?: string;
  is_active?: boolean;
  has_business_requirements?: boolean;
  is_24_7_coverage?: boolean;
}

export interface DailyStaffTotal {
  min_staff: number;
  max_staff: number;
  preferred_staff: number;
}

export interface ShiftDetail {
  shift_id: string;
  days_count: number;
  staffing: Staffing;
}

export interface WeeklyTotals {
  min_staff_hours: number;
  max_staff_hours: number;
  preferred_staff_hours: number;
}

export interface StaffingSummary {
  daily_totals: { [key: string]: DailyStaffTotal }; // "1" to "7" for days of week
  shift_details: ShiftDetail[];
  total_shifts: number;
  weekly_totals: WeeklyTotals;
}

export interface WorkRulesSummary {
  max_consecutive_days: number;
  max_hours_per_day?: number;
  max_hours_per_week: number;
  min_rest_days: number;
  overtime_allowed: boolean;
  weekend_requirements: string;
}

export interface TemplateWithSummary extends SchedulingTemplate {
  staffing_summary: StaffingSummary;
  work_rules_summary: WorkRulesSummary;
  weekly_coverage_hours: number;
}

// Template Examples Response
export interface TemplateExample {
  name: string;
  pattern_type: string;
  business_requirements: BusinessRequirements;
}

export interface TemplateExamplesResponse {
  examples: { [key: string]: TemplateExample };
  message: string;
  usage: {
    description: string;
    endpoint: string;
    instructions: string[];
    shifts_endpoint: string;
    validation_endpoint: string;
  };
}

// Available Templates Response
export interface AvailableTemplatesResponse {
  count: number;
  message: string;
  templates: TemplateWithSummary[];
}

// Template Analysis Response
export interface ShiftDetails {
  description: string;
  end_time: string;
  is_night_shift: boolean;
  name: string;
  shift_id: string;
  start_time: string;
  working_days: string;
}

export interface AnalyzedShiftRequirement extends ShiftRequirement {
  shift_details: ShiftDetails;
}

export interface AnalyzedBusinessRequirements extends BusinessRequirements {
  shifts: AnalyzedShiftRequirement[];
}

export interface TemplateAnalysis {
  business_requirements: AnalyzedBusinessRequirements;
  daily_staff_totals: { [key: string]: any };
  is_24_7_coverage: boolean;
  staffing_summary: StaffingSummary;
  template_info: {
    created_at: string;
    description: string;
    name: string;
    pattern_type: string;
    template_id: string;
  };
  validation_status: [boolean, string];
  weekly_coverage_hours: number;
  work_rules_summary: WorkRulesSummary;
}

export interface TemplateAnalysisResponse {
  analysis: TemplateAnalysis;
  message: string;
}

// Schedule Generation
export interface GenerateScheduleRequest {
  company_id: string;
  template_id: string;
  start_date: string; // YYYY-MM-DD
  end_date: string; // YYYY-MM-DD
  schedule_name: string;
}

export interface GeneratedSchedule {
  schedule_id: string;
  schedule_name: string;
  start_date: string;
  end_date: string;
  status: 'draft' | 'active' | 'completed';
  template_id: string;
  created_at: string;
}

export interface GenerateScheduleResponse {
  message: string;
  schedule: GeneratedSchedule;
}

// Schedule Assignment
export interface AssignScheduleRequest {
  company_id: string;
  schedule_id: string;
  assignment_type: 'employee' | 'department';
  target_ids: string[]; // employee_ids or department_ids
  assignment_strategy: 'automatic' | 'manual';
}

export interface ScheduleAssignment {
  assignment_id: string;
  employee_id: string;
  shift_id: string;
  start_date: string;
  end_date: string;
}

export interface AssignmentSummary {
  assignment_strategy: string;
  assignment_type: string;
  failed_assignments: number;
  total_assignments: number;
}

export interface AssignScheduleResponse {
  assignments: ScheduleAssignment[];
  assignments_created: number;
  errors: any[];
  message: string;
  schedule_id: string;
  summary: AssignmentSummary;
}

// Available Shifts for Template Creation
export interface AvailableShift {
  shift_id: string;
  name: string;
  description?: string;
  start_time: string;
  end_time: string;
  is_night_shift: boolean;
  is_flexible: boolean;
  working_days: string;
}

export interface AvailableShiftsResponse {
  shifts: AvailableShift[];
  message: string;
}

// Template Creation Request
export interface CreateTemplateRequest {
  company_id: string;
  name: string;
  description: string;
  pattern_type: 'weekly' | 'monthly' | 'custom';
  business_requirements: BusinessRequirements;
}

export interface CreateTemplateResponse {
  message: string;
  template: TemplateWithSummary;
}

// Validation Request
export interface ValidateRequirementsRequest {
  company_id: string;
  business_requirements: BusinessRequirements;
}

export interface ValidateRequirementsResponse {
  valid: boolean;
  message: string;
  errors?: string[];
  warnings?: string[];
}
