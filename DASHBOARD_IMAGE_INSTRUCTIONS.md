# How to Add Dashboard Preview Image

## Steps to Add Your Dashboard Image

1. **Prepare Your Image**
   - Take a screenshot of your KaziSync dashboard
   - Recommended size: 1200x675 pixels (16:9 aspect ratio)
   - Format: PNG or JPG
   - Optimize for web (keep file size under 500KB)

2. **Add Image to Project**
   - Create a folder: `/public/images/` (if it doesn't exist)
   - Save your image as: `/public/images/dashboard-preview.png`

3. **Update the Hero Component**
   - Open `components/Hero.tsx`
   - Find the placeholder section (around line 50-60)
   - Replace the placeholder `<div>` with:

```jsx
<img 
  src="/images/dashboard-preview.png" 
  alt="KaziSync Dashboard Preview" 
  className="w-full h-full object-cover rounded-lg" 
/>
```

4. **Remove the Placeholder**
   - Delete the entire placeholder div with the SVG icon and text
   - Keep only the image tag

## Example of Final Code

```jsx
<div className="aspect-w-16 aspect-h-9 bg-background-dark rounded-lg flex items-center justify-center overflow-hidden">
  <img 
    src="/images/dashboard-preview.png" 
    alt="KaziSync Dashboard Preview" 
    className="w-full h-full object-cover rounded-lg" 
  />
</div>
```

## Alternative: Multiple Images

If you want to show multiple dashboard views, you can create a simple carousel or use multiple images:

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <img src="/images/dashboard-hr.png" alt="HR Dashboard" className="rounded-lg" />
  <img src="/images/dashboard-employee.png" alt="Employee Dashboard" className="rounded-lg" />
</div>
```

## Tips for Best Results

- Use high-quality screenshots
- Show the most impressive/feature-rich parts of your dashboard
- Consider adding subtle animations or hover effects
- Ensure the image loads quickly
- Test on different screen sizes

The placeholder is currently set up to guide you on where to place your image. Once you add your dashboard screenshot, it will automatically display in the hero section of your landing page.
